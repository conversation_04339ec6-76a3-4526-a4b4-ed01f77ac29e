# SACRA2 Frontend

A modern React frontend for the SACRA2 AI model evaluation platform.

## Tech Stack

- **Framework**: React 18 + TypeScript 5
- **Build Tool**: Vite 5
- **UI Library**: Ant Design 5 + Ant Design Pro Components
- **State Management**: 
  - React Query 4 (server state)
  - <PERSON>ustand (UI state)
- **Routing**: React Router 6
- **HTTP Client**: Axios with interceptors
- **Authentication**: JWT (access token in memory, refresh token in httpOnly cookies)
- **Testing**: Vitest + Testing Library
- **Code Quality**: ESLint + Prettier + Husky

## Getting Started

### Prerequisites

- Node.js >= 18
- pnpm >= 8

### Installation

```bash
# Install dependencies
pnpm install

# Copy environment template
cp .env.example .env

# Start development server
pnpm dev
```

### Available Scripts

```bash
# Development
pnpm dev          # Start dev server
pnpm build        # Production build
pnpm preview      # Preview production build

# Code Quality
pnpm lint         # Run ESLint
pnpm lint:fix     # Fix ESLint errors
pnpm format       # Format with Prettier
pnpm type-check   # TypeScript type checking

# Testing
pnpm test         # Run tests
pnpm test:ui      # Run tests with UI
pnpm test:coverage # Coverage report
```

## Architecture

### Directory Structure

```
src/
├── api/           # API client and React Query hooks
├── auth/          # Authentication context and utilities
├── components/    # Reusable UI components
├── pages/         # Page components (lazy loaded)
├── stores/        # Zustand stores for UI state
├── types/         # TypeScript type definitions
├── hooks/         # Custom React hooks
└── utils/         # Utility functions
```

### Key Features

- **Enterprise UI**: Professional admin interface using Ant Design Pro
- **Real-time Updates**: Polling for evaluation status updates
- **Responsive Design**: Mobile-first responsive layout
- **Type Safety**: Strict TypeScript configuration
- **Performance**: Lazy loading, code splitting, caching
- **Security**: Secure JWT token handling
- **Accessibility**: WCAG 2.1 AA compliant components

### State Management

- **Server State**: React Query for API data, caching, and synchronization
- **UI State**: Zustand for app-wide UI preferences and temporary state
- **Authentication**: React Context for user session management

### Authentication Flow

1. User logs in with email/password
2. Server returns access token (stored in memory) and sets refresh token in httpOnly cookie
3. API client automatically attaches access token to requests
4. On token expiry, automatic refresh using httpOnly cookie
5. Automatic logout and redirect to login on refresh failure

## Development Guidelines

### Component Patterns

- Use ProComponents (ProTable, ProForm, ProLayout) for consistent enterprise UI
- Separate container and presentation components
- Use React Query for all server state management
- Implement proper loading and error states

### Code Style

- Follow ESLint and Prettier configurations
- Use TypeScript strict mode
- Write descriptive component and function names
- Add JSDoc comments for complex logic

### Testing

- Write unit tests for utility functions and hooks
- Test component behavior, not implementation details
- Use MSW for API mocking in tests

## Deployment

### Environment Variables

Copy `.env.example` to `.env` and configure:

- `VITE_API_BASE_URL`: Backend API URL
- `VITE_AUTH_COOKIE_NAME`: Refresh token cookie name

### Production Build

```bash
# Build for production
pnpm build

# Preview production build locally
pnpm preview
```

## Contributing

1. Follow the existing code style and patterns
2. Write tests for new features
3. Update documentation as needed
4. Ensure all linting and type checking passes

## License

Private - SACRA2 Project