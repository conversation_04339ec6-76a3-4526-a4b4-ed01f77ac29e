{"name": "sacra2-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "SACRA2 Frontend - AI Model Evaluation Platform", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --fix", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "prepare": "echo 'Skipping husky install'"}, "dependencies": {"@ant-design/charts": "^1.4.2", "@ant-design/icons": "^5.2.6", "@ant-design/plots": "^1.2.5", "@ant-design/pro-components": "^2.6.48", "@tanstack/react-query": "^4.36.1", "@tanstack/react-query-devtools": "^4.36.1", "@xyflow/react": "^12.3.0", "antd": "^5.12.8", "axios": "^1.6.2", "classnames": "^2.3.2", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "lucide-react": "^0.539.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-router-dom": "^6.20.1", "styled-components": "^6.1.6", "typescript": "^5.3.3", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.12", "@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.4", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.21", "eslint": "^9.15.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "husky": "^8.0.3", "jsdom": "^23.0.1", "lint-staged": "^15.2.0", "msw": "^2.0.11", "postcss": "^8.5.6", "prettier": "^3.1.1", "tailwindcss": "^4.1.12", "vite": "^5.0.8", "vitest": "^1.0.4"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.12.1", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{js,jsx,json,css,scss,md}": ["prettier --write"]}}