import { BaseEntity } from './index'

export interface Evaluation extends BaseEntity {
  name: string
  description?: string
  status: EvaluationStatus
  projectId: string
  assessmentId: string
  modelConfig: ModelConfig
  results: EvaluationResults
  metrics: EvaluationMetrics
  startedAt?: string
  completedAt?: string
  errorMessage?: string
}

export type EvaluationStatus =
  | 'pending'
  | 'queued'
  | 'running'
  | 'completed'
  | 'failed'
  | 'cancelled'

export interface ModelConfig {
  provider: string
  model: string
  temperature: number
  maxTokens: number
  topP: number
  frequencyPenalty: number
  presencePenalty: number
  systemPrompt?: string
  customConfig?: Record<string, unknown>
}

export interface EvaluationResults {
  overallScore: number
  capabilityScores: CapabilityScore[]
  probeResults: ProbeResult[]
  summary: ResultSummary
}

export interface CapabilityScore {
  capabilityId: string
  capabilityName: string
  score: number
  weight: number
  probesetResults: ProbesetResult[]
}

export interface ProbesetResult {
  probesetId: string
  probesetName: string
  score: number
  weight: number
  probeResults: ProbeResult[]
}

export interface ProbeResult {
  probeId: string
  probeName: string
  score: number
  passed: boolean
  response?: string
  reasoning?: string
  executionTimeMs: number
  tokenUsage: TokenUsage
  cached: boolean
}

export interface ResultSummary {
  totalProbes: number
  passedProbes: number
  failedProbes: number
  averageScore: number
  totalTokensUsed: number
  totalCostUsd: number
  executionTimeMs: number
  cacheHitRate: number
}

export interface TokenUsage {
  promptTokens: number
  completionTokens: number
  totalTokens: number
  costUsd: number
}

export interface EvaluationMetrics {
  performance: PerformanceMetrics
  quality: QualityMetrics
  cost: CostMetrics
}

export interface PerformanceMetrics {
  averageResponseTime: number
  p95ResponseTime: number
  throughputPerSecond: number
  errorRate: number
}

export interface QualityMetrics {
  accuracyScore: number
  consistencyScore: number
  relevanceScore: number
  safetyScore: number
}

export interface CostMetrics {
  totalCostUsd: number
  costPerEvaluation: number
  tokenEfficiency: number
  cacheHitRate: number
}

export interface EvaluationCreateRequest {
  name: string
  description?: string
  assessmentId: string
  modelConfig: ModelConfig
}

export interface EvaluationUpdateRequest extends Partial<EvaluationCreateRequest> {
  status?: EvaluationStatus
}