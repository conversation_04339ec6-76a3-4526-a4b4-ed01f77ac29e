import { BaseEntity } from './index'

export interface ModelProvider extends BaseEntity {
  name: string
  displayName: string
  description?: string
  status: ProviderStatus
  apiConfig: ProviderApiConfig
  models: Model[]
  features: ProviderFeatures
  rateLimits: RateLimits
}

export type ProviderStatus = 'active' | 'deprecated' | 'maintenance'

export interface ProviderApiConfig {
  baseUrl: string
  authType: 'api_key' | 'oauth' | 'bearer_token'
  requiredCredentials: string[]
  defaultHeaders?: Record<string, string>
  timeout: number
}

export interface ProviderFeatures {
  supportsStreaming: boolean
  supportsTools: boolean
  supportsVision: boolean
  supportsAudio: boolean
  maxContextLength: number
}

export interface RateLimits {
  requestsPerMinute: number
  requestsPerHour: number
  tokensPerMinute: number
  tokensPerDay: number
}

export interface Model extends BaseEntity {
  name: string
  displayName: string
  description?: string
  providerId: string
  status: ModelStatus
  capabilities: ModelCapabilities
  pricing: ModelPricing
  limits: ModelLimits
  metadata: ModelMetadata
}

export type ModelStatus = 'available' | 'deprecated' | 'beta' | 'coming_soon'

export interface ModelCapabilities {
  inputTypes: ('text' | 'image' | 'audio')[]
  outputTypes: ('text' | 'image' | 'audio')[]
  supportsStreaming: boolean
  supportsTools: boolean
  supportsSystemPrompt: boolean
  contextLength: number
}

export interface ModelPricing {
  inputTokenPriceUsd: number
  outputTokenPriceUsd: number
  currency: string
  billingUnit: 'token' | 'request' | 'character'
}

export interface ModelLimits {
  maxTokensPerRequest: number
  maxRequestsPerMinute: number
  maxConcurrentRequests: number
}

export interface ModelMetadata {
  version: string
  releaseDate: string
  trainingData?: string
  parameters?: string
  benchmarkScores?: Record<string, number>
  tags: string[]
}

export interface ModelConfigTemplate {
  id: string
  name: string
  description?: string
  modelId: string
  defaultConfig: ModelConfig
  isPublic: boolean
  usageCount: number
}

export interface ModelConfig {
  provider: string
  model: string
  temperature: number
  maxTokens: number
  topP: number
  topK?: number
  frequencyPenalty: number
  presencePenalty: number
  systemPrompt?: string
  stopSequences?: string[]
  customConfig?: Record<string, unknown>
}