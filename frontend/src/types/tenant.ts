import { BaseEntity } from './index'

export interface Tenant extends BaseEntity {
  name: string
  slug: string
  description?: string
  logoUrl?: string
  status: TenantStatus
  settings: TenantSettings
  subscription: TenantSubscription
}

export type TenantStatus = 'active' | 'suspended' | 'inactive'

export interface TenantSettings {
  maxProjects?: number
  maxEvaluations?: number
  allowedProviders: string[]
  retentionPeriodDays: number
  features: TenantFeature[]
}

export interface TenantFeature {
  name: string
  enabled: boolean
  config?: Record<string, unknown>
}

export interface TenantSubscription {
  plan: string
  status: 'active' | 'cancelled' | 'expired'
  expiresAt?: string
  limits: SubscriptionLimits
}

export interface SubscriptionLimits {
  evaluationsPerMonth: number
  modelsPerProject: number
  storageLimitGB: number
}