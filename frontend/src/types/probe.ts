/**
 * PROBE types for SACRA2 frontend
 * Based on the model definition:
 * 
 * PROBE {
 *   string id PK
 *   string code
 *   string name
 *   string description
 *   json   params
 *   string version
 *   boolean active
 *   %% UNIQUE: code
 *   %% UNIQUE: (name, version)
 *   %% NOTES: Basic test (prompt/tool/regex/etc.)
 * }
 */

export interface Probe {
  /** Unique identifier for the probe */
  id: string
  /** Unique code identifier for the probe */
  code: string
  /** Display name of the probe */
  name: string
  /** Description of what the probe tests */
  description: string
  /** JSON parameters for probe configuration */
  params: Record<string, any>
  /** Version of the probe */
  version: string
  /** Whether the probe is active */
  active: boolean
  /** Timestamps */
  createdAt?: string
  updatedAt?: string
}

export interface ProbeCreateRequest {
  /** Unique code identifier for the probe */
  code: string
  /** Display name of the probe */
  name: string
  /** Description of what the probe tests */
  description: string
  /** JSON parameters for probe configuration */
  params: Record<string, any>
  /** Version of the probe */
  version: string
  /** Whether the probe is active */
  active: boolean
}

export interface ProbeUpdateRequest {
  /** Display name of the probe */
  name?: string
  /** Description of what the probe tests */
  description?: string
  /** JSON parameters for probe configuration */
  params?: Record<string, any>
  /** Version of the probe */
  version?: string
  /** Whether the probe is active */
  active?: boolean
}

export interface ProbeListResponse {
  probes: Probe[]
  total: number
  page: number
  pageSize: number
}

/**
 * Common probe parameter types for different probe types
 */
export interface PromptProbeParams {
  type: 'prompt'
  prompt: string
  expectedResponse?: string
  model?: string
  temperature?: number
  maxTokens?: number
}

export interface RegexProbeParams {
  type: 'regex'
  pattern: string
  flags?: string
  testString?: string
  shouldMatch: boolean
}

export interface ToolProbeParams {
  type: 'tool'
  toolName: string
  toolArgs: Record<string, any>
  expectedOutput?: any
  timeout?: number
}

export interface CustomProbeParams {
  type: 'custom'
  script: string
  language: 'javascript' | 'python'
  dependencies?: string[]
}

export type ProbeParams = 
  | PromptProbeParams 
  | RegexProbeParams 
  | ToolProbeParams 
  | CustomProbeParams 
  | Record<string, any>

/**
 * Probe validation errors
 */
export interface ProbeValidationError {
  field: string
  message: string
  code: string
}

/**
 * Probe filter and search options
 */
export interface ProbeFilters {
  search?: string
  active?: boolean
  version?: string
  type?: string
  page?: number
  pageSize?: number
}
