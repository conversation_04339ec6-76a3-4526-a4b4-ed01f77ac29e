import { BaseEntity } from './index'

export interface Project extends BaseEntity {
  name: string
  description?: string
  status: ProjectStatus
  tenantId: string
  ownerId: string
  settings: ProjectSettings
  metadata: ProjectMetadata
  evaluationCount: number
  lastEvaluationAt?: string
}

export type ProjectStatus = 'active' | 'archived' | 'draft'

export interface ProjectSettings {
  defaultProvider?: string
  defaultModel?: string
  timeoutSeconds: number
  retryCount: number
  enableCaching: boolean
  cacheTtlMinutes: number
}

export interface ProjectMetadata {
  tags: string[]
  category?: string
  version: string
  environment: 'development' | 'staging' | 'production'
}

export interface ProjectCreateRequest {
  name: string
  description?: string
  settings?: Partial<ProjectSettings>
  metadata?: Partial<ProjectMetadata>
}

export interface ProjectUpdateRequest extends Partial<ProjectCreateRequest> {
  status?: ProjectStatus
}