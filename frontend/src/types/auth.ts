import { BaseEntity } from './index'

export interface User extends BaseEntity {
  email: string
  firstName: string
  lastName: string
  displayName: string
  avatar?: string
  isActive: boolean
  lastLoginAt?: string
  roles: UserRole[]
}

export interface UserRole {
  id: string
  name: string
  scope: RoleScope
  permissions: Permission[]
}

export interface Permission {
  id: string
  name: string
  resource: string
  action: string
}

export type RoleScope = 'platform' | 'tenant' | 'project' | 'evaluation'

export interface AuthContextType {
  user: User | null
  accessToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  refreshToken: () => Promise<string | null>
}

export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  accessToken: string
  user: User
}

export interface RefreshTokenResponse {
  accessToken: string
}