import { RouterProvider } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { ConfigProvider } from 'antd'
import enUS from 'antd/locale/en_US'

import { AuthProvider } from '@/auth/AuthContext'
import { router } from '@/routes'
import { queryClient } from '@/api/queryClient'

// Global error boundary
import { ErrorBoundary } from 'react-error-boundary'

const ErrorFallback = ({ error, resetErrorBoundary }: any) => (
  <div
    role='alert'
    style={{
      padding: 20,
      textAlign: 'center',
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
    }}
  >
    <h2>Something went wrong:</h2>
    <pre style={{ color: 'red', marginBottom: 20 }}>{error.message}</pre>
    <button onClick={resetErrorBoundary}>Try again</button>
  </div>
)

function App() {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onReset={() => window.location.assign(window.location.origin)}
    >
      <QueryClientProvider client={queryClient}>
        <ConfigProvider
          locale={enUS}
          theme={{
            token: {
              colorPrimary: '#1890ff',
              borderRadius: 6,
            },
          }}
        >
          <AuthProvider>
            <RouterProvider router={router} />
          </AuthProvider>
        </ConfigProvider>

        {/* React Query DevTools - only in development */}
        {import.meta.env.DEV && (
          <ReactQueryDevtools
            initialIsOpen={false}
            position='bottom-right'
          />
        )}
      </QueryClientProvider>
    </ErrorBoundary>
  )
}

export default App