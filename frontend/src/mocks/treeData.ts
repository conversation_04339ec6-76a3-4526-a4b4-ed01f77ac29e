import { TreeNode } from '../components/VSCodeRulesEditor';

/**
 * Mock tree data for the VSCode Rules Editor demo
 */
export const mockTreeData: TreeNode[] = [
  {
    id: '1',
    name: 'probe-001',
    type: 'folder',
    isExpanded: true,
    children: [
      {
        id: '2',
        name: 'rules',
        type: 'folder',
        isExpanded: true,
        children: [
          {
            id: '3',
            name: 'validation.json',
            type: 'file',
            extension: 'json',
            content: JSON.stringify({
              rules: [
                {
                  id: 'rule-001',
                  name: 'Check response time',
                  condition: 'response_time < 1000',
                  severity: 'warning'
                }
              ]
            }, null, 2)
          },
          {
            id: '4',
            name: 'transform.yaml',
            type: 'file',
            extension: 'yaml',
            content: `transform:
  - name: normalize_data
    type: json_path
    path: "$.data"
  - name: validate_schema
    type: schema_validation
    schema: "./schemas/response.json"`
          }
        ]
      },
      {
        id: '5',
        name: 'config',
        type: 'folder',
        children: [
          {
            id: '6',
            name: 'settings.ts',
            type: 'file',
            extension: 'ts',
            content: `export interface ProbeSettings {
  interval: number;
  timeout: number;
  retries: number;
}

export const defaultSettings: ProbeSettings = {
  interval: 30000,
  timeout: 5000,
  retries: 3
};`
          }
        ]
      }
    ]
  },
  {
    id: '7',
    name: 'probe-002',
    type: 'folder',
    children: [
      {
        id: '8',
        name: 'rules',
        type: 'folder',
        children: [
          {
            id: '9',
            name: 'security.json',
            type: 'file',
            extension: 'json',
            content: JSON.stringify({
              security_rules: [
                {
                  check: 'ssl_cert_validity',
                  min_days: 30
                },
                {
                  check: 'header_security',
                  required_headers: ['X-Frame-Options', 'X-Content-Type-Options']
                }
              ]
            }, null, 2)
          }
        ]
      }
    ]
  }
];
