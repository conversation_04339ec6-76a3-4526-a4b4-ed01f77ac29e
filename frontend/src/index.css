/* Global styles */
@tailwind base;
@tailwind components;
@tailwind utilities;
* {
  box-sizing: border-box;
}

html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Ant Design customizations */
.ant-layout {
  min-height: 100vh;
}

/* ProTable customizations */
.ant-pro-table .ant-table-tbody > tr > td {
  padding: 12px 16px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Loading states */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .ant-pro-table .ant-table-thead > tr > th,
  .ant-pro-table .ant-table-tbody > tr > td {
    padding: 8px 12px;
  }
}

/* Print styles */
@media print {
  .ant-layout-sider,
  .ant-layout-header,
  .ant-affix,
  .ant-back-top,
  .ant-drawer,
  .ant-modal,
  .ant-message,
  .ant-notification {
    display: none !important;
  }
}