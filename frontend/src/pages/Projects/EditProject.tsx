import { useParams, useNavigate } from 'react-router-dom'
import { Typography, Spin, Alert } from 'antd'

import { useProject } from '@/api/queries/useProjects'
import ProjectForm from '@/components/forms/ProjectForm'

const { Title } = Typography

export const EditProjectPage = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { data: project, isLoading, error } = useProject(id!)

  const handleSuccess = () => {
    navigate('/projects')
  }

  const handleCancel = () => {
    navigate('/projects')
  }

  if (isLoading) {
    return <Spin tip='Loading project...' />
  }

  if (error) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred.'
    return <Alert message='Error' description={errorMessage} type='error' showIcon />
  }

  return (
    <div>
      <Title level={2}>Edit Project</Title>
      {project && (
        <ProjectForm
          project={project}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      )}
    </div>
  )
}
