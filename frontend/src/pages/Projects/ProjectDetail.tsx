import { useParams } from 'react-router-dom'
import { Card, Typography, Spin, Alert } from 'antd'

import { useProject } from '@/api/queries/useProjects'
import { EvaluationsTable } from '@/components/tables/EvaluationsTable'

const { Title } = Typography

export const ProjectDetailPage = () => {
  const { id } = useParams<{ id: string }>()
  const { data: project, isLoading, error } = useProject(id!)

  if (isLoading) {
    return <Spin size='large' style={{ display: 'block', textAlign: 'center', padding: 50 }} />
  }

  if (error) {
    return (
      <Alert
        message='Error'
        description='Failed to load project details'
        type='error'
        showIcon
      />
    )
  }

  if (!project) {
    return (
      <Alert
        message='Not Found'
        description='Project not found'
        type='warning'
        showIcon
      />
    )
  }

  return (
    <div>
      <Title level={2}>{project.name}</Title>
      
      {project.description && (
        <Card style={{ marginBottom: 24 }}>
          <p>{project.description}</p>
        </Card>
      )}

      <Card title='Project Evaluations'>
        <EvaluationsTable projectId={project.id} />
      </Card>
    </div>
  )
}