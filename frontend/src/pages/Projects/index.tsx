import { useState } from 'react'
import { Typography, Modal } from 'antd'

import { ProjectsTable } from '@/components/tables/ProjectsTable'
import ProjectForm from '@/components/forms/ProjectForm'

const { Title } = Typography

export const ProjectsPage = () => {
  const [createModalVisible, setCreateModalVisible] = useState(false)

  const handleCreateProject = () => {
    setCreateModalVisible(true)
  }

  return (
    <div>
      <Title level={2}>Projects</Title>
      
      <ProjectsTable onCreateProject={handleCreateProject} />

      {/* Create Project Modal */}
      <Modal
        title='Create New Project'
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={600}
      >
        <ProjectForm
          onSuccess={() => setCreateModalVisible(false)}
          onCancel={() => setCreateModalVisible(false)}
        />
      </Modal>
    </div>
  )
}