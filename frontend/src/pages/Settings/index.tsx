import { Card, Typography, Tabs } from 'antd'
import { UserOutlined, SettingOutlined, TeamOutlined } from '@ant-design/icons'

const { Title } = Typography

export const SettingsPage = () => {
  const items = [
    {
      key: 'profile',
      label: 'Profile',
      icon: <UserOutlined />,
      children: (
        <Card>
          <p>User profile settings will be implemented here</p>
        </Card>
      ),
    },
    {
      key: 'preferences',
      label: 'Preferences',
      icon: <SettingOutlined />,
      children: (
        <Card>
          <p>Application preferences will be implemented here</p>
        </Card>
      ),
    },
    {
      key: 'team',
      label: 'Team',
      icon: <TeamOutlined />,
      children: (
        <Card>
          <p>Team management will be implemented here</p>
        </Card>
      ),
    },
  ]

  return (
    <div>
      <Title level={2}>Settings</Title>
      
      <Tabs defaultActiveKey='profile' items={items} />
    </div>
  )
}