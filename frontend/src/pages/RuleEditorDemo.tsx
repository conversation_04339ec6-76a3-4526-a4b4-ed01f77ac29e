import React, { useRef, useCallback } from 'react';
import { Card, Button, Space, Tooltip, message } from 'antd';
import { PlayCircleOutlined, SaveOutlined, FormatPainterOutlined, ReloadOutlined } from '@ant-design/icons';
import { VSCodeRulesEditor, VSCodeRulesEditorHandle, TreeNode } from '../components/VSCodeRulesEditor';
import { realRulesTreeData } from '../mocks/realRulesData';

/**
 * Demo page showcasing the VSCodeRulesEditor component
 * 
 * Features demonstrated:
 * - File tree navigation and operations
 * - Monaco editor integration
 * - Toolbar actions (Run, Format, Save, Reload)
 * - Real-time file editing
 * - Search and filtering
 * - Drag & drop operations
 * - Context menus
 * - Keyboard shortcuts
 */
export const RuleEditorDemoPage: React.FC = () => {
  const editorRef = useRef<VSCodeRulesEditorHandle>(null);

  // Toolbar action handlers
  const handleRun = useCallback(() => {
    editorRef.current?.run();
    message.success('Run triggered - validation started');
  }, []);

  const handleFormat = useCallback(() => {
    editorRef.current?.format();
    message.success('Code formatted successfully');
  }, []);

  const handleSave = useCallback(() => {
    const result = editorRef.current?.save();
    if (result?.fileName) {
      message.success(`Saved ${result.fileName}`);
      console.log('Saved file:', result);
    } else {
      message.info('No file selected to save');
    }
  }, []);

  const handleReload = useCallback(() => {
    editorRef.current?.reload();
    message.success('Editor reloaded to initial state');
  }, []);

  // File change handler
  const handleFileChange = useCallback((fileId: string, content: string) => {
    console.log('File changed:', { fileId, content: content.substring(0, 100) + '...' });
  }, []);

  // File selection handler
  const handleFileSelect = useCallback((file: TreeNode) => {
    console.log('File selected:', file.name);
  }, []);

  // Debug: Log the tree data being used
  React.useEffect(() => {
    console.log('RuleEditorDemo - Tree data:', realRulesTreeData);
    console.log('RuleEditorDemo - First item:', realRulesTreeData[0]);
  }, []);

  return (
    <Card
      title={
        <div className="flex items-center gap-2">
          <span>Probe Editor</span>
          <span className="text-xs text-gray-500">PROBE admin panel demo</span>
        </div>
      }
      extra={
        <Space size="small">
          <Tooltip title="Run validation tests on current file">
            <Button size="small" type="primary" icon={<PlayCircleOutlined />} onClick={handleRun}>
              Run
            </Button>
          </Tooltip>
          <Tooltip title="Format current file with Monaco editor">
            <Button size="small" icon={<FormatPainterOutlined />} onClick={handleFormat}>
              Format
            </Button>
          </Tooltip>
          <Tooltip title="Save current file">
            <Button size="small" icon={<SaveOutlined />} onClick={handleSave}>
              Save
            </Button>
          </Tooltip>
          <Tooltip title="Reload editor to initial state">
            <Button size="small" icon={<ReloadOutlined />} onClick={handleReload}>
              Reload
            </Button>
          </Tooltip>
        </Space>
      }
      bordered
      bodyStyle={{ padding: 0 }}
    >
      <div style={{ height: '70vh' }}>
        <VSCodeRulesEditor
          ref={editorRef}
          initialTreeData={realRulesTreeData}
          onFileChange={handleFileChange}
          onFileSelect={handleFileSelect}
          theme="vs-dark"
          readOnly={false}
          className="h-full"
          key="sacra2-rules-editor"
        />
      </div>
    </Card>
  );
};

