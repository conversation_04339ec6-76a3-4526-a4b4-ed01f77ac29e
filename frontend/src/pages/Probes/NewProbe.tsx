import { useNavigate } from 'react-router-dom'
import { <PERSON><PERSON><PERSON>, Card, Button, Space } from 'antd'
import { ArrowLeftOutlined } from '@ant-design/icons'

import ProbeForm from '@/components/forms/ProbeForm'

const { Title } = Typography

export const NewProbePage = () => {
  const navigate = useNavigate()

  const handleSuccess = () => {
    navigate('/probes')
  }

  const handleCancel = () => {
    navigate('/probes')
  }

  const handleBack = () => {
    navigate('/probes')
  }

  return (
    <div>
      <Space style={{ marginBottom: 24 }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={handleBack}
        >
          Back to Probes
        </Button>
      </Space>

      <Card>
        <Title level={2}>Create New Probe</Title>
        <ProbeForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </Card>
    </div>
  )
}
