import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Button,
  Card,
  Input,
  Space,
  Table,
  Tag,
  Typography,
  Switch,
  Tooltip,
  Popconfirm,
  Select,
  Row,
  Col,
  Divider
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  Play<PERSON>ircleOutlined,
  FilterOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { useProbes, useDeleteProbe, useToggleProbeStatus, useDuplicateProbe } from '@/api/queries/useProbes'
import type { Probe, ProbeFilters } from '@/types/probe'

const { Title, Text } = Typography
const { Search } = Input

export const ProbesPage = () => {
  const navigate = useNavigate()
  const [filters, setFilters] = useState<ProbeFilters>({
    page: 1,
    pageSize: 20
  })

  const { data: probesData, isLoading, error } = useProbes(filters)
  const deleteProbe = useDeleteProbe()
  const toggleProbeStatus = useToggleProbeStatus()
  const duplicateProbe = useDuplicateProbe()

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value, page: 1 }))
  }

  const handleFilterChange = (key: keyof ProbeFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }))
  }

  const handleTableChange = (pagination: any) => {
    setFilters(prev => ({
      ...prev,
      page: pagination.current,
      pageSize: pagination.pageSize
    }))
  }

  const handleEdit = (probe: Probe) => {
    navigate(`/probes/${probe.id}/edit`)
  }

  const handleView = (probe: Probe) => {
    navigate(`/probes/${probe.id}`)
  }

  const handleDelete = (probe: Probe) => {
    deleteProbe.mutate(probe.id)
  }

  const handleToggleStatus = (probe: Probe) => {
    toggleProbeStatus.mutate({ id: probe.id, active: !probe.active })
  }

  const handleDuplicate = (probe: Probe) => {
    const newVersion = `${probe.version}-copy-${Date.now()}`
    duplicateProbe.mutate({ id: probe.id, newVersion })
  }

  const handleRun = (probe: Probe) => {
    // TODO: Implement probe execution
    console.log('Running probe:', probe.id)
  }

  const columns: ColumnsType<Probe> = [
    {
      title: 'Code',
      dataIndex: 'code',
      key: 'code',
      width: 150,
      render: (code: string) => (
        <Text code style={{ fontSize: '12px' }}>{code}</Text>
      ),
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Probe) => (
        <Button 
          type="link" 
          onClick={() => handleView(record)}
          style={{ padding: 0, height: 'auto' }}
        >
          {name}
        </Button>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (description: string) => (
        <Tooltip title={description}>
          <Text type="secondary">{description}</Text>
        </Tooltip>
      ),
    },
    {
      title: 'Type',
      dataIndex: ['params', 'type'],
      key: 'type',
      width: 100,
      render: (type: string) => {
        const typeColors: Record<string, string> = {
          prompt: 'blue',
          regex: 'green',
          tool: 'orange',
          custom: 'purple'
        }
        return (
          <Tag color={typeColors[type] || 'default'}>
            {type?.toUpperCase() || 'UNKNOWN'}
          </Tag>
        )
      },
    },
    {
      title: 'Version',
      dataIndex: 'version',
      key: 'version',
      width: 100,
      render: (version: string) => (
        <Tag>{version}</Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'active',
      key: 'active',
      width: 100,
      render: (active: boolean, record: Probe) => (
        <Switch
          checked={active}
          onChange={() => handleToggleStatus(record)}
          loading={toggleProbeStatus.isPending}
          size="small"
        />
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 200,
      render: (_, record: Probe) => (
        <Space size="small">
          <Tooltip title="Run Probe">
            <Button
              type="text"
              icon={<PlayCircleOutlined />}
              onClick={() => handleRun(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="Duplicate">
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={() => handleDuplicate(record)}
              loading={duplicateProbe.isPending}
              size="small"
            />
          </Tooltip>
          <Popconfirm
            title="Delete Probe"
            description="Are you sure you want to delete this probe?"
            onConfirm={() => handleDelete(record)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
                size="small"
                loading={deleteProbe.isPending}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={2} style={{ margin: 0 }}>
              Probes
            </Title>
            <Text type="secondary">
              Manage and configure test probes for your evaluations
            </Text>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/probes/new')}
            >
              New Probe
            </Button>
          </Col>
        </Row>
      </div>

      <Card>
        {/* Filters */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="Search probes..."
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Select
              placeholder="Status"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilterChange('active', value)}
              options={[
                { value: true, label: 'Active' },
                { value: false, label: 'Inactive' },
              ]}
            />
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Select
              placeholder="Type"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilterChange('type', value)}
              options={[
                { value: 'prompt', label: 'Prompt' },
                { value: 'regex', label: 'Regex' },
                { value: 'tool', label: 'Tool' },
                { value: 'custom', label: 'Custom' },
              ]}
            />
          </Col>
        </Row>

        <Divider />

        {/* Table */}
        <Table<Probe>
          columns={columns}
          dataSource={probesData?.probes || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: filters.page,
            pageSize: filters.pageSize,
            total: probesData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} probes`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 800 }}
        />
      </Card>
    </div>
  )
}

export default ProbesPage
