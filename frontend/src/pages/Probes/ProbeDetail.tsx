import { useParams, useNavigate } from 'react-router-dom'
import {
  <PERSON><PERSON><PERSON>,
  Spin,
  <PERSON>ert,
  Card,
  Button,
  Space,
  Descriptions,
  Tag,
  Switch,
  Popconfirm,
  Row,
  Col,
  Divider
} from 'antd'
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  Play<PERSON>ircleOutlined,
  CodeOutlined
} from '@ant-design/icons'
import { useProbe, useDeleteProbe, useToggleProbeStatus, useDuplicateProbe } from '@/api/queries/useProbes'
import type { Probe } from '@/types/probe'

const { Title, Text, Paragraph } = Typography

export const ProbeDetailPage = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { data: probe, isLoading, error } = useProbe(id!)
  const deleteProbe = useDeleteProbe()
  const toggleProbeStatus = useToggleProbeStatus()
  const duplicateProbe = useDuplicateProbe()

  const handleBack = () => {
    navigate('/probes')
  }

  const handleEdit = () => {
    navigate(`/probes/${id}/edit`)
  }

  const handleDelete = () => {
    deleteProbe.mutate(id!, {
      onSuccess: () => {
        navigate('/probes')
      }
    })
  }

  const handleToggleStatus = () => {
    if (probe) {
      toggleProbeStatus.mutate({ id: probe.id, active: !probe.active })
    }
  }

  const handleDuplicate = () => {
    if (probe) {
      const newVersion = `${probe.version}-copy-${Date.now()}`
      duplicateProbe.mutate({ id: probe.id, newVersion }, {
        onSuccess: () => {
          navigate('/probes')
        }
      })
    }
  }

  const handleRun = () => {
    // TODO: Implement probe execution
    console.log('Running probe:', id)
  }

  if (isLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '400px' 
      }}>
        <Spin size="large" tip="Loading probe..." />
      </div>
    )
  }

  if (error) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred.'
    return (
      <div>
        <Space style={{ marginBottom: 24 }}>
          <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
            Back to Probes
          </Button>
        </Space>
        <Alert 
          message="Error Loading Probe" 
          description={errorMessage} 
          type="error" 
          showIcon 
        />
      </div>
    )
  }

  if (!probe) {
    return (
      <div>
        <Space style={{ marginBottom: 24 }}>
          <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
            Back to Probes
          </Button>
        </Space>
        <Alert 
          message="Probe Not Found" 
          description="The requested probe could not be found." 
          type="warning" 
          showIcon 
        />
      </div>
    )
  }

  const getProbeTypeColor = (type: string) => {
    const typeColors: Record<string, string> = {
      prompt: 'blue',
      regex: 'green',
      tool: 'orange',
      custom: 'purple'
    }
    return typeColors[type] || 'default'
  }

  const renderProbeParams = (params: any) => {
    if (!params || typeof params !== 'object') {
      return <Text type="secondary">No parameters configured</Text>
    }

    return (
      <div style={{ marginTop: 16 }}>
        <pre style={{ 
          background: '#f5f5f5', 
          padding: 16, 
          borderRadius: 6,
          overflow: 'auto',
          fontSize: '12px'
        }}>
          {JSON.stringify(params, null, 2)}
        </pre>
      </div>
    )
  }

  return (
    <div>
      <Space style={{ marginBottom: 24 }}>
        <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
          Back to Probes
        </Button>
      </Space>

      <Row gutter={[24, 24]}>
        <Col span={24}>
          <Card
            title={
              <Space>
                <CodeOutlined />
                <span>{probe.name}</span>
                <Tag color={probe.active ? 'green' : 'red'}>
                  {probe.active ? 'Active' : 'Inactive'}
                </Tag>
              </Space>
            }
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={handleRun}
                >
                  Run Probe
                </Button>
                <Button
                  icon={<EditOutlined />}
                  onClick={handleEdit}
                >
                  Edit
                </Button>
                <Button
                  icon={<CopyOutlined />}
                  onClick={handleDuplicate}
                  loading={duplicateProbe.isPending}
                >
                  Duplicate
                </Button>
                <Popconfirm
                  title="Delete Probe"
                  description="Are you sure you want to delete this probe?"
                  onConfirm={handleDelete}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button
                    danger
                    icon={<DeleteOutlined />}
                    loading={deleteProbe.isPending}
                  >
                    Delete
                  </Button>
                </Popconfirm>
              </Space>
            }
          >
            <Descriptions column={2} bordered>
              <Descriptions.Item label="Code" span={1}>
                <Text code>{probe.code}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="Version" span={1}>
                <Tag>{probe.version}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Type" span={1}>
                <Tag color={getProbeTypeColor(probe.params?.type)}>
                  {probe.params?.type?.toUpperCase() || 'UNKNOWN'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Status" span={1}>
                <Switch
                  checked={probe.active}
                  onChange={handleToggleStatus}
                  loading={toggleProbeStatus.isPending}
                />
                <Text style={{ marginLeft: 8 }}>
                  {probe.active ? 'Active' : 'Inactive'}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Description" span={2}>
                <Paragraph>{probe.description}</Paragraph>
              </Descriptions.Item>
              <Descriptions.Item label="Created" span={1}>
                {probe.createdAt ? new Date(probe.createdAt).toLocaleString() : 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="Updated" span={1}>
                {probe.updatedAt ? new Date(probe.updatedAt).toLocaleString() : 'N/A'}
              </Descriptions.Item>
            </Descriptions>

            <Divider orientation="left">Parameters</Divider>
            {renderProbeParams(probe.params)}
          </Card>
        </Col>
      </Row>
    </div>
  )
}
