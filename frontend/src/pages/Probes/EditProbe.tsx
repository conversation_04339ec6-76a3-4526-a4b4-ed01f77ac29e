import { useParams, useNavigate } from 'react-router-dom'
import { Typography, Spin, Alert, Card, Button, Space } from 'antd'
import { ArrowLeftOutlined } from '@ant-design/icons'

import { useProbe } from '@/api/queries/useProbes'
import ProbeForm from '@/components/forms/ProbeForm'

const { Title } = Typography

export const EditProbePage = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { data: probe, isLoading, error } = useProbe(id!)

  const handleSuccess = () => {
    navigate('/probes')
  }

  const handleCancel = () => {
    navigate('/probes')
  }

  const handleBack = () => {
    navigate('/probes')
  }

  if (isLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '400px' 
      }}>
        <Spin size="large" tip="Loading probe..." />
      </div>
    )
  }

  if (error) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred.'
    return (
      <div>
        <Space style={{ marginBottom: 24 }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={handleBack}
          >
            Back to Probes
          </Button>
        </Space>
        <Alert 
          message="Error Loading Probe" 
          description={errorMessage} 
          type="error" 
          showIcon 
        />
      </div>
    )
  }

  if (!probe) {
    return (
      <div>
        <Space style={{ marginBottom: 24 }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={handleBack}
          >
            Back to Probes
          </Button>
        </Space>
        <Alert 
          message="Probe Not Found" 
          description="The requested probe could not be found." 
          type="warning" 
          showIcon 
        />
      </div>
    )
  }

  return (
    <div>
      <Space style={{ marginBottom: 24 }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={handleBack}
        >
          Back to Probes
        </Button>
      </Space>

      <Card>
        <Title level={2}>Edit Probe</Title>
        <ProbeForm
          probe={probe}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </Card>
    </div>
  )
}
