import { useState } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { Card, Form, Input, Button, Typography, Space, Divider } from 'antd'
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons'

import { useAuth } from '@/auth/useAuth'

const { Title, Text } = Typography

interface LocationState {
  from: {
    pathname: string
  }
}

export const LoginPage = () => {
  const [loading, setLoading] = useState(false)
  const { login, isAuthenticated } = useAuth()
  const location = useLocation()
  
  const from = (location.state as LocationState)?.from?.pathname || '/dashboard'

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to={from} replace />
  }

  const handleLogin = async (values: { email: string; password: string }) => {
    setLoading(true)
    try {
      await login(values.email, values.password)
      // Navigation will be handled by the auth context
    } catch (error) {
      // Error handling is done by the auth context
    } finally {
      setLoading(false)
    }
  }

  return (
    <div
      style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '20px',
      }}
    >
      <Card
        style={{
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Space
          direction='vertical'
          size='large'
          style={{ width: '100%', textAlign: 'center' }}
        >
          {/* Logo/Title */}
          <div>
            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
              SACRA2
            </Title>
            <Text type='secondary'>
              AI Model Evaluation Platform
            </Text>
          </div>

          <Divider />

          {/* Login Form */}
          <Form
            name='login'
            onFinish={handleLogin}
            autoComplete='off'
            size='large'
          >
            <Form.Item
              name='email'
              rules={[
                { required: true, message: 'Please input your email!' },
                { type: 'email', message: 'Please enter a valid email!' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder='Email'
                autoComplete='email'
              />
            </Form.Item>

            <Form.Item
              name='password'
              rules={[
                { required: true, message: 'Please input your password!' },
                { min: 6, message: 'Password must be at least 6 characters!' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder='Password'
                autoComplete='current-password'
              />
            </Form.Item>

            <Form.Item>
              <Button
                type='primary'
                htmlType='submit'
                loading={loading}
                icon={<LoginOutlined />}
                style={{ width: '100%', height: 45 }}
              >
                Sign In
              </Button>
            </Form.Item>
          </Form>

          {/* Demo Credentials */}
          <div style={{ textAlign: 'left' }}>
            <Text type='secondary' style={{ fontSize: '12px' }}>
              Demo Credentials:
              <br />
              Email: <EMAIL>
              <br />
              Password: admin123
            </Text>
          </div>
        </Space>
      </Card>
    </div>
  )
}