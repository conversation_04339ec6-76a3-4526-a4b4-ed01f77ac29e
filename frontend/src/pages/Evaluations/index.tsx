import { useState } from 'react'
import { Typography, Modal } from 'antd'

import { EvaluationsTable } from '@/components/tables/EvaluationsTable'

const { Title } = Typography

export const EvaluationsPage = () => {
  const [createModalVisible, setCreateModalVisible] = useState(false)

  const handleCreateEvaluation = () => {
    setCreateModalVisible(true)
  }

  return (
    <div>
      <Title level={2}>Evaluations</Title>
      
      <EvaluationsTable onCreateEvaluation={handleCreateEvaluation} />

      {/* Create Evaluation Modal - TODO: Implement EvaluationForm */}
      <Modal
        title='Create New Evaluation'
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={800}
      >
        {/* TODO: Add EvaluationForm component */}
        <div style={{ padding: 20, textAlign: 'center' }}>
          EvaluationForm component will be implemented here
        </div>
      </Modal>
    </div>
  )
}