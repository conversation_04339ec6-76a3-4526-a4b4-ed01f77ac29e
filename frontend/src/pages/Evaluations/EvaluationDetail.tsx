import { useParams } from 'react-router-dom'
import { Card, Typography, Spin, Alert } from 'antd'

import { useEvaluation } from '@/api/queries/useEvaluations'

const { Title } = Typography

export const EvaluationDetailPage = () => {
  const { id } = useParams<{ id: string }>()
  const { data: evaluation, isLoading, error } = useEvaluation(id!)

  if (isLoading) {
    return <Spin size='large' style={{ display: 'block', textAlign: 'center', padding: 50 }} />
  }

  if (error) {
    return (
      <Alert
        message='Error'
        description='Failed to load evaluation details'
        type='error'
        showIcon
      />
    )
  }

  if (!evaluation) {
    return (
      <Alert
        message='Not Found'
        description='Evaluation not found'
        type='warning'
        showIcon
      />
    )
  }

  return (
    <div>
      <Title level={2}>{evaluation.name}</Title>
      
      <Card>
        {/* TODO: Add detailed evaluation results and metrics */}
        <p>Evaluation details and results will be implemented here</p>
        <p>Status: {evaluation.status}</p>
        {evaluation.results && (
          <p>Score: {(evaluation.results.overallScore * 100).toFixed(1)}%</p>
        )}
      </Card>
    </div>
  )
}