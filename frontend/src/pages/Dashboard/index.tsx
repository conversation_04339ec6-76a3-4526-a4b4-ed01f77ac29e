import { Card, Typography } from 'antd'

import { EvaluationsTable } from '@/components/tables/EvaluationsTable'
import DashboardMetrics from './DashboardMetrics'

const { Title } = Typography

export const DashboardPage = () => {
  return (
    <div>
      <Title level={2}>Dashboard</Title>

      {/* Key Metrics and Analytics */}
      <DashboardMetrics />

      {/* Recent Evaluations */}
      <Card title='Recent Evaluations'>
        <EvaluationsTable showActions={false} />
      </Card>
    </div>
  )
}