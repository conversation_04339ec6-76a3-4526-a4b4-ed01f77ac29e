import { Card, Row, Col, Statistic, Typography, Empty, List, Skeleton } from 'antd'
import {
  ProjectOutlined,
  ExperimentOutlined,
  DollarOutlined,
  TrophyOutlined,
} from '@ant-design/icons'
import {
  useDashboardStats,
  useCostAnalytics,
  usePerformanceMetrics,
} from '@/api/queries/useAnalytics'

const { Title } = Typography

/**
 * DashboardMetrics
 * Centralizes KPI tiles and basic analytics sections.
 * Uses existing query hooks and renders gracefully without charts dependency.
 */
export const DashboardMetrics = () => {
  const { data: stats, isLoading: loadingStats } = useDashboardStats()
  const { data: cost, isLoading: loadingCost } = useCostAnalytics('30d')
  const { data: perf, isLoading: loadingPerf } = usePerformanceMetrics('7d')

  // Try to extract simple time-series arrays; fallback to any[]
  const costSeries: any[] = Array.isArray((cost as any)?.series)
    ? (cost as any).series
    : Array.isArray(cost)
      ? (cost as any)
      : []
  const perfSeries: any[] = Array.isArray((perf as any)?.series)
    ? (perf as any).series
    : Array.isArray(perf)
      ? (perf as any)
      : []

  return (
    <div>
      {/* KPI Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title='Total Projects'
              value={(stats as any)?.totalProjects || 0}
              prefix={<ProjectOutlined />}
              loading={loadingStats}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title='Total Evaluations'
              value={(stats as any)?.totalEvaluations || 0}
              prefix={<ExperimentOutlined />}
              loading={loadingStats}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title='Total Cost'
              value={(stats as any)?.totalCost || 0}
              precision={2}
              prefix={<DollarOutlined />}
              loading={loadingStats}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title='Average Score'
              value={((stats as any)?.averageScore || 0) * 100}
              precision={1}
              suffix='%'
              prefix={<TrophyOutlined />}
              loading={loadingStats}
            />
          </Card>
        </Col>
      </Row>

      {/* Cost (30d) */}
      <Card title='Cost (last 30 days)' style={{ marginBottom: 24 }}>
        {loadingCost ? (
          <Skeleton active />
        ) : costSeries.length > 0 ? (
          <List
            size='small'
            dataSource={costSeries.slice(0, 10)}
            renderItem={(item: any, idx) => (
              <List.Item>
                <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                  <span>{item.date || item.x || idx}</span>
                  <strong>{item.value ?? item.y ?? '-'}</strong>
                </div>
              </List.Item>
            )}
          />
        ) : (
          <Empty description='No cost data' />
        )}
      </Card>

      {/* Performance (7d) */}
      <Card title='Performance (last 7 days)'>
        {loadingPerf ? (
          <Skeleton active />
        ) : perfSeries.length > 0 ? (
          <List
            size='small'
            dataSource={perfSeries.slice(0, 10)}
            renderItem={(item: any, idx) => (
              <List.Item>
                <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                  <span>{item.date || item.x || idx}</span>
                  <strong>{item.value ?? item.y ?? '-'}</strong>
                </div>
              </List.Item>
            )}
          />
        ) : (
          <Empty description='No performance data' />
        )}
      </Card>
    </div>
  )
}

export default DashboardMetrics
