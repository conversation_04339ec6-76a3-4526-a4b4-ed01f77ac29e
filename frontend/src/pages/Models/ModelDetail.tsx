import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Typography, Spin, Card, Descriptions, Tag, Alert, Button, Row, Col } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';

import { useModel, useModelProvider } from '@/api/queries/useModels';

const { Title, Text, Paragraph } = Typography;

export const ModelDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const { data: model, isLoading, error } = useModel(id || '');
  const { data: provider } = useModelProvider(model?.providerId || '');

  if (isLoading) {
    return <Spin size='large' style={{ display: 'block', marginTop: 50 }} />;
  }

  if (error) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return <Alert message="Error loading model details" description={errorMessage} type="error" showIcon />;
  }

  if (!model) {
    return <Alert message="Model not found" type="warning" />;
  }

  return (
    <div>
      <Link to="/models">
        <Button type="text" icon={<ArrowLeftOutlined />} style={{ marginBottom: 16 }}>
          Back to Models
        </Button>
      </Link>

      <Title level={2}>{model.displayName}</Title>
      <Text type="secondary">ID: {model.id}</Text>

      <Paragraph style={{ marginTop: 16 }}>{model.description}</Paragraph>

      <Row gutter={[16, 16]}>
        <Col xs={24} md={12}>
          <Card title="General Information">
            <Descriptions column={1} bordered>
              <Descriptions.Item label="Provider">
                {provider ? provider.displayName : 'Loading...'}
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={model.status === 'available' ? 'green' : 'orange'}>{model.status}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Version">{model.metadata.version}</Descriptions.Item>
              <Descriptions.Item label="Release Date">{new Date(model.metadata.releaseDate).toLocaleDateString()}</Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card title="Capabilities">
            <Descriptions column={1} bordered>
              <Descriptions.Item label="Context Length">{model.capabilities.contextLength.toLocaleString()} tokens</Descriptions.Item>
              <Descriptions.Item label="Input Types">
                {model.capabilities.inputTypes.map(t => <Tag key={t}>{t}</Tag>)}
              </Descriptions.Item>
              <Descriptions.Item label="Output Types">
                {model.capabilities.outputTypes.map(t => <Tag key={t}>{t}</Tag>)}
              </Descriptions.Item>
              <Descriptions.Item label="Supports Streaming">{model.capabilities.supportsStreaming ? 'Yes' : 'No'}</Descriptions.Item>
              <Descriptions.Item label="Supports Tools">{model.capabilities.supportsTools ? 'Yes' : 'No'}</Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card title="Pricing (USD)">
            <Descriptions column={1} bordered>
              <Descriptions.Item label="Billing Unit">{model.pricing.billingUnit}</Descriptions.Item>
              <Descriptions.Item label="Input Price">${model.pricing.inputTokenPriceUsd.toFixed(6)} / token</Descriptions.Item>
              <Descriptions.Item label="Output Price">${model.pricing.outputTokenPriceUsd.toFixed(6)} / token</Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card title="Rate Limits">
            <Descriptions column={1} bordered>
              <Descriptions.Item label="Max Tokens / Request">{model.limits.maxTokensPerRequest.toLocaleString()}</Descriptions.Item>
              <Descriptions.Item label="Max Requests / Minute">{model.limits.maxRequestsPerMinute}</Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>
    </div>
  );
};
