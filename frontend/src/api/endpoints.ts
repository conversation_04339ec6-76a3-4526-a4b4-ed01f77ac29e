import { Pa<PERSON>ationParams, PaginatedResponse } from '@/types'
import { User, LoginRequest, LoginResponse } from '@/types/auth'
import { Project, ProjectCreateRequest, ProjectUpdateRequest } from '@/types/project'
import { Evaluation, EvaluationCreateRequest, EvaluationUpdateRequest } from '@/types/evaluation'
import { ModelProvider, Model, ModelConfigTemplate } from '@/types/model'
import { Tenant } from '@/types/tenant'
import { api } from './client'

// Auth endpoints
export const authApi = {
  login: (data: LoginRequest): Promise<LoginResponse> =>
    api.post<LoginResponse>('/auth/login', data),

  logout: (): Promise<void> =>
    api.post<void>('/auth/logout'),

  refresh: (): Promise<{ accessToken: string }> =>
    api.post<{ accessToken: string }>('/auth/refresh'),

  getProfile: (): Promise<User> =>
    api.get<User>('/auth/me'),

  updateProfile: (data: Partial<User>): Promise<User> =>
    api.patch<User>('/auth/me', data),
}

// Tenant endpoints
export const tenantApi = {
  getCurrent: (): Promise<Tenant> =>
    api.get<Tenant>('/tenant/current'),

  update: (data: Partial<Tenant>): Promise<Tenant> =>
    api.patch<Tenant>('/tenant/current', data),

  getSettings: (): Promise<Tenant['settings']> =>
    api.get<Tenant['settings']>('/tenant/current/settings'),

  updateSettings: (data: Partial<Tenant['settings']>): Promise<Tenant['settings']> =>
    api.patch<Tenant['settings']>('/tenant/current/settings', data),
}

// Project endpoints
export const projectApi = {
  getAll: (params?: PaginationParams): Promise<PaginatedResponse<Project>> =>
    api.get<PaginatedResponse<Project>>('/projects', { params }),

  getById: (id: string): Promise<Project> =>
    api.get<Project>(`/projects/${id}`),

  create: (data: ProjectCreateRequest): Promise<Project> =>
    api.post<Project>('/projects', data),

  update: (id: string, data: ProjectUpdateRequest): Promise<Project> =>
    api.patch<Project>(`/projects/${id}`, data),

  delete: (id: string): Promise<void> =>
    api.delete<void>(`/projects/${id}`),

  duplicate: (id: string, name: string): Promise<Project> =>
    api.post<Project>(`/projects/${id}/duplicate`, { name }),

  getStats: (id: string): Promise<{
    evaluationCount: number
    lastEvaluationAt: string | null
    totalCost: number
    averageScore: number
  }> =>
    api.get(`/projects/${id}/stats`),
}

// Evaluation endpoints  
export const evaluationApi = {
  getAll: (projectId?: string, params?: PaginationParams): Promise<PaginatedResponse<Evaluation>> => {
    const url = projectId ? `/projects/${projectId}/evaluations` : '/evaluations'
    return api.get<PaginatedResponse<Evaluation>>(url, { params })
  },

  getById: (id: string): Promise<Evaluation> =>
    api.get<Evaluation>(`/evaluations/${id}`),

  create: (projectId: string, data: EvaluationCreateRequest): Promise<Evaluation> =>
    api.post<Evaluation>(`/projects/${projectId}/evaluations`, data),

  update: (id: string, data: EvaluationUpdateRequest): Promise<Evaluation> =>
    api.patch<Evaluation>(`/evaluations/${id}`, data),

  delete: (id: string): Promise<void> =>
    api.delete<void>(`/evaluations/${id}`),

  start: (id: string): Promise<Evaluation> =>
    api.post<Evaluation>(`/evaluations/${id}/start`),

  stop: (id: string): Promise<Evaluation> =>
    api.post<Evaluation>(`/evaluations/${id}/stop`),

  retry: (id: string): Promise<Evaluation> =>
    api.post<Evaluation>(`/evaluations/${id}/retry`),

  getResults: (id: string): Promise<Evaluation['results']> =>
    api.get<Evaluation['results']>(`/evaluations/${id}/results`),

  exportResults: (id: string, format: 'json' | 'csv' | 'pdf'): Promise<Blob> =>
    api.get(`/evaluations/${id}/export/${format}`, { 
      responseType: 'blob' 
    }),
}

// Model endpoints
export const modelApi = {
  getProviders: (): Promise<ModelProvider[]> =>
    api.get<ModelProvider[]>('/models/providers'),

  getProvider: (providerId: string): Promise<ModelProvider> =>
    api.get<ModelProvider>(`/models/providers/${providerId}`),

  getModels: (providerId?: string): Promise<Model[]> => {
    const url = providerId ? `/models/providers/${providerId}/models` : '/models'
    return api.get<Model[]>(url)
  },

  getModel: (modelId: string): Promise<Model> =>
    api.get<Model>(`/models/${modelId}`),

  testModel: (modelId: string, prompt: string): Promise<{
    response: string
    tokenUsage: {
      promptTokens: number
      completionTokens: number
      totalTokens: number
    }
    responseTimeMs: number
    costUsd: number
  }> =>
    api.post(`/models/${modelId}/test`, { prompt }),

  getConfigTemplates: (): Promise<ModelConfigTemplate[]> =>
    api.get<ModelConfigTemplate[]>('/models/config-templates'),

  createConfigTemplate: (data: Omit<ModelConfigTemplate, 'id' | 'usageCount'>): Promise<ModelConfigTemplate> =>
    api.post<ModelConfigTemplate>('/models/config-templates', data),

  updateConfigTemplate: (id: string, data: Partial<ModelConfigTemplate>): Promise<ModelConfigTemplate> =>
    api.patch<ModelConfigTemplate>(`/models/config-templates/${id}`, data),

  deleteConfigTemplate: (id: string): Promise<void> =>
    api.delete<void>(`/models/config-templates/${id}`),
}

// Dashboard/Analytics endpoints
export const analyticsApi = {
  getDashboardStats: (): Promise<{
    totalProjects: number
    totalEvaluations: number
    totalCost: number
    averageScore: number
    recentEvaluations: Evaluation[]
    costTrend: Array<{ date: string; cost: number }>
    scoreTrend: Array<{ date: string; score: number }>
  }> =>
    api.get('/analytics/dashboard'),

  getProjectStats: (projectId: string, timeRange?: string): Promise<{
    evaluationCount: number
    totalCost: number
    averageScore: number
    scoreHistory: Array<{ date: string; score: number }>
    costHistory: Array<{ date: string; cost: number }>
    modelUsage: Array<{ model: string; count: number; cost: number }>
  }> =>
    api.get(`/analytics/projects/${projectId}`, { params: { timeRange } }),

  getEvaluationMetrics: (evaluationId: string): Promise<Evaluation['metrics']> =>
    api.get<Evaluation['metrics']>(`/analytics/evaluations/${evaluationId}/metrics`),
}