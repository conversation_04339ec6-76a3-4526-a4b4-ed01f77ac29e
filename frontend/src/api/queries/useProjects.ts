import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { message } from 'antd'

import { PaginationParams } from '@/types'
import { Project, ProjectCreateRequest, ProjectUpdateRequest } from '@/types/project'
import { projectApi } from '../endpoints'
import { queryKeys } from '../queryClient'

// Get all projects
export const useProjects = (params?: PaginationParams) => {
  return useQuery({
    queryKey: queryKeys.projects.all(params),
    queryFn: () => projectApi.getAll(params),
    keepPreviousData: true, // Keep previous data while loading new page
  })
}

// Get project by ID
export const useProject = (id: string) => {
  return useQuery({
    queryKey: queryKeys.projects.detail(id),
    queryFn: () => projectApi.getById(id),
    enabled: !!id,
  })
}

// Get project statistics
export const useProjectStats = (id: string) => {
  return useQuery({
    queryKey: queryKeys.projects.stats(id),
    queryFn: () => projectApi.getStats(id),
    enabled: !!id,
  })
}

// Create project mutation
export const useCreateProject = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: ProjectCreateRequest) => projectApi.create(data),
    onSuccess: (newProject) => {
      // Invalidate projects list to refetch
      queryClient.invalidateQueries({ queryKey: queryKeys.projects.all() })
      
      // Add the new project to cache
      queryClient.setQueryData(queryKeys.projects.detail(newProject.id), newProject)
      
      message.success('Project created successfully')
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to create project')
    },
  })
}

// Update project mutation
export const useUpdateProject = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: ProjectUpdateRequest }) => 
      projectApi.update(id, data),
    onMutate: async ({ id, data }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.projects.detail(id) })

      // Snapshot the previous value
      const previousProject = queryClient.getQueryData(queryKeys.projects.detail(id))

      // Optimistically update to the new value
      if (previousProject) {
        queryClient.setQueryData(queryKeys.projects.detail(id), (old: Project) => ({
          ...old,
          ...data,
          updatedAt: new Date().toISOString(),
        }))
      }

      return { previousProject }
    },
    onSuccess: (updatedProject) => {
      // Update the project in cache
      queryClient.setQueryData(queryKeys.projects.detail(updatedProject.id), updatedProject)
      
      // Invalidate projects list to refetch
      queryClient.invalidateQueries({ queryKey: queryKeys.projects.all() })
      
      message.success('Project updated successfully')
    },
    onError: (error: any, { id }, context) => {
      // Rollback optimistic update on error
      if (context?.previousProject) {
        queryClient.setQueryData(queryKeys.projects.detail(id), context.previousProject)
      }
      message.error(error?.message || 'Failed to update project')
    },
  })
}

// Delete project mutation
export const useDeleteProject = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => projectApi.delete(id),
    onSuccess: (_, deletedId) => {
      // Remove project from cache
      queryClient.removeQueries({ queryKey: queryKeys.projects.detail(deletedId) })
      
      // Update projects list
      queryClient.setQueryData(
        queryKeys.projects.all(),
        (old: any) => {
          if (!old) return old
          return {
            ...old,
            data: old.data.filter((project: Project) => project.id !== deletedId),
            total: old.total - 1,
          }
        }
      )
      
      message.success('Project deleted successfully')
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to delete project')
    },
  })
}

// Duplicate project mutation
export const useDuplicateProject = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, name }: { id: string; name: string }) => 
      projectApi.duplicate(id, name),
    onSuccess: (duplicatedProject) => {
      // Add the duplicated project to cache
      queryClient.setQueryData(queryKeys.projects.detail(duplicatedProject.id), duplicatedProject)
      
      // Invalidate projects list to refetch
      queryClient.invalidateQueries({ queryKey: queryKeys.projects.all() })
      
      message.success('Project duplicated successfully')
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to duplicate project')
    },
  })
}