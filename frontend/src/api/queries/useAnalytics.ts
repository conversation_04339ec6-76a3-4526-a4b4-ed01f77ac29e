import { useQuery } from '@tanstack/react-query'

import { analyticsApi } from '../endpoints'
import { queryKeys } from '../queryClient'

// Get dashboard statistics
export const useDashboardStats = () => {
  return useQuery({
    queryKey: queryKeys.analytics.dashboard,
    queryFn: () => analyticsApi.getDashboardStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Auto-refetch every 5 minutes
  })
}

// Get project-specific analytics
export const useProjectAnalytics = (projectId: string, timeRange?: string) => {
  return useQuery({
    queryKey: queryKeys.analytics.project(projectId, timeRange),
    queryFn: () => analyticsApi.getProjectStats(projectId, timeRange),
    enabled: !!projectId,
    staleTime: 2 * 60 * 1000,
  })
}

// Hook for real-time evaluation monitoring
export const useEvaluationMonitoring = (evaluationIds: string[]) => {
  return useQuery({
    queryKey: ['evaluations', 'monitoring', evaluationIds],
    queryFn: async () => {
      // Fetch current status of all evaluations
      const evaluations = await Promise.all(
        evaluationIds.map(id => 
          fetch(`/api/evaluations/${id}`).then(res => res.json())
        )
      )
      return evaluations
    },
    enabled: evaluationIds.length > 0,
    refetchInterval: (data) => {
      // Refetch more frequently if any evaluation is running
      const hasRunningEvaluations = data?.some((evaluation: any) => 
        ['pending', 'queued', 'running'].includes(evaluation.status)
      )
      return hasRunningEvaluations ? 3000 : 30000 // 3s vs 30s
    },
    staleTime: 1000, // Keep data fresh for real-time updates
  })
}

// Hook for cost analytics with time series data
export const useCostAnalytics = (timeRange: string = '30d') => {
  return useQuery({
    queryKey: ['analytics', 'cost', timeRange],
    queryFn: async () => {
      const response = await fetch(`/api/analytics/cost?timeRange=${timeRange}`)
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Hook for performance metrics across all evaluations
export const usePerformanceMetrics = (timeRange: string = '7d') => {
  return useQuery({
    queryKey: ['analytics', 'performance', timeRange],
    queryFn: async () => {
      const response = await fetch(`/api/analytics/performance?timeRange=${timeRange}`)
      return response.json()
    },
    staleTime: 5 * 60 * 1000,
  })
}