import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { message } from 'antd'

import { PaginationParams } from '@/types'
import { Evaluation, EvaluationCreateRequest, EvaluationUpdateRequest } from '@/types/evaluation'
import { evaluationApi } from '../endpoints'
import { queryKeys } from '../queryClient'

// Get all evaluations (optionally filtered by project)
export const useEvaluations = (projectId?: string, params?: PaginationParams) => {
  return useQuery({
    queryKey: queryKeys.evaluations.all(projectId, params),
    queryFn: () => evaluationApi.getAll(projectId, params),
    keepPreviousData: true,
  })
}

// Get evaluation by ID
export const useEvaluation = (id: string) => {
  return useQuery({
    queryKey: queryKeys.evaluations.detail(id),
    queryFn: () => evaluationApi.getById(id),
    enabled: !!id,
  })
}

// Get evaluation results
export const useEvaluationResults = (id: string) => {
  return useQuery({
    queryKey: queryKeys.evaluations.results(id),
    queryFn: () => evaluationApi.getResults(id),
    enabled: !!id,
  })
}

// Get evaluation metrics
export const useEvaluationMetrics = (id: string) => {
  return useQuery({
    queryKey: queryKeys.evaluations.metrics(id),
    queryFn: () => evaluationApi.getEvaluationMetrics(id),
    enabled: !!id,
  })
}

// Create evaluation mutation
export const useCreateEvaluation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ projectId, data }: { projectId: string; data: EvaluationCreateRequest }) =>
      evaluationApi.create(projectId, data),
    onSuccess: (newEvaluation, { projectId }) => {
      // Invalidate evaluations lists
      queryClient.invalidateQueries({ queryKey: queryKeys.evaluations.all() })
      queryClient.invalidateQueries({ queryKey: queryKeys.evaluations.all(projectId) })
      
      // Add the new evaluation to cache
      queryClient.setQueryData(queryKeys.evaluations.detail(newEvaluation.id), newEvaluation)
      
      // Update project stats
      queryClient.invalidateQueries({ queryKey: queryKeys.projects.stats(projectId) })
      
      message.success('Evaluation created successfully')
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to create evaluation')
    },
  })
}

// Update evaluation mutation
export const useUpdateEvaluation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: EvaluationUpdateRequest }) =>
      evaluationApi.update(id, data),
    onMutate: async ({ id, data }) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.evaluations.detail(id) })

      const previousEvaluation = queryClient.getQueryData(queryKeys.evaluations.detail(id))

      if (previousEvaluation) {
        queryClient.setQueryData(queryKeys.evaluations.detail(id), (old: Evaluation) => ({
          ...old,
          ...data,
          updatedAt: new Date().toISOString(),
        }))
      }

      return { previousEvaluation }
    },
    onSuccess: (updatedEvaluation) => {
      queryClient.setQueryData(queryKeys.evaluations.detail(updatedEvaluation.id), updatedEvaluation)
      queryClient.invalidateQueries({ queryKey: queryKeys.evaluations.all() })
      
      message.success('Evaluation updated successfully')
    },
    onError: (error: any, { id }, context) => {
      if (context?.previousEvaluation) {
        queryClient.setQueryData(queryKeys.evaluations.detail(id), context.previousEvaluation)
      }
      message.error(error?.message || 'Failed to update evaluation')
    },
  })
}

// Start evaluation mutation
export const useStartEvaluation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => evaluationApi.start(id),
    onSuccess: (updatedEvaluation) => {
      // Update evaluation status in cache
      queryClient.setQueryData(queryKeys.evaluations.detail(updatedEvaluation.id), updatedEvaluation)
      
      // Refresh evaluations list to show updated status
      queryClient.invalidateQueries({ queryKey: queryKeys.evaluations.all() })
      
      message.success('Evaluation started successfully')
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to start evaluation')
    },
  })
}

// Stop evaluation mutation
export const useStopEvaluation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => evaluationApi.stop(id),
    onSuccess: (updatedEvaluation) => {
      queryClient.setQueryData(queryKeys.evaluations.detail(updatedEvaluation.id), updatedEvaluation)
      queryClient.invalidateQueries({ queryKey: queryKeys.evaluations.all() })
      
      message.success('Evaluation stopped successfully')
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to stop evaluation')
    },
  })
}

// Retry evaluation mutation
export const useRetryEvaluation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => evaluationApi.retry(id),
    onSuccess: (updatedEvaluation) => {
      queryClient.setQueryData(queryKeys.evaluations.detail(updatedEvaluation.id), updatedEvaluation)
      queryClient.invalidateQueries({ queryKey: queryKeys.evaluations.all() })
      
      message.success('Evaluation restarted successfully')
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to retry evaluation')
    },
  })
}

// Delete evaluation mutation
export const useDeleteEvaluation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => evaluationApi.delete(id),
    onSuccess: (_, deletedId) => {
      // Remove evaluation from cache
      queryClient.removeQueries({ queryKey: queryKeys.evaluations.detail(deletedId) })
      queryClient.removeQueries({ queryKey: queryKeys.evaluations.results(deletedId) })
      queryClient.removeQueries({ queryKey: queryKeys.evaluations.metrics(deletedId) })
      
      // Update evaluations lists
      queryClient.invalidateQueries({ queryKey: queryKeys.evaluations.all() })
      
      message.success('Evaluation deleted successfully')
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to delete evaluation')
    },
  })
}

// Export evaluation results mutation
export const useExportEvaluation = () => {
  return useMutation({
    mutationFn: ({ id, format }: { id: string; format: 'json' | 'csv' | 'pdf' }) =>
      evaluationApi.exportResults(id, format),
    onSuccess: (blob, { format }) => {
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `evaluation-results.${format}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      message.success('Export completed successfully')
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to export results')
    },
  })
}