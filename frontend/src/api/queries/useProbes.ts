import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { message } from 'antd'
import { apiClient } from '../client'
import type { 
  Probe, 
  ProbeCreateRequest, 
  ProbeUpdateRequest, 
  ProbeListResponse,
  ProbeFilters 
} from '@/types/probe'

/**
 * Query keys for probe-related queries
 */
export const probeKeys = {
  all: ['probes'] as const,
  lists: () => [...probeKeys.all, 'list'] as const,
  list: (filters: ProbeFilters) => [...probeKeys.lists(), filters] as const,
  details: () => [...probeKeys.all, 'detail'] as const,
  detail: (id: string) => [...probeKeys.details(), id] as const,
}

/**
 * Fetch all probes with optional filters
 */
export const useProbes = (filters: ProbeFilters = {}) => {
  return useQuery({
    queryKey: probeKeys.list(filters),
    queryFn: async (): Promise<ProbeListResponse> => {
      const params = new URLSearchParams()
      
      if (filters.search) params.append('search', filters.search)
      if (filters.active !== undefined) params.append('active', filters.active.toString())
      if (filters.version) params.append('version', filters.version)
      if (filters.type) params.append('type', filters.type)
      if (filters.page) params.append('page', filters.page.toString())
      if (filters.pageSize) params.append('pageSize', filters.pageSize.toString())
      
      const response = await apiClient.get(`/probes?${params.toString()}`)
      return response.data
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Fetch a single probe by ID
 */
export const useProbe = (id: string) => {
  return useQuery({
    queryKey: probeKeys.detail(id),
    queryFn: async (): Promise<Probe> => {
      const response = await apiClient.get(`/probes/${id}`)
      return response.data
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Create a new probe
 */
export const useCreateProbe = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (data: ProbeCreateRequest): Promise<Probe> => {
      const response = await apiClient.post('/probes', data)
      return response.data
    },
    onSuccess: (newProbe) => {
      // Invalidate and refetch probe lists
      queryClient.invalidateQueries({ queryKey: probeKeys.lists() })
      
      // Add the new probe to the cache
      queryClient.setQueryData(probeKeys.detail(newProbe.id), newProbe)
      
      message.success('Probe created successfully')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Failed to create probe'
      message.error(errorMessage)
    },
  })
}

/**
 * Update an existing probe
 */
export const useUpdateProbe = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: ProbeUpdateRequest }): Promise<Probe> => {
      const response = await apiClient.put(`/probes/${id}`, data)
      return response.data
    },
    onSuccess: (updatedProbe) => {
      // Update the probe in the cache
      queryClient.setQueryData(probeKeys.detail(updatedProbe.id), updatedProbe)
      
      // Invalidate probe lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: probeKeys.lists() })
      
      message.success('Probe updated successfully')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Failed to update probe'
      message.error(errorMessage)
    },
  })
}

/**
 * Delete a probe
 */
export const useDeleteProbe = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      await apiClient.delete(`/probes/${id}`)
    },
    onSuccess: (_, deletedId) => {
      // Remove the probe from the cache
      queryClient.removeQueries({ queryKey: probeKeys.detail(deletedId) })
      
      // Invalidate probe lists
      queryClient.invalidateQueries({ queryKey: probeKeys.lists() })
      
      message.success('Probe deleted successfully')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Failed to delete probe'
      message.error(errorMessage)
    },
  })
}

/**
 * Toggle probe active status
 */
export const useToggleProbeStatus = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ id, active }: { id: string; active: boolean }): Promise<Probe> => {
      const response = await apiClient.patch(`/probes/${id}/status`, { active })
      return response.data
    },
    onSuccess: (updatedProbe) => {
      // Update the probe in the cache
      queryClient.setQueryData(probeKeys.detail(updatedProbe.id), updatedProbe)
      
      // Invalidate probe lists
      queryClient.invalidateQueries({ queryKey: probeKeys.lists() })
      
      const status = updatedProbe.active ? 'activated' : 'deactivated'
      message.success(`Probe ${status} successfully`)
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Failed to update probe status'
      message.error(errorMessage)
    },
  })
}

/**
 * Duplicate a probe (create a copy with new version)
 */
export const useDuplicateProbe = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ id, newVersion }: { id: string; newVersion: string }): Promise<Probe> => {
      const response = await apiClient.post(`/probes/${id}/duplicate`, { version: newVersion })
      return response.data
    },
    onSuccess: (newProbe) => {
      // Add the new probe to the cache
      queryClient.setQueryData(probeKeys.detail(newProbe.id), newProbe)
      
      // Invalidate probe lists
      queryClient.invalidateQueries({ queryKey: probeKeys.lists() })
      
      message.success('Probe duplicated successfully')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Failed to duplicate probe'
      message.error(errorMessage)
    },
  })
}
