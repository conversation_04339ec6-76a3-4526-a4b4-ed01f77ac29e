import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { message } from 'antd'

import { ModelProvider, Model, ModelConfigTemplate } from '@/types/model'
import { modelApi } from '../endpoints'
import { queryKeys } from '../queryClient'

// Get all model providers
export const useModelProviders = () => {
  return useQuery({
    queryKey: queryKeys.models.providers,
    queryFn: () => modelApi.getProviders(),
    staleTime: 10 * 60 * 1000, // 10 minutes (providers don't change often)
  })
}

// Get specific model provider
export const useModelProvider = (providerId: string) => {
  return useQuery({
    queryKey: queryKeys.models.provider(providerId),
    queryFn: () => modelApi.getProvider(providerId),
    enabled: !!providerId,
    staleTime: 10 * 60 * 1000,
  })
}

// Get all models (optionally filtered by provider)
export const useModels = (providerId?: string) => {
  return useQuery({
    queryKey: queryKeys.models.all(providerId),
    queryFn: () => modelApi.getModels(providerId),
    staleTime: 10 * 60 * 1000, // Models don't change frequently
  })
}

// Get specific model
export const useModel = (modelId: string) => {
  return useQuery({
    queryKey: queryKeys.models.detail(modelId),
    queryFn: () => modelApi.getModel(modelId),
    enabled: !!modelId,
    staleTime: 10 * 60 * 1000,
  })
}

// Get model configuration templates
export const useModelConfigTemplates = () => {
  return useQuery({
    queryKey: queryKeys.models.configTemplates,
    queryFn: () => modelApi.getConfigTemplates(),
  })
}

// Test model mutation
export const useTestModel = () => {
  return useMutation({
    mutationFn: ({ modelId, prompt }: { modelId: string; prompt: string }) =>
      modelApi.testModel(modelId, prompt),
    onSuccess: (result) => {
      message.success(`Model test completed in ${result.responseTimeMs}ms`)
    },
    onError: (error: any) => {
      message.error(error?.message || 'Model test failed')
    },
  })
}

// Create config template mutation
export const useCreateConfigTemplate = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: Omit<ModelConfigTemplate, 'id' | 'usageCount'>) =>
      modelApi.createConfigTemplate(data),
    onSuccess: (newTemplate) => {
      // Add to templates list
      queryClient.setQueryData(
        queryKeys.models.configTemplates,
        (old: ModelConfigTemplate[]) => {
          return old ? [...old, newTemplate] : [newTemplate]
        }
      )
      
      message.success('Configuration template created successfully')
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to create configuration template')
    },
  })
}

// Update config template mutation
export const useUpdateConfigTemplate = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<ModelConfigTemplate> }) =>
      modelApi.updateConfigTemplate(id, data),
    onMutate: async ({ id, data }) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.models.configTemplates })

      const previousTemplates = queryClient.getQueryData(queryKeys.models.configTemplates)

      // Optimistically update
      queryClient.setQueryData(
        queryKeys.models.configTemplates,
        (old: ModelConfigTemplate[]) => {
          return old?.map(template => 
            template.id === id 
              ? { ...template, ...data, updatedAt: new Date().toISOString() }
              : template
          ) || []
        }
      )

      return { previousTemplates }
    },
    onSuccess: () => {
      message.success('Configuration template updated successfully')
    },
    onError: (error: any, variables, context) => {
      // Rollback on error
      if (context?.previousTemplates) {
        queryClient.setQueryData(queryKeys.models.configTemplates, context.previousTemplates)
      }
      message.error(error?.message || 'Failed to update configuration template')
    },
  })
}

// Delete config template mutation
export const useDeleteConfigTemplate = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => modelApi.deleteConfigTemplate(id),
    onSuccess: (_, deletedId) => {
      // Remove from templates list
      queryClient.setQueryData(
        queryKeys.models.configTemplates,
        (old: ModelConfigTemplate[]) => {
          return old?.filter(template => template.id !== deletedId) || []
        }
      )
      
      message.success('Configuration template deleted successfully')
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to delete configuration template')
    },
  })
}

// Hook to get available models grouped by provider
export const useModelsByProvider = () => {
  const { data: providers, isLoading: providersLoading } = useModelProviders()
  const { data: models, isLoading: modelsLoading } = useModels()

  const modelsByProvider = providers?.map(provider => ({
    provider,
    models: models?.filter(model => model.providerId === provider.id) || [],
  })) || []

  return {
    data: modelsByProvider,
    isLoading: providersLoading || modelsLoading,
  }
}

// Hook to get popular/recommended models
export const usePopularModels = () => {
  const { data: models, ...rest } = useModels()

  // Filter for popular models based on some criteria
  const popularModels = models?.filter(model => 
    model.status === 'available' && 
    model.metadata.tags?.includes('popular')
  ).slice(0, 10) || []

  return {
    data: popularModels,
    ...rest,
  }
}