import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { message } from 'antd'

import { ApiResponse, ApiError } from '@/types'
import { tokenStore } from '@/auth/tokenStore'

// Create axios instance with base configuration
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: '/api',
    timeout: 30000, // 30 seconds
    headers: {
      'Content-Type': 'application/json',
    },
    withCredentials: true, // Important for httpOnly cookies
  })

  // Request interceptor - add auth token
  client.interceptors.request.use(
    (config) => {
      const token = tokenStore.getAccessToken()
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // Response interceptor - handle token refresh and errors
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      return response
    },
    async (error) => {
      const originalRequest = error.config

      // Handle 401 errors (token expired)
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true

        try {
          // Try to refresh the token
          const newToken = await refreshTokenRequest()

          if (newToken) {
            tokenStore.setAccessToken(newToken)
            // Retry the original request with new token
            originalRequest.headers.Authorization = `Bearer ${newToken}`
            return client(originalRequest)
          }
        } catch (refreshError) {
          // Refresh failed, redirect to login
          console.error('Token refresh failed:', refreshError)
          tokenStore.clearToken()
          // Redirect to login page
          window.location.href = '/login'
          return Promise.reject(refreshError)
        }
      }

      // Handle other HTTP errors
      const apiError: ApiError = {
        message: error.response?.data?.message || error.message || 'An error occurred',
        status: error.response?.status || 0,
        errors: error.response?.data?.errors,
      }

      // Show error message for 4xx and 5xx errors (except 401 which is handled above)
      if (error.response?.status !== 401) {
        message.error(apiError.message)
      }

      return Promise.reject(apiError)
    }
  )

  return client
}

// Separate function for token refresh to avoid circular dependencies
const refreshTokenRequest = async (): Promise<string | null> => {
  try {
    const response = await axios.post(
      '/api/auth/refresh',
      {},
      { withCredentials: true }
    )

    return response.data.accessToken
  } catch (error) {
    console.error('Token refresh request failed:', error)
    throw error
  }
}

// Create the API client instance
export const apiClient = createApiClient()

// Utility function for making typed API calls
export const makeApiCall = async <T>(
  config: AxiosRequestConfig
): Promise<T> => {
  try {
    const response = await apiClient<ApiResponse<T>>(config)
    return response.data.data
  } catch (error) {
    throw error
  }
}

// Common API methods
export const api = {
  get: <T>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    makeApiCall<T>({ method: 'GET', url, ...config }),

  post: <T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> =>
    makeApiCall<T>({ method: 'POST', url, data, ...config }),

  put: <T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> =>
    makeApiCall<T>({ method: 'PUT', url, data, ...config }),

  patch: <T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> =>
    makeApiCall<T>({ method: 'PATCH', url, data, ...config }),

  delete: <T>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    makeApiCall<T>({ method: 'DELETE', url, ...config }),
}