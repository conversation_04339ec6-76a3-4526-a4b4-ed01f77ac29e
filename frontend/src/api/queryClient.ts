import { QueryClient, DefaultOptions } from '@tanstack/react-query'
import { message } from 'antd'

// Default options for React Query
const queryConfig: DefaultOptions = {
  queries: {
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors (client errors)
      if (error?.status >= 400 && error?.status < 500) {
        return false
      }
      // Retry up to 2 times for other errors
      return failureCount < 2
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
  },
  mutations: {
    onError: (error: any) => {
      // Global error handler for mutations
      console.error('Mutation error:', error)
      if (error?.message) {
        message.error(error.message)
      }
    },
  },
}

export const queryClient = new QueryClient({
  defaultOptions: queryConfig,
})

// Query keys factory for better organization and type safety
export const queryKeys = {
  // Auth
  auth: {
    profile: ['auth', 'profile'] as const,
  },
  
  // Tenant
  tenant: {
    current: ['tenant', 'current'] as const,
    settings: ['tenant', 'settings'] as const,
  },

  // Projects
  projects: {
    all: (params?: Record<string, unknown>) => ['projects', params] as const,
    detail: (id: string) => ['projects', 'detail', id] as const,
    stats: (id: string) => ['projects', 'stats', id] as const,
  },

  // Evaluations
  evaluations: {
    all: (projectId?: string, params?: Record<string, unknown>) => 
      ['evaluations', projectId, params] as const,
    detail: (id: string) => ['evaluations', 'detail', id] as const,
    results: (id: string) => ['evaluations', 'results', id] as const,
    metrics: (id: string) => ['evaluations', 'metrics', id] as const,
  },

  // Models
  models: {
    providers: ['models', 'providers'] as const,
    provider: (providerId: string) => ['models', 'provider', providerId] as const,
    all: (providerId?: string) => ['models', 'list', providerId] as const,
    detail: (modelId: string) => ['models', 'detail', modelId] as const,
    configTemplates: ['models', 'config-templates'] as const,
  },

  // Analytics
  analytics: {
    dashboard: ['analytics', 'dashboard'] as const,
    project: (projectId: string, timeRange?: string) => 
      ['analytics', 'project', projectId, timeRange] as const,
  },
} as const