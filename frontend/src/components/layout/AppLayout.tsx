import { useState, useEffect } from 'react'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import { ProLayout, ProConfigProvider } from '@ant-design/pro-components'
import { App, ConfigProvider, theme } from 'antd'
import {
  DashboardOutlined,
  ProjectOutlined,
  ExperimentOutlined,
  RobotOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
} from '@ant-design/icons'

import { useAuth } from '@/auth/useAuth'
import { useAppStore, useSidebarState, useTheme } from '@/stores/appStore'
import { HeaderContent } from './HeaderContent'

const menuData = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    icon: <DashboardOutlined />,
  },
  {
    path: '/projects',
    name: 'Projects',
    icon: <ProjectOutlined />,
  },
  {
    path: '/evaluations',
    name: 'Evaluations',
    icon: <ExperimentOutlined />,
  },
  {
    path: '/models',
    name: 'Models',
    icon: <RobotOutlined />,
  },
  {
    path: '/settings',
    name: 'Settings',
    icon: <SettingOutlined />,
  },
]

export const AppLayout = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout } = useAuth()
  const { theme: themeMode } = useTheme()
  const { collapsed, toggle } = useSidebarState()
  const setBreadcrumbs = useAppStore((state) => state.setBreadcrumbs)

  const [pathname, setPathname] = useState(location.pathname)

  // Update breadcrumbs based on current path
  useEffect(() => {
    const generateBreadcrumbs = (path: string) => {
      const segments = path.split('/').filter(Boolean)
      const breadcrumbs = []

      let currentPath = ''
      for (const segment of segments) {
        currentPath += `/${segment}`
        
        // Find menu item for this path
        const menuItem = menuData.find(item => item.path === currentPath)
        if (menuItem) {
          breadcrumbs.push({
            title: menuItem.name,
            path: currentPath,
          })
        } else {
          // For dynamic routes, capitalize the segment
          breadcrumbs.push({
            title: segment.charAt(0).toUpperCase() + segment.slice(1),
            path: currentPath,
          })
        }
      }

      return breadcrumbs
    }

    setBreadcrumbs(generateBreadcrumbs(location.pathname))
  }, [location.pathname, setBreadcrumbs])

  const handleMenuClick = (path: string) => {
    setPathname(path)
    navigate(path)
  }

  const handleLogout = async () => {
    await logout()
    navigate('/login')
  }

  const rightContentRender = () => <HeaderContent />

  const avatarProps = {
    src: user?.avatar,
    title: user?.displayName || user?.email,
    size: 'small' as const,
  }

  // Determine theme algorithm
  const getThemeAlgorithm = () => {
    if (themeMode === 'dark') {
      return theme.darkAlgorithm
    }
    if (themeMode === 'light') {
      return theme.defaultAlgorithm
    }
    
    // Auto mode - detect system preference
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return theme.darkAlgorithm
    }
    return theme.defaultAlgorithm
  }

  return (
    <ConfigProvider
      theme={{
        algorithm: getThemeAlgorithm(),
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <ProConfigProvider hashed={false}>
        <App>
          <ProLayout
            title='SACRA2'
            logo='/logo.svg'
            layout='mix'
            fixedHeader
            fixSiderbar
            collapsed={collapsed}
            onCollapse={toggle}
            location={{ pathname }}
            menuItemRender={(item, dom) => (
              <div onClick={() => handleMenuClick(item.path || '/')}>
                {dom}
              </div>
            )}
            breadcrumbRender={(routers = []) => [
              {
                path: '/',
                breadcrumbName: 'Home',
              },
              ...routers,
            ]}
            route={{
              path: '/',
              children: menuData,
            }}
            avatarProps={avatarProps}
            actionsRender={() => [rightContentRender()]}
            menuExtraRender={({ collapsed: menuCollapsed }) =>
              !menuCollapsed && (
                <div style={{ padding: '0 16px' }}>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                      padding: '8px 0',
                      borderTop: '1px solid #f0f0f0',
                      marginTop: 8,
                      fontSize: '12px',
                      color: '#999',
                    }}
                  >
                    <UserOutlined />
                    {user?.displayName || user?.email}
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                      padding: '8px 0',
                      cursor: 'pointer',
                      fontSize: '12px',
                      color: '#999',
                    }}
                    onClick={handleLogout}
                  >
                    <LogoutOutlined />
                    Logout
                  </div>
                </div>
              )
            }
            onMenuHeaderClick={() => navigate('/dashboard')}
            menuProps={{
              selectedKeys: [pathname],
            }}
          >
            <div
              style={{
                minHeight: 'calc(100vh - 64px)',
                padding: 24,
                backgroundColor: 'var(--ant-color-bg-container)',
              }}
            >
              <Outlet />
            </div>
          </ProLayout>
        </App>
      </ProConfigProvider>
    </ConfigProvider>
  )
}