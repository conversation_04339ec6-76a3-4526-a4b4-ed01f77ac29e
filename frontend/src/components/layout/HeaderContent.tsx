import { Badge, Dropdown, <PERSON>, <PERSON>lt<PERSON>, But<PERSON> } from 'antd'
import {
  <PERSON>Outlined,
  QuestionCircleOutlined,
  Gith<PERSON>Outlined,
  BulbOutlined,
  MoonOutlined,
  SunOutlined,
} from '@ant-design/icons'
import type { MenuProps } from 'antd'

import { useNotificationStore, useUnreadNotifications } from '@/stores/notificationStore'
import { useTheme } from '@/stores/appStore'
import { NotificationDropdown } from './NotificationDropdown'

export const HeaderContent = () => {
  const { theme, setTheme } = useTheme()
  const unreadNotifications = useUnreadNotifications()
  const markAllAsRead = useNotificationStore((state) => state.markAllAsRead)

  const handleThemeToggle = () => {
    const nextTheme = theme === 'light' ? 'dark' : theme === 'dark' ? 'auto' : 'light'
    setTheme(nextTheme)
  }

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return <SunOutlined />
      case 'dark':
        return <MoonOutlined />
      default:
        return <BulbOutlined />
    }
  }

  const getThemeTooltip = () => {
    switch (theme) {
      case 'light':
        return 'Switch to Dark Mode'
      case 'dark':
        return 'Switch to Auto Mode'
      default:
        return 'Switch to Light Mode'
    }
  }

  const helpMenuItems: MenuProps['items'] = [
    {
      key: 'docs',
      label: 'Documentation',
      icon: <QuestionCircleOutlined />,
      onClick: () => window.open('https://docs.sacra2.ai', '_blank'),
    },
    {
      key: 'api',
      label: 'API Reference',
      onClick: () => window.open('https://api.sacra2.ai/docs', '_blank'),
    },
    {
      type: 'divider',
    },
    {
      key: 'github',
      label: 'GitHub',
      icon: <GithubOutlined />,
      onClick: () => window.open('https://github.com/sacra2-ai/sacra2', '_blank'),
    },
    {
      key: 'support',
      label: 'Support',
      onClick: () => window.open('mailto:<EMAIL>'),
    },
  ]

  return (
    <Space size='middle'>
      {/* Theme Toggle */}
      <Tooltip title={getThemeTooltip()}>
        <Button
          type='text'
          icon={getThemeIcon()}
          onClick={handleThemeToggle}
          style={{ border: 'none' }}
        />
      </Tooltip>

      {/* Notifications */}
      <Dropdown
        dropdownRender={() => <NotificationDropdown />}
        trigger={['click']}
        placement='bottomRight'
      >
        <Badge count={unreadNotifications.length} size='small'>
          <Button
            type='text'
            icon={<BellOutlined />}
            style={{ border: 'none' }}
          />
        </Badge>
      </Dropdown>

      {/* Help Menu */}
      <Dropdown menu={{ items: helpMenuItems }} trigger={['click']}>
        <Button
          type='text'
          icon={<QuestionCircleOutlined />}
          style={{ border: 'none' }}
        />
      </Dropdown>
    </Space>
  )
}