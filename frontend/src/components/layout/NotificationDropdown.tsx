import { List, Button, Typography, Empty, Space, Tag, Divider } from 'antd'
import { 
  CheckOutlined, 
  ClearOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  CloseOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

import { 
  useNotificationStore, 
  useRecentNotifications,
  type Notification 
} from '@/stores/notificationStore'

dayjs.extend(relativeTime)

const { Text } = Typography

const getNotificationIcon = (type: Notification['type']) => {
  switch (type) {
    case 'success':
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />
    case 'error':
      return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
    case 'warning':
      return <ExclamationCircleOutlined style={{ color: '#faad14' }} />
    case 'info':
      return <InfoCircleOutlined style={{ color: '#1890ff' }} />
    default:
      return <InfoCircleOutlined />
  }
}

const getNotificationColor = (type: Notification['type']) => {
  switch (type) {
    case 'success':
      return '#52c41a'
    case 'error':
      return '#ff4d4f'
    case 'warning':
      return '#faad14'
    case 'info':
      return '#1890ff'
    default:
      return '#d9d9d9'
  }
}

export const NotificationDropdown = () => {
  const recentNotifications = useRecentNotifications(10)
  const { markAsRead, markAllAsRead, removeNotification, clearAll, unreadCount } = 
    useNotificationStore()

  const handleMarkAsRead = (id: string, e: React.MouseEvent) => {
    e.stopPropagation()
    markAsRead(id)
  }

  const handleRemove = (id: string, e: React.MouseEvent) => {
    e.stopPropagation()
    removeNotification(id)
  }

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id)
    }
  }

  return (
    <div style={{ width: 400, maxHeight: 500, backgroundColor: 'white' }}>
      {/* Header */}
      <div
        style={{
          padding: '12px 16px',
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Space>
          <Text strong>Notifications</Text>
          {unreadCount > 0 && (
            <Tag color='blue' size='small'>
              {unreadCount} new
            </Tag>
          )}
        </Space>
        
        <Space size='small'>
          {unreadCount > 0 && (
            <Button 
              type='text' 
              size='small' 
              icon={<CheckOutlined />}
              onClick={markAllAsRead}
            >
              Mark all read
            </Button>
          )}
          {recentNotifications.length > 0 && (
            <Button 
              type='text' 
              size='small' 
              icon={<ClearOutlined />}
              onClick={clearAll}
            >
              Clear all
            </Button>
          )}
        </Space>
      </div>

      {/* Notifications List */}
      <div style={{ maxHeight: 400, overflowY: 'auto' }}>
        {recentNotifications.length === 0 ? (
          <div style={{ padding: 24 }}>
            <Empty
              description='No notifications'
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        ) : (
          <List
            size='small'
            dataSource={recentNotifications}
            renderItem={(notification) => (
              <List.Item
                key={notification.id}
                style={{
                  padding: '12px 16px',
                  cursor: 'pointer',
                  backgroundColor: notification.read ? 'transparent' : '#f6ffed',
                  borderLeft: `3px solid ${getNotificationColor(notification.type)}`,
                }}
                onClick={() => handleNotificationClick(notification)}
                actions={[
                  !notification.read && (
                    <Button
                      type='text'
                      size='small'
                      icon={<CheckOutlined />}
                      onClick={(e) => handleMarkAsRead(notification.id, e)}
                      title='Mark as read'
                    />
                  ),
                  <Button
                    type='text'
                    size='small'
                    icon={<CloseOutlined />}
                    onClick={(e) => handleRemove(notification.id, e)}
                    title='Remove'
                  />
                ].filter(Boolean)}
              >
                <List.Item.Meta
                  avatar={getNotificationIcon(notification.type)}
                  title={
                    <Space>
                      <Text strong={!notification.read}>{notification.title}</Text>
                      {!notification.read && (
                        <div
                          style={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: '#1890ff',
                          }}
                        />
                      )}
                    </Space>
                  }
                  description={
                    <div>
                      <Text type='secondary'>{notification.message}</Text>
                      <br />
                      <Space style={{ marginTop: 4 }}>
                        <ClockCircleOutlined style={{ fontSize: 12 }} />
                        <Text type='secondary' style={{ fontSize: 12 }}>
                          {dayjs(notification.timestamp).fromNow()}
                        </Text>
                      </Space>
                      {notification.actions && notification.actions.length > 0 && (
                        <div style={{ marginTop: 8 }}>
                          <Space size='small'>
                            {notification.actions.map((action, index) => (
                              <Button
                                key={index}
                                size='small'
                                type='link'
                                onClick={(e) => {
                                  e.stopPropagation()
                                  action.action()
                                }}
                              >
                                {action.label}
                              </Button>
                            ))}
                          </Space>
                        </div>
                      )}
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </div>

      {/* Footer */}
      {recentNotifications.length > 10 && (
        <>
          <Divider style={{ margin: 0 }} />
          <div style={{ padding: 12, textAlign: 'center' }}>
            <Button type='link' size='small'>
              View all notifications
            </Button>
          </div>
        </>
      )}
    </div>
  )
}