import { useState } from 'react'
import { <PERSON> } from 'react-router-dom';
import { ProTable, ProColumns } from '@ant-design/pro-components'
import { Button, Space, Tag, Modal, Input, Card, Descriptions } from 'antd'
import {
  PlayCircleOutlined,
  StarOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons'
import dayjs from 'dayjs'

import { Model } from '@/types/model'
import { useModels, useModelProviders, useTestModel } from '@/api/queries/useModels'

interface ModelsTableProps {
  providerId?: string
  selectable?: boolean
  onModelSelect?: (model: Model) => void
  selectedModelId?: string
}

export const ModelsTable = ({ 
  providerId,
  selectable = false,
  onModelSelect,
  selectedModelId 
}: ModelsTableProps) => {
  const [selectedModel, setSelectedModel] = useState<Model | null>(null)
  const [testModalVisible, setTestModalVisible] = useState(false)
  const [testPrompt, setTestPrompt] = useState('Hello! How are you today?')
  
  const { data: providers } = useModelProviders()
  const testModelMutation = useTestModel()

  const handleTestModel = (model: Model) => {
    setSelectedModel(model)
    setTestModalVisible(true)
  }

  const handleRunTest = () => {
    if (selectedModel && testPrompt.trim()) {
      testModelMutation.mutate({
        modelId: selectedModel.id,
        prompt: testPrompt,
      })
      setTestModalVisible(false)
    }
  }

  const getProviderName = (providerId: string) => {
    return providers?.find(p => p.id === providerId)?.displayName || providerId
  }

  const formatPricing = (model: Model) => {
    const { pricing } = model
    if (pricing.inputTokenPriceUsd === 0 && pricing.outputTokenPriceUsd === 0) {
      return 'Free'
    }
    
    return `$${pricing.inputTokenPriceUsd.toFixed(6)}/$${pricing.outputTokenPriceUsd.toFixed(6)} per 1K tokens`
  }

  const columns: ProColumns<Model>[] = [
    {
      title: 'Model',
      key: 'model',
      width: 250,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>
            {record.displayName}
          </div>
          <div style={{ fontSize: 12, color: '#666' }}>
            {getProviderName(record.providerId)}
          </div>
          {record.metadata.tags?.includes('popular') && (
            <Tag icon={<StarOutlined />} color='gold'>
              Popular
            </Tag>
          )}
          {record.status === 'beta' && (
            <Tag color='blue'>
              Beta
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: 'Capabilities',
      key: 'capabilities',
      width: 200,
      search: false,
      render: (_, record) => (
        <Space direction='vertical' size='small'>
          <div>
            <Tag>
              {record.capabilities.contextLength.toLocaleString()} context
            </Tag>
          </div>
          <div>
            {record.capabilities.inputTypes.map(type => (
              <Tag key={type} color='blue'>
                {type}
              </Tag>
            ))}
          </div>
          {record.capabilities.supportsTools && (
            <Tag color='green'>Tools</Tag>
          )}
          {record.capabilities.supportsStreaming && (
            <Tag color='purple'>Streaming</Tag>
          )}
        </Space>
      ),
    },
    {
      title: 'Pricing',
      key: 'pricing',
      width: 180,
      search: false,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: 12 }}>{formatPricing(record)}</div>
          {record.pricing.inputTokenPriceUsd > 0 && (
            <div style={{ fontSize: 11, color: '#666', marginTop: 2 }}>
              In: ${record.pricing.inputTokenPriceUsd.toFixed(6)}/1K
              <br />
              Out: ${record.pricing.outputTokenPriceUsd.toFixed(6)}/1K
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      valueType: 'select',
      valueEnum: {
        available: { text: 'Available', status: 'Success' },
        deprecated: { text: 'Deprecated', status: 'Warning' },
        beta: { text: 'Beta', status: 'Processing' },
        coming_soon: { text: 'Coming Soon', status: 'Default' },
      },
    },
    {
      title: 'Released',
      dataIndex: ['metadata', 'releaseDate'],
      key: 'releaseDate',
      width: 120,
      search: false,
      render: (_, record) => {
        const date = record.metadata?.releaseDate;
        return date ? dayjs(date).format('MMM YYYY') : '-';
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      search: false,
      fixed: 'right',
      render: (_, record) => (
        <Space size='small'>
          <Link to={`/models/${record.id}`}>
            <Button
              size='small'
              icon={<InfoCircleOutlined />}
            >
              Details
            </Button>
          </Link>
          {record.status === 'available' && (
            <Button
              size='small'
              icon={<PlayCircleOutlined />}
              onClick={() => handleTestModel(record)}
            >
              Test
            </Button>
          )}
          {selectable && (
            <Button
              size='small'
              type={selectedModelId === record.id ? 'primary' : 'default'}
              onClick={() => onModelSelect?.(record)}
            >
              {selectedModelId === record.id ? 'Selected' : 'Select'}
            </Button>
          )}
        </Space>
      ),
    },
  ]

  return (
    <>
      <ProTable<Model>
        columns={columns}
        cardBordered
        request={async (params) => {
          const { data, isLoading, error } = useModels(providerId)

          if (error) {
            throw error
          }

          // Client-side filtering for search
          let filteredData = data || []
          if (params.model) {
            filteredData = filteredData.filter(model =>
              model.displayName.toLowerCase().includes(params.model.toLowerCase()) ||
              model.name.toLowerCase().includes(params.model.toLowerCase())
            )
          }

          return {
            data: filteredData,
            success: !isLoading,
            total: filteredData.length,
          }
        }}
        rowKey='id'
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
        }}
        pagination={{
          pageSize: 20,
          showQuickJumper: true,
          showSizeChanger: true,
        }}
        dateFormatter='string'
        headerTitle='Available Models'
        scroll={{ x: 1000 }}
      />

      {/* Test Model Modal */}
      <Modal
        title={`Test ${selectedModel?.displayName}`}
        open={testModalVisible}
        onOk={handleRunTest}
        onCancel={() => setTestModalVisible(false)}
        confirmLoading={testModelMutation.isPending}
      >
        <Space direction='vertical' size='middle' style={{ width: '100%' }}>
          <div>
            <label>Test Prompt:</label>
            <Input.TextArea
              value={testPrompt}
              onChange={(e) => setTestPrompt(e.target.value)}
              rows={4}
              placeholder='Enter a prompt to test the model...'
            />
          </div>
          
          {testModelMutation.data && (
            <Card title='Response' size='small'>
              <pre style={{ whiteSpace: 'pre-wrap', fontSize: 12 }}>
                {testModelMutation.data.response}
              </pre>
              <Descriptions column={2} size='small' style={{ marginTop: 16 }}>
                <Descriptions.Item label='Response Time'>
                  {testModelMutation.data.responseTimeMs}ms
                </Descriptions.Item>
                <Descriptions.Item label='Cost'>
                  ${testModelMutation.data.costUsd.toFixed(6)}
                </Descriptions.Item>
                <Descriptions.Item label='Total Tokens'>
                  {testModelMutation.data.tokenUsage.totalTokens}
                </Descriptions.Item>
                <Descriptions.Item label='Prompt Tokens'>
                  {testModelMutation.data.tokenUsage.promptTokens}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          )}
        </Space>
      </Modal>
    </>
  )
}