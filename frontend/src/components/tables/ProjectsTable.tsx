import { useRef } from 'react'
import { ProTable, ActionType, ProColumns } from '@ant-design/pro-components'
import { Button, Space, Tag, Dropdown, Modal } from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  MoreOutlined,
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import type { MenuProps } from 'antd'

import { Project, ProjectStatus } from '@/types/project'
import { useProjects, useDeleteProject, useDuplicateProject } from '@/api/queries/useProjects'

const statusColors: Record<ProjectStatus, string> = {
  active: 'green',
  archived: 'orange',
  draft: 'blue',
}

const statusLabels: Record<ProjectStatus, string> = {
  active: 'Active',
  archived: 'Archived',
  draft: 'Draft',
}

interface ProjectsTableProps {
  showActions?: boolean
  onCreateProject?: () => void
}

export const ProjectsTable = ({ 
  showActions = true, 
  onCreateProject 
}: ProjectsTableProps) => {
  const navigate = useNavigate()
  const actionRef = useRef<ActionType>()
  
  const deleteProjectMutation = useDeleteProject()
  const duplicateProjectMutation = useDuplicateProject()

  const handleView = (project: Project) => {
    navigate(`/projects/${project.id}`)
  }

  const handleEdit = (project: Project) => {
    navigate(`/projects/${project.id}/edit`)
  }

  const handleDelete = (project: Project) => {
    Modal.confirm({
      title: 'Delete Project',
      content: `Are you sure you want to delete "${project.name}"? This action cannot be undone.`,
      okText: 'Delete',
      okButtonProps: { danger: true },
      onOk: async () => {
        await deleteProjectMutation.mutateAsync(project.id)
        actionRef.current?.reload()
      },
    })
  }

  const handleDuplicate = async (project: Project) => {
    await duplicateProjectMutation.mutateAsync({
      id: project.id,
      name: `${project.name} (Copy)`,
    })
    actionRef.current?.reload()
  }

  const getRowActions = (record: Project): MenuProps['items'] => [
    {
      key: 'view',
      label: 'View Details',
      icon: <EyeOutlined />,
      onClick: () => handleView(record),
    },
    {
      key: 'edit',
      label: 'Edit',
      icon: <EditOutlined />,
      onClick: () => handleEdit(record),
    },
    {
      key: 'duplicate',
      label: 'Duplicate',
      icon: <CopyOutlined />,
      onClick: () => handleDuplicate(record),
    },
    {
      type: 'divider',
    },
    {
      key: 'delete',
      label: 'Delete',
      icon: <DeleteOutlined />,
      danger: true,
      onClick: () => handleDelete(record),
    },
  ]

  const columns: ProColumns<Project>[] = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      render: (_, record) => (
        <Button
          type='link'
          style={{ padding: 0, height: 'auto', textAlign: 'left' }}
          onClick={() => handleView(record)}
        >
          {record.name}
        </Button>
      ),
      width: 200,
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      search: false,
      width: 300,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      valueType: 'select',
      valueEnum: {
        active: { text: 'Active', status: 'Success' },
        archived: { text: 'Archived', status: 'Warning' },
        draft: { text: 'Draft', status: 'Processing' },
      },
      render: (_, record) => (
        <Tag color={statusColors[record.status]}>
          {statusLabels[record.status]}
        </Tag>
      ),
    },
    {
      title: 'Evaluations',
      dataIndex: 'evaluationCount',
      key: 'evaluationCount',
      width: 120,
      search: false,
      sorter: true,
      render: (count) => count || 0,
    },
    {
      title: 'Last Evaluation',
      dataIndex: 'lastEvaluationAt',
      key: 'lastEvaluationAt',
      width: 180,
      search: false,
      sorter: true,
      render: (_, record) => record.lastEvaluationAt ? dayjs(record.lastEvaluationAt).format('MMM D, YYYY HH:mm') : '-',
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      search: false,
      sorter: true,
      render: (_, record) => dayjs(record.createdAt).format('MMM D, YYYY HH:mm'),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      search: false,
      fixed: 'right',
      render: (_, record) => (
        <Space size='small'>
          <Button
            type='text'
            size='small'
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          />
          <Dropdown
            menu={{ items: getRowActions(record) ?? [] }}
            trigger={['click']}
            placement='bottomRight'
          >
            <Button 
              type='text' 
              size='small' 
              icon={<MoreOutlined />} 
            />
          </Dropdown>
        </Space>
      ),
    },
  ]

  // Remove actions column if not needed
  if (!showActions) {
    columns.pop()
  }

  return (
    <ProTable<Project>
      columns={columns}
      actionRef={actionRef}
      cardBordered
      request={async (params, sort) => {
        const queryParams: Record<string, any> = {
          page: params.current,
          pageSize: params.pageSize,
        }

        const sortBy = Object.keys(sort || {})[0]
        const sortOrder = Object.values(sort || {})[0]

        if (sortBy && sortOrder) {
          queryParams.sortBy = sortBy
          queryParams.sortOrder = sortOrder as 'asc' | 'desc'
        }

        const { data, isLoading, error } = useProjects(queryParams)

        if (error) {
          throw error
        }

        return {
          data: data?.data || [],
          success: !isLoading,
          total: data?.total || 0,
        }
      }}
      editable={{
        type: 'multiple',
      }}
      columnsState={{
        persistenceKey: 'pro-table-projects',
        persistenceType: 'localStorage',
      }}
      rowKey='id'
      search={{
        labelWidth: 'auto',
        defaultCollapsed: false,
      }}
      form={{
        syncToUrl: (values, type) => {
          if (type === 'get') {
            return {
              ...values,
              created_at: [values.startTime, values.endTime],
            }
          }
          return values
        },
      }}
      pagination={{
        pageSize: 20,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => 
          `${range[0]}-${range[1]} of ${total} projects`,
      }}
      dateFormatter='string'
      headerTitle='Projects'
      toolBarRender={showActions ? () => [
        <Button
          key='create'
          icon={<PlusOutlined />}
          type='primary'
          onClick={onCreateProject}
        >
          New Project
        </Button>,
      ] : false}
      scroll={{ x: 1200 }}
    />
  )
}