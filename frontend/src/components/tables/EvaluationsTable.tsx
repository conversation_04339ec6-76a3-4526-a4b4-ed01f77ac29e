import { useRef } from 'react'
import { ProTable, ActionType, ProColumns } from '@ant-design/pro-components'
import { Button, Space, Tag, Dropdown, Progress, Popconfirm, Tooltip } from 'antd'
import {
  PlusOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  MoreOutlined,
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import type { MenuProps } from 'antd'

import { Evaluation, EvaluationStatus } from '@/types/evaluation'
import { 
  useEvaluations, 
  useStartEvaluation,
  useStopEvaluation,
  useRetryEvaluation,
  useDeleteEvaluation,
  useExportEvaluation,
} from '@/api/queries/useEvaluations'

const statusColors: Record<EvaluationStatus, string> = {
  pending: 'blue',
  queued: 'cyan',
  running: 'processing',
  completed: 'success',
  failed: 'error',
  cancelled: 'default',
}

const statusLabels: Record<EvaluationStatus, string> = {
  pending: 'Pending',
  queued: 'Queued',
  running: 'Running',
  completed: 'Completed',
  failed: 'Failed',
  cancelled: 'Cancelled',
}

interface EvaluationsTableProps {
  projectId?: string
  showActions?: boolean
  onCreateEvaluation?: () => void
}

export const EvaluationsTable = ({ 
  projectId,
  showActions = true, 
  onCreateEvaluation 
}: EvaluationsTableProps) => {
  const navigate = useNavigate()
  const actionRef = useRef<ActionType>()
  
  const startEvaluationMutation = useStartEvaluation()
  const stopEvaluationMutation = useStopEvaluation()
  const retryEvaluationMutation = useRetryEvaluation()
  const deleteEvaluationMutation = useDeleteEvaluation()
  const exportEvaluationMutation = useExportEvaluation()

  const handleView = (evaluation: Evaluation) => {
    navigate(`/evaluations/${evaluation.id}`)
  }

  const handleStart = async (evaluation: Evaluation) => {
    await startEvaluationMutation.mutateAsync(evaluation.id)
    actionRef.current?.reload()
  }

  const handleStop = async (evaluation: Evaluation) => {
    await stopEvaluationMutation.mutateAsync(evaluation.id)
    actionRef.current?.reload()
  }

  const handleRetry = async (evaluation: Evaluation) => {
    await retryEvaluationMutation.mutateAsync(evaluation.id)
    actionRef.current?.reload()
  }

  const handleDelete = async (evaluation: Evaluation) => {
    await deleteEvaluationMutation.mutateAsync(evaluation.id)
    actionRef.current?.reload()
  }

  const handleExport = (evaluation: Evaluation, format: 'json' | 'csv' | 'pdf') => {
    exportEvaluationMutation.mutate({
      id: evaluation.id,
      format,
    })
  }

  const getActionButton = (record: Evaluation) => {
    switch (record.status) {
      case 'pending':
      case 'queued':
        return (
          <Button
            size='small'
            icon={<PlayCircleOutlined />}
            onClick={() => handleStart(record)}
            disabled={startEvaluationMutation.isPending}
          >
            Start
          </Button>
        )
      case 'running':
        return (
          <Button
            size='small'
            icon={<PauseCircleOutlined />}
            onClick={() => handleStop(record)}
            disabled={stopEvaluationMutation.isPending}
          >
            Stop
          </Button>
        )
      case 'failed':
      case 'cancelled':
        return (
          <Button
            size='small'
            icon={<ReloadOutlined />}
            onClick={() => handleRetry(record)}
            disabled={retryEvaluationMutation.isPending}
          >
            Retry
          </Button>
        )
      default:
        return null
    }
  }

  const getRowActions = (record: Evaluation): MenuProps['items'] => {
    const actions = [
      {
        key: 'view',
        label: 'View Details',
        icon: <EyeOutlined />,
        onClick: () => handleView(record),
      },
    ]

    if (record.status === 'completed') {
      actions.push(
        {
          key: 'export',
          label: 'Export Results',
          icon: <DownloadOutlined />,
          children: [
            {
              key: 'export-json',
              label: 'JSON',
              onClick: () => handleExport(record, 'json'),
            },
            {
              key: 'export-csv',
              label: 'CSV',
              onClick: () => handleExport(record, 'csv'),
            },
            {
              key: 'export-pdf',
              label: 'PDF',
              onClick: () => handleExport(record, 'pdf'),
            },
          ],
        }
      )
    }

    actions.push(
      {
        type: 'divider',
      },
      {
        key: 'delete',
        label: 'Delete',
        icon: <DeleteOutlined />,
        danger: true,
        onClick: () => handleDelete(record),
      }
    )

    return actions
  }

  const getProgressPercent = (evaluation: Evaluation): number => {
    if (!evaluation.results) return 0
    
    const { summary } = evaluation.results
    if (!summary.totalProbes) return 0
    
    const completedProbes = summary.passedProbes + summary.failedProbes
    return Math.round((completedProbes / summary.totalProbes) * 100)
  }

  const columns: ProColumns<Evaluation>[] = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      render: (_, record) => (
        <Button
          type='link'
          style={{ padding: 0, height: 'auto', textAlign: 'left' }}
          onClick={() => handleView(record)}
        >
          {record.name}
        </Button>
      ),
      width: 200,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      valueType: 'select',
      valueEnum: {
        pending: { text: 'Pending', status: 'Processing' },
        queued: { text: 'Queued', status: 'Processing' },
        running: { text: 'Running', status: 'Processing' },
        completed: { text: 'Completed', status: 'Success' },
        failed: { text: 'Failed', status: 'Error' },
        cancelled: { text: 'Cancelled', status: 'Default' },
      },
      render: (_, record) => (
        <Tag color={statusColors[record.status]}>
          {statusLabels[record.status]}
        </Tag>
      ),
    },
    {
      title: 'Progress',
      key: 'progress',
      width: 150,
      search: false,
      render: (_, record) => {
        if (record.status === 'running') {
          const percent = getProgressPercent(record)
          return <Progress percent={percent} size='small' />
        }
        if (record.status === 'completed') {
          return <Progress percent={100} size='small' status='success' />
        }
        if (record.status === 'failed') {
          const percent = getProgressPercent(record)
          return <Progress percent={percent} size='small' status='exception' />
        }
        return '-'
      },
    },
    {
      title: 'Score',
      key: 'score',
      width: 100,
      search: false,
      sorter: true,
      render: (_, record) => {
        if (record.status === 'completed' && record.results) {
          return (
            <Tooltip title={`Overall Score: ${record.results.overallScore.toFixed(3)}`}>
              <Tag color={record.results.overallScore >= 0.8 ? 'green' : 
                          record.results.overallScore >= 0.6 ? 'orange' : 'red'}>
                {(record.results.overallScore * 100).toFixed(1)}%
              </Tag>
            </Tooltip>
          )
        }
        return '-'
      },
    },
    {
      title: 'Model',
      key: 'model',
      width: 150,
      search: false,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.modelConfig.provider}</div>
          <div style={{ fontSize: 12, color: '#666' }}>{record.modelConfig.model}</div>
        </div>
      ),
    },
    {
      title: 'Duration',
      key: 'duration',
      width: 120,
      search: false,
      render: (_, record) => {
        if (record.startedAt && record.completedAt) {
          const duration = dayjs(record.completedAt).diff(dayjs(record.startedAt), 'minute')
          return duration > 0 ? `${duration}m` : '< 1m'
        }
        if (record.startedAt && record.status === 'running') {
          const duration = dayjs().diff(dayjs(record.startedAt), 'minute')
          return duration > 0 ? `${duration}m` : '< 1m'
        }
        return '-'
      },
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      search: false,
      sorter: true,
      render: (date) => dayjs(date).format('MMM D, YYYY HH:mm'),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      search: false,
      fixed: 'right',
      render: (_, record) => (
        <Space size='small'>
          {getActionButton(record)}
          <Dropdown 
            menu={{ items: getRowActions(record) }}
            trigger={['click']}
            placement='bottomRight'
          >
            <Button 
              type='text' 
              size='small' 
              icon={<MoreOutlined />} 
            />
          </Dropdown>
        </Space>
      ),
    },
  ]

  // Remove actions column if not needed
  if (!showActions) {
    columns.pop()
  }

  return (
    <ProTable<Evaluation>
      columns={columns}
      actionRef={actionRef}
      cardBordered
      request={async (params, sort) => {
        const { data, isLoading, error } = useEvaluations(projectId, {
          page: params.current,
          pageSize: params.pageSize,
          sortBy: Object.keys(sort || {})[0],
          sortOrder: Object.values(sort || {})[0] as 'asc' | 'desc',
        })

        if (error) {
          throw error
        }

        return {
          data: data?.data || [],
          success: !isLoading,
          total: data?.total || 0,
        }
      }}
      columnsState={{
        persistenceKey: 'pro-table-evaluations',
        persistenceType: 'localStorage',
      }}
      rowKey='id'
      search={{
        labelWidth: 'auto',
        defaultCollapsed: false,
      }}
      pagination={{
        pageSize: 20,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => 
          `${range[0]}-${range[1]} of ${total} evaluations`,
      }}
      dateFormatter='string'
      headerTitle={projectId ? 'Project Evaluations' : 'All Evaluations'}
      toolBarRender={showActions ? () => [
        <Button
          key='create'
          icon={<PlusOutlined />}
          type='primary'
          onClick={onCreateEvaluation}
        >
          New Evaluation
        </Button>,
      ] : false}
      scroll={{ x: 1400 }}
      polling={2000} // Poll every 2 seconds to update running evaluations
    />
  )
}