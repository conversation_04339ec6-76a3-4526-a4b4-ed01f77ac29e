import { useEffect, useMemo, useState } from 'react'
import { 
  Button, 
  Form, 
  Input, 
  Switch, 
  Space, 
  Collapse, 
  Select,
  InputNumber,
  Alert,
  Typography,
  Divider
} from 'antd'
import { CodeOutlined, SettingOutlined, InfoCircleOutlined } from '@ant-design/icons'
import { useCreateProbe, useUpdateProbe } from '@/api/queries/useProbes'
import type { 
  Probe, 
  ProbeCreateRequest, 
  ProbeUpdateRequest,
  ProbeParams,
  PromptProbeParams,
  RegexProbeParams,
  ToolProbeParams,
  CustomProbeParams
} from '@/types/probe'

const { TextArea } = Input
const { Text } = Typography

export interface ProbeFormProps {
  probe?: Probe
  onSuccess?: () => void
  onCancel?: () => void
}

/**
 * ProbeForm
 * Ant Design form for creating/editing a PROBE. Uses React Query mutations.
 */
const ProbeForm = ({ probe, onSuccess, onCancel }: ProbeFormProps) => {
  const [form] = Form.useForm<ProbeCreateRequest | ProbeUpdateRequest>()
  const [probeType, setProbeType] = useState<string>('prompt')
  const createProbeMutation = useCreateProbe()
  const updateProbeMutation = useUpdateProbe()

  const isEditMode = !!probe
  const isPending = createProbeMutation.isPending || updateProbeMutation.isPending

  const initialValues = useMemo<ProbeCreateRequest>(() => ({
    code: '',
    name: '',
    description: '',
    version: '1.0.0',
    active: true,
    params: {
      type: 'prompt',
      prompt: '',
      expectedResponse: '',
      model: 'gpt-4o',
      temperature: 0.7,
      maxTokens: 1000
    } as PromptProbeParams,
  }), [])

  useEffect(() => {
    if (isEditMode && probe) {
      form.setFieldsValue({
        ...probe,
        params: probe.params || {}
      })
      // Set probe type from params
      if (probe.params?.type) {
        setProbeType(probe.params.type)
      }
    }
  }, [probe, isEditMode, form])

  const handleProbeTypeChange = (type: string) => {
    setProbeType(type)
    
    // Reset params based on type
    let defaultParams: ProbeParams
    
    switch (type) {
      case 'prompt':
        defaultParams = {
          type: 'prompt',
          prompt: '',
          expectedResponse: '',
          model: 'gpt-4o',
          temperature: 0.7,
          maxTokens: 1000
        } as PromptProbeParams
        break
      case 'regex':
        defaultParams = {
          type: 'regex',
          pattern: '',
          flags: 'g',
          testString: '',
          shouldMatch: true
        } as RegexProbeParams
        break
      case 'tool':
        defaultParams = {
          type: 'tool',
          toolName: '',
          toolArgs: {},
          expectedOutput: null,
          timeout: 30
        } as ToolProbeParams
        break
      case 'custom':
        defaultParams = {
          type: 'custom',
          script: '',
          language: 'javascript',
          dependencies: []
        } as CustomProbeParams
        break
      default:
        defaultParams = { type }
    }
    
    form.setFieldValue('params', defaultParams)
  }

  const handleFinish = (values: ProbeCreateRequest | ProbeUpdateRequest) => {
    const payload = {
      ...values,
      params: values.params || {}
    }

    if (isEditMode && probe) {
      updateProbeMutation.mutate({ id: probe.id, data: payload as ProbeUpdateRequest }, {
        onSuccess: () => {
          onSuccess?.()
        },
      })
    } else {
      createProbeMutation.mutate(payload as ProbeCreateRequest, {
        onSuccess: () => {
          onSuccess?.()
          form.resetFields()
        },
      })
    }
  }

  const renderProbeTypeParams = () => {
    switch (probeType) {
      case 'prompt':
        return (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Form.Item 
              name={['params', 'prompt']} 
              label="Prompt"
              rules={[{ required: true, message: 'Please enter the prompt' }]}
            >
              <TextArea rows={4} placeholder="Enter the prompt to test..." />
            </Form.Item>
            <Form.Item name={['params', 'expectedResponse']} label="Expected Response">
              <TextArea rows={2} placeholder="Optional expected response pattern..." />
            </Form.Item>
            <Form.Item name={['params', 'model']} label="Model">
              <Select
                options={[
                  { value: 'gpt-4o', label: 'GPT-4o' },
                  { value: 'gpt-4', label: 'GPT-4' },
                  { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
                  { value: 'claude-3', label: 'Claude 3' },
                  { value: 'llama-3', label: 'Llama 3' },
                ]}
              />
            </Form.Item>
            <Form.Item name={['params', 'temperature']} label="Temperature">
              <InputNumber min={0} max={2} step={0.1} style={{ width: '100%' }} />
            </Form.Item>
            <Form.Item name={['params', 'maxTokens']} label="Max Tokens">
              <InputNumber min={1} max={4000} style={{ width: '100%' }} />
            </Form.Item>
          </Space>
        )
      
      case 'regex':
        return (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Form.Item 
              name={['params', 'pattern']} 
              label="Regex Pattern"
              rules={[{ required: true, message: 'Please enter the regex pattern' }]}
            >
              <Input placeholder="^[a-zA-Z0-9]+$" />
            </Form.Item>
            <Form.Item name={['params', 'flags']} label="Flags">
              <Input placeholder="g, i, m, etc." />
            </Form.Item>
            <Form.Item name={['params', 'testString']} label="Test String">
              <TextArea rows={2} placeholder="String to test against the pattern..." />
            </Form.Item>
            <Form.Item name={['params', 'shouldMatch']} label="Should Match" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Space>
        )
      
      case 'tool':
        return (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Form.Item 
              name={['params', 'toolName']} 
              label="Tool Name"
              rules={[{ required: true, message: 'Please enter the tool name' }]}
            >
              <Input placeholder="calculator, web_search, etc." />
            </Form.Item>
            <Form.Item name={['params', 'toolArgs']} label="Tool Arguments (JSON)">
              <TextArea rows={3} placeholder='{"arg1": "value1", "arg2": "value2"}' />
            </Form.Item>
            <Form.Item name={['params', 'expectedOutput']} label="Expected Output">
              <TextArea rows={2} placeholder="Expected tool output..." />
            </Form.Item>
            <Form.Item name={['params', 'timeout']} label="Timeout (seconds)">
              <InputNumber min={1} max={300} style={{ width: '100%' }} />
            </Form.Item>
          </Space>
        )
      
      case 'custom':
        return (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Form.Item name={['params', 'language']} label="Language">
              <Select
                options={[
                  { value: 'javascript', label: 'JavaScript' },
                  { value: 'python', label: 'Python' },
                ]}
              />
            </Form.Item>
            <Form.Item 
              name={['params', 'script']} 
              label="Script"
              rules={[{ required: true, message: 'Please enter the script' }]}
            >
              <TextArea rows={8} placeholder="Enter your custom script..." />
            </Form.Item>
            <Form.Item name={['params', 'dependencies']} label="Dependencies">
              <Select mode="tags" placeholder="Add dependencies..." />
            </Form.Item>
          </Space>
        )
      
      default:
        return (
          <Alert
            message="Custom Parameters"
            description="Configure custom parameters for this probe type."
            type="info"
            showIcon
          />
        )
    }
  }

  return (
    <Form<ProbeCreateRequest | ProbeUpdateRequest>
      form={form}
      layout="vertical"
      initialValues={initialValues}
      onFinish={handleFinish}
    >
      {/* Basic Information */}
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <InfoCircleOutlined style={{ marginRight: 8 }} />
          <Text strong>Basic Information</Text>
        </div>
        
        <Form.Item
          name="code"
          label="Code"
          rules={[
            { required: true, message: 'Please enter the probe code' },
            { pattern: /^[a-zA-Z0-9_-]+$/, message: 'Code can only contain letters, numbers, hyphens, and underscores' }
          ]}
          extra="Unique identifier for the probe (letters, numbers, hyphens, underscores only)"
        >
          <Input placeholder="my-probe-code" disabled={isEditMode} />
        </Form.Item>

        <Form.Item
          name="name"
          label="Name"
          rules={[{ required: true, message: 'Please enter the probe name' }]}
        >
          <Input placeholder="My Probe" autoFocus={!isEditMode} />
        </Form.Item>

        <Form.Item
          name="description"
          label="Description"
          rules={[{ required: true, message: 'Please enter the probe description' }]}
        >
          <TextArea rows={3} placeholder="Describe what this probe tests..." />
        </Form.Item>

        <Form.Item
          name="version"
          label="Version"
          rules={[{ required: true, message: 'Please enter the probe version' }]}
        >
          <Input placeholder="1.0.0" />
        </Form.Item>

        <Form.Item name="active" label="Active" valuePropName="checked">
          <Switch />
        </Form.Item>
      </Space>

      <Divider />

      {/* Probe Configuration */}
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <SettingOutlined style={{ marginRight: 8 }} />
          <Text strong>Probe Configuration</Text>
        </div>

        <Form.Item name={['params', 'type']} label="Probe Type">
          <Select
            value={probeType}
            onChange={handleProbeTypeChange}
            options={[
              { value: 'prompt', label: 'Prompt Test' },
              { value: 'regex', label: 'Regex Pattern' },
              { value: 'tool', label: 'Tool Execution' },
              { value: 'custom', label: 'Custom Script' },
            ]}
          />
        </Form.Item>

        {renderProbeTypeParams()}
      </Space>

      <Divider />

      {/* Actions */}
      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit" loading={isPending}>
            {isEditMode ? 'Save Changes' : 'Create Probe'}
          </Button>
          <Button onClick={onCancel}>Cancel</Button>
        </Space>
      </Form.Item>
    </Form>
  )
}

export default ProbeForm
