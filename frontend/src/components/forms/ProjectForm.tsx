import { useEffect, useMemo } from 'react'
import { Button, Form, Input, Select, Switch, InputNumber, Space, Collapse } from 'antd'
import { useCreateProject, useUpdateProject } from '@/api/queries/useProjects'
import type { Project, ProjectCreateRequest, ProjectUpdateRequest } from '@/types/project'

export interface ProjectFormProps {
  project?: Project
  onSuccess?: () => void
  onCancel?: () => void
}

/**
 * ProjectForm
 * Ant Design form for creating a Project. Uses React Query mutation.
 */
const ProjectForm = ({ project, onSuccess, onCancel }: ProjectFormProps) => {
  const [form] = Form.useForm<ProjectCreateRequest | ProjectUpdateRequest>()
  const createProjectMutation = useCreateProject()
  const updateProjectMutation = useUpdateProject()

  const isEditMode = !!project
  const isPending = createProjectMutation.isPending || updateProjectMutation.isPending

  const initialValues = useMemo<ProjectCreateRequest>(() => ({
    name: '',
    description: '',
    settings: {
      timeoutSeconds: 60,
      retryCount: 0,
      enableCaching: false,
      cacheTtlMinutes: 5,
    },
    metadata: {
      tags: [],
      environment: 'development',
      version: '1.0.0',
    },
  }), [])

  useEffect(() => {
    if (isEditMode) {
      form.setFieldsValue(project)
    }
  }, [project, isEditMode, form])

  const handleFinish = (values: ProjectCreateRequest | ProjectUpdateRequest) => {
    const baseSettings = {
      timeoutSeconds: values.settings?.timeoutSeconds ?? 60,
      retryCount: values.settings?.retryCount ?? 0,
      enableCaching: !!values.settings?.enableCaching,
      cacheTtlMinutes: values.settings?.cacheTtlMinutes ?? 5,
    }

    const settings = {
      ...baseSettings,
      ...(values.settings?.defaultProvider
        ? { defaultProvider: values.settings.defaultProvider }
        : {}),
      ...(values.settings?.defaultModel
        ? { defaultModel: values.settings.defaultModel }
        : {}),
    }

    const metadata = {
      tags: values.metadata?.tags || [],
      environment: values.metadata?.environment || 'development',
      version: values.metadata?.version || '1.0.0',
      ...(values.metadata?.category
        ? { category: values.metadata.category }
        : {}),
    }

    const payload = {
      name: values.name,
      ...(values.description && values.description.trim()
        ? { description: values.description.trim() }
        : {}),
      ...(settings ? { settings } : {}),
      ...(metadata ? { metadata } : {}),
    }

    if (isEditMode && project) {
      updateProjectMutation.mutate({ id: project.id, data: payload as ProjectUpdateRequest }, {
        onSuccess: () => {
          onSuccess?.()
        },
      })
    } else {
      createProjectMutation.mutate(payload as ProjectCreateRequest, {
        onSuccess: () => {
          onSuccess?.()
          form.resetFields()
        },
      })
    }
  }

  return (
    <Form<ProjectCreateRequest | ProjectUpdateRequest>
      form={form}
      layout='vertical'
      initialValues={initialValues}
      onFinish={handleFinish}
    >
      <Form.Item
        name='name'
        label='Name'
        rules={[{ required: true, message: 'Please enter the project name' }]}
      >
        <Input placeholder='My Project' autoFocus />
      </Form.Item>

      <Form.Item name='description' label='Description'>
        <Input.TextArea rows={3} placeholder='Optional project description' />
      </Form.Item>

      <Collapse size='small' items={[
        {
          key: 'settings',
          label: 'Advanced Settings',
          children: (
            <Space direction='vertical' style={{ width: '100%' }}>
              <Form.Item name={['settings', 'defaultProvider']} label='Default Provider'>
                <Input placeholder='e.g. openai, azure, bedrock' />
              </Form.Item>
              <Form.Item name={['settings', 'defaultModel']} label='Default Model'>
                <Input placeholder='e.g. gpt-4o, llama-3, claude-3' />
              </Form.Item>
              <Form.Item name={['settings', 'timeoutSeconds']} label='Timeout (seconds)'>
                <InputNumber min={5} max={600} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item name={['settings', 'retryCount']} label='Retry Count'>
                <InputNumber min={0} max={5} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item name={['settings', 'enableCaching']} label='Enable Caching' valuePropName='checked'>
                <Switch />
              </Form.Item>
              <Form.Item name={['settings', 'cacheTtlMinutes']} label='Cache TTL (minutes)'>
                <InputNumber min={1} max={1440} style={{ width: '100%' }} />
              </Form.Item>
            </Space>
          ),
        },
        {
          key: 'metadata',
          label: 'Metadata',
          children: (
            <Space direction='vertical' style={{ width: '100%' }}>
              <Form.Item name={['metadata', 'tags']} label='Tags'>
                <Select mode='tags' placeholder='Add tags' />
              </Form.Item>
              <Form.Item name={['metadata', 'category']} label='Category'>
                <Input placeholder='Optional category' />
              </Form.Item>
              <Form.Item name={['metadata', 'environment']} label='Environment'>
                <Select
                  options={[
                    { value: 'development', label: 'Development' },
                    { value: 'staging', label: 'Staging' },
                    { value: 'production', label: 'Production' },
                  ]}
                />
              </Form.Item>
              <Form.Item name={['metadata', 'version']} label='Version'>
                <Input placeholder='1.0.0' />
              </Form.Item>
            </Space>
          ),
        },
      ]} />

      <Form.Item>
        <Space>
          <Button type='primary' htmlType='submit' loading={isPending}>
            {isEditMode ? 'Save Changes' : 'Create Project'}
          </Button>
          <Button onClick={onCancel}>Cancel</Button>
        </Space>
      </Form.Item>
    </Form>
  )
}

export default ProjectForm
