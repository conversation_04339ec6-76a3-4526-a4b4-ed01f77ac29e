/**
 * Tree node interface
 * Represents a node in the file tree
 */
export interface TreeNode {
  /** Unique identifier for the node */
  id: string;
  /** Display name of the file or folder */
  name: string;
  /** Type of the node */
  type: 'file' | 'folder';
  /** File extension (for files only) */
  extension?: string | undefined;
  /** File content (for files only) */
  content?: string | undefined;
  /** Child nodes (for folders only) */
  children?: TreeNode[] | undefined;
  /** Whether the folder is expanded (for folders only) */
  isExpanded?: boolean | undefined;
}

/**
 * Context menu position and target information
 */
export interface ContextMenuState {
  x: number;
  y: number;
  node: TreeNode;
}

/**
 * Drag position indicator for drag & drop operations
 */
export type DragPosition = 'before' | 'after' | 'inside' | null;

/**
 * File operation result
 */
export interface FileOperationResult {
  fileId?: string;
  fileName?: string;
  content?: string;
}

/**
 * VSCode Rules Editor imperative handle interface
 */
export interface VSCodeRulesEditorHandle {
  /** Format the current file using Monaco editor */
  format: () => void;
  /** Save the current file and return file information */
  save: () => FileOperationResult | undefined;
  /** Reload the editor to initial state */
  reload: () => void;
  /** Run tests/validation on current file */
  run: () => void;
}

/**
 * VSCode Rules Editor component props
 */
export interface VSCodeRulesEditorProps {
  /** Initial tree data structure */
  initialTreeData?: TreeNode[];
  /** Callback when file content changes */
  onFileChange?: (fileId: string, content: string) => void;
  /** Callback when file is selected */
  onFileSelect?: (file: TreeNode) => void;
  /** Custom theme for Monaco editor */
  theme?: 'vs' | 'vs-dark' | 'vs-light' | 'hc-black';
  /** Whether the editor is read-only */
  readOnly?: boolean;
  /** Custom class name for styling */
  className?: string;
}

/**
 * Tree item component props
 */
export interface TreeItemProps {
  node: TreeNode;
  level?: number;
  selectedId?: string | undefined;
  onSelect: (node: TreeNode) => void;
  onToggle: (nodeId: string) => void;
  onContextMenu: (event: React.MouseEvent, node: TreeNode) => void;
  onRename: (nodeId: string, newName: string) => void;
  onCreateFile: (parentId: string) => void;
  onCreateFolder: (parentId: string) => void;
  onMoveNode: (draggedId: string, targetId: string, position: DragPosition) => void;
  onDelete: (nodeId: string) => void;
}

/**
 * Context menu component props
 */
export interface ContextMenuProps {
  x: number;
  y: number;
  node: TreeNode;
  onClose: () => void;
  onAction: (action: string, node: TreeNode) => void;
}

/**
 * Monaco editor component props
 */
export interface MonacoEditorProps {
  /** Editor content value */
  value: string;
  /** Programming language for syntax highlighting */
  language?: string | undefined;
  /** Editor theme */
  theme: 'vs' | 'vs-dark' | 'vs-light' | 'hc-black';
  /** File name for display */
  fileName: string;
  /** Whether editor is read-only */
  readOnly?: boolean;
  /** Content change handler */
  onChange?: (newContent: string) => void;
  /** Editor mount callback */
  onEditorMount?: (editor: any, monaco: any) => void;
}

/**
 * File extension to language mapping
 */
export const LANGUAGE_MAP: Record<string, string> = {
  json: 'json',
  yaml: 'yaml',
  yml: 'yaml',
  js: 'javascript',
  jsx: 'javascriptreact',
  ts: 'typescript',
  tsx: 'typescriptreact',
  css: 'css',
  scss: 'scss',
  html: 'html',
  md: 'markdown',
  py: 'python',
  txt: 'plaintext',
} as const;

/**
 * File type colors for icons
 */
export const FILE_TYPE_COLORS: Record<string, string> = {
  yaml: '#ff6b6b',
  yml: '#ff6b6b',
  ts: '#3178c6',
  tsx: '#61dafb',
  json: '#ffd700',
  jsonl: '#ffd700',
  js: '#f7df1e',
  jsx: '#f7df1e',
  md: '#083fa1',
  css: '#1572b6',
  scss: '#1572b6',
  html: '#e34f26',
  py: '#3776ab',
  default: '#6b7280',
} as const;
