import React, { useRef, useEffect, useCallback } from 'react';
import { MonacoEditorProps } from '../types/TreeNode';
import { getMonacoLanguage, getFileIcon } from '../utils/fileIcons';

// Declare global Monaco types
declare global {
  interface Window {
    monaco: any;
    require: any;
  }
}

/**
 * Monaco Editor component with VSCode-like styling and functionality
 */
export const MonacoEditor: React.FC<MonacoEditorProps> = ({
  value,
  language,
  theme = 'vs',
  fileName,
  readOnly = false,
  onChange,
  onEditorMount,
}) => {
  const editorRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const monacoRef = useRef<any>(null);

  const initializeEditor = useCallback(() => {
    if (!containerRef.current || !window.monaco) return;

    const monacoLanguage = getMonacoLanguage(language);

    const editor = window.monaco.editor.create(containerRef.current, {
      value,
      language: monacoLanguage,
      theme,
      readOnly,
      automaticLayout: true,
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      fontSize: 14,
      lineHeight: 20,
      fontFamily: '"Fira Code", "Cascadia Code", "JetBrains Mono", Consolas, "Courier New", monospace',
      renderWhitespace: 'selection',
      wordWrap: 'on',
      contextmenu: true,
      mouseWheelZoom: true,
      cursorBlinking: 'blink',
      cursorSmoothCaretAnimation: 'on',
      smoothScrolling: true,
      guides: {
        bracketPairs: true,
        indentation: true,
      },
    });

    // Store references
    editorRef.current = editor;
    monacoRef.current = window.monaco;

    // Notify parent component
    onEditorMount?.(editor, window.monaco);

    // Listen for content changes
    editor.onDidChangeModelContent(() => {
      const newValue = editor.getValue();
      onChange?.(newValue);
    });

    // Add keyboard shortcuts
    editor.addCommand(window.monaco.KeyMod.CtrlCmd | window.monaco.KeyCode.KeyS, () => {
      // Save shortcut - handled by parent component
      console.log('Save shortcut triggered');
    });

    return editor;
  }, [value, language, theme, readOnly, onChange, onEditorMount]);

  const loadMonaco = useCallback(async () => {
    if (window.monaco) {
      initializeEditor();
      return;
    }

    try {
      // Load Monaco Editor from CDN
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs/loader.js';
      script.async = true;

      script.onload = () => {
        window.require.config({
          paths: { vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs' }
        });

        window.require(['vs/editor/editor.main'], () => {
          initializeEditor();
        });
      };

      document.head.appendChild(script);
    } catch (error) {
      console.error('Failed to load Monaco Editor:', error);
    }
  }, [initializeEditor]);

  useEffect(() => {
    loadMonaco();

    return () => {
      if (editorRef.current) {
        editorRef.current.dispose();
      }
    };
  }, [loadMonaco]);

  // Update editor value when prop changes
  useEffect(() => {
    if (editorRef.current && editorRef.current.getValue() !== value) {
      editorRef.current.setValue(value);
    }
  }, [value]);

  // Update editor language when prop changes
  useEffect(() => {
    if (editorRef.current && monacoRef.current && language) {
      const model = editorRef.current.getModel();
      if (model) {
        monacoRef.current.editor.setModelLanguage(model, getMonacoLanguage(language));
      }
    }
  }, [language]);

  return (
    <div className="flex-1 flex flex-col">
      {/* Editor Header */}
      <div className="bg-gray-100 px-4 py-2 border-b border-gray-300 flex items-center gap-2">
        {getFileIcon({
          id: 'temp',
          name: fileName,
          type: 'file',
          extension: language
        })}
        <span className="text-gray-700 text-sm">{fileName}</span>
        <div className="ml-auto text-xs text-gray-500 uppercase">
          {getMonacoLanguage(language)}
        </div>
        <div className="w-2 h-2 rounded-full bg-gray-600 opacity-70" />
      </div>

      {/* Monaco Editor Container */}
      <div
        ref={containerRef}
        className="flex-1"
        style={{ minHeight: '200px' }}
      />

      {/* Status Bar */}
      <div className="bg-gray-200 text-gray-700 text-xs px-4 py-1 flex items-center justify-between border-t border-gray-300">
        <div className="flex items-center gap-4">
          <span>Monaco Editor</span>
          <span>UTF-8</span>
          <span>{getMonacoLanguage(language).toUpperCase()}</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-green-600">●</span>
          <span>Ready</span>
        </div>
      </div>
    </div>
  );
};
