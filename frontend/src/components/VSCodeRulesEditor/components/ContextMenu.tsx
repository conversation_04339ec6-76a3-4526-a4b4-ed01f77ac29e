import React from 'react';
import { File, Folder, Edit, Copy, Trash2 } from 'lucide-react';
import { ContextMenuProps } from '../types/TreeNode';

/**
 * Context menu component for tree item actions
 */
export const ContextMenu: React.FC<ContextMenuProps> = ({
  x,
  y,
  node,
  onClose,
  onAction,
}) => {
  const handleAction = (action: string) => {
    onAction(action, node);
    onClose();
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClose();
  };

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 z-40"
        onClick={handleBackdropClick}
      />
      
      {/* Context Menu */}
      <div
        className="fixed bg-gray-800 border border-gray-600 rounded-md shadow-lg z-50 py-1 min-w-32"
        style={{ left: x, top: y }}
        onClick={(e) => e.stopPropagation()}
        role="menu"
        aria-label="Context menu"
      >
        {node.type === 'folder' && (
          <>
            <button
              className="w-full px-3 py-1 text-left hover:bg-gray-700 text-sm flex items-center gap-2 text-gray-200"
              onClick={() => handleAction('createFile')}
              role="menuitem"
            >
              <File className="w-3 h-3" />
              New File
            </button>
            <button
              className="w-full px-3 py-1 text-left hover:bg-gray-700 text-sm flex items-center gap-2 text-gray-200"
              onClick={() => handleAction('createFolder')}
              role="menuitem"
            >
              <Folder className="w-3 h-3" />
              New Folder
            </button>
            <hr className="border-gray-600 my-1" />
          </>
        )}
        
        <button
          className="w-full px-3 py-1 text-left hover:bg-gray-700 text-sm flex items-center gap-2 text-gray-200"
          onClick={() => handleAction('rename')}
          role="menuitem"
        >
          <Edit className="w-3 h-3" />
          Rename
        </button>
        
        <button
          className="w-full px-3 py-1 text-left hover:bg-gray-700 text-sm flex items-center gap-2 text-gray-200"
          onClick={() => handleAction('copy')}
          role="menuitem"
        >
          <Copy className="w-3 h-3" />
          Duplicate
        </button>
        
        <hr className="border-gray-600 my-1" />
        
        <button
          className="w-full px-3 py-1 text-left hover:bg-red-600 text-sm flex items-center gap-2 text-gray-200"
          onClick={() => handleAction('delete')}
          role="menuitem"
        >
          <Trash2 className="w-3 h-3" />
          Delete
        </button>
      </div>
    </>
  );
};
