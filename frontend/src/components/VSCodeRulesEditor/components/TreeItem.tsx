import React, { useState, useEffect, useCallback } from 'react';
import { ChevronRight, ChevronDown, Plus, Folder, Edit } from 'lucide-react';
import { TreeItemProps, DragPosition } from '../types/TreeNode';
import { getFileIcon } from '../utils/fileIcons';

/**
 * Tree item component with drag & drop, context menu, and inline editing
 */
export const TreeItem: React.FC<TreeItemProps> = ({
  node,
  level = 0,
  selectedId,
  onSelect,
  onToggle,
  onContextMenu,
  onRename,
  onCreateFile,
  onCreateFolder,
  onMoveNode,
  onDelete,
}) => {
  const [isRenaming, setIsRenaming] = useState(false);
  const [newName, setNewName] = useState(node.name);
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragPosition, setDragPosition] = useState<DragPosition>(null);

  const isSelected = selectedId === node.id;

  const handleRename = useCallback(() => {
    if (newName.trim() && newName !== node.name) {
      onRename(node.id, newName.trim());
    }
    setIsRenaming(false);
    setNewName(node.name);
  }, [newName, node.name, node.id, onRename]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename();
    } else if (e.key === 'Escape') {
      setIsRenaming(false);
      setNewName(node.name);
    }
  }, [handleRename, node.name]);

  const handleItemClick = useCallback(() => {
    onSelect(node);
  }, [node, onSelect]);

  const handleItemDoubleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (node.type === 'folder') {
      onToggle(node.id);
    } else {
      onSelect(node);
    }
  }, [node, onToggle, onSelect]);

  const handleItemKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && isSelected) {
      e.preventDefault();
      setIsRenaming(true);
    } else if (e.key === 'Delete' && isSelected) {
      onDelete(node.id);
    }
  }, [isSelected, node.id, onDelete]);

  const startRename = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setIsRenaming(true);
  }, []);

  // Drag & Drop handlers
  const handleDragStart = useCallback((e: React.DragEvent) => {
    e.dataTransfer.setData('application/json', JSON.stringify({
      id: node.id,
      name: node.name,
      type: node.type
    }));
    e.dataTransfer.effectAllowed = 'move';
    setIsDragging(true);

    // Visual feedback
    setTimeout(() => {
      const element = e.target as HTMLElement;
      element.style.opacity = '0.5';
    }, 0);
  }, [node]);

  const handleDragEnd = useCallback((e: React.DragEvent) => {
    setIsDragging(false);
    const element = e.target as HTMLElement;
    element.style.opacity = '1';
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';

    if (isDragging) return; // Don't allow drop on self

    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const y = e.clientY - rect.top;
    const height = rect.height;

    let position: DragPosition = null;

    if (node.type === 'folder') {
      // For folders: before (top 25%), inside (middle 50%), after (bottom 25%)
      if (y < height * 0.25) {
        position = 'before';
      } else if (y > height * 0.75) {
        position = 'after';
      } else {
        position = 'inside';
      }
    } else {
      // For files: before (top 50%), after (bottom 50%)
      position = y < height * 0.5 ? 'before' : 'after';
    }

    setDragPosition(position);
    setIsDragOver(true);
  }, [isDragging, node.type]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    // Only hide if leaving the item completely
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
      setDragPosition(null);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    setDragPosition(null);

    if (isDragging) return; // Don't allow drop on self

    try {
      const dragData = JSON.parse(e.dataTransfer.getData('application/json'));
      if (dragData.id === node.id) return; // Don't drop on self

      onMoveNode(dragData.id, node.id, dragPosition);
    } catch (error) {
      console.error('Error parsing drag data:', error);
    }
  }, [isDragging, node.id, dragPosition, onMoveNode]);

  // Focus input when renaming starts
  useEffect(() => {
    if (isRenaming) {
      const input = document.getElementById(`rename-${node.id}`);
      if (input) {
        (input as HTMLInputElement).focus();
        (input as HTMLInputElement).select();
      }
    }
  }, [isRenaming, node.id]);

  // Drop indicator styles
  const getDropIndicatorClass = () => {
    if (!isDragOver || !dragPosition) return '';

    switch (dragPosition) {
      case 'before':
        return 'border-t-2 border-blue-400';
      case 'after':
        return 'border-b-2 border-blue-400';
      case 'inside':
        return 'bg-blue-900 bg-opacity-30 border border-blue-400 border-opacity-50';
      default:
        return '';
    }
  };

  return (
    <div>
      <div
        className={`group flex items-center gap-1 px-2 py-0.5 hover:bg-gray-700 cursor-pointer text-sm relative transition-all ${
          isSelected ? 'bg-gray-600' : ''
        } ${isDragging ? 'opacity-50' : ''} ${getDropIndicatorClass()}`}
        style={{ 
          paddingLeft: `${level * 12 + 8}px`, 
          color: '#ffffff' 
        }}
        onClick={handleItemClick}
        onDoubleClick={handleItemDoubleClick}
        onContextMenu={(e) => onContextMenu(e, node)}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onKeyDown={handleItemKeyDown}
        tabIndex={0}
        draggable={!isRenaming}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        role="treeitem"
        aria-selected={isSelected}
        aria-expanded={node.type === 'folder' ? node.isExpanded : undefined}
      >
        {/* Fold/Unfold button */}
        {node.type === 'folder' ? (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggle(node.id);
            }}
            className="flex items-center justify-center w-2 h-2 hover:bg-gray-600 rounded text-gray-100 hover:text-white"
            aria-label={node.isExpanded ? 'Collapse folder' : 'Expand folder'}
          >
            {node.isExpanded ? (
              <ChevronDown className="w-2 h-2" />
            ) : (
              <ChevronRight className="w-2 h-2" />
            )}
          </button>
        ) : (
          <div className="w-2" />
        )}

        {/* File/Folder icon */}
        {getFileIcon(node)}

        {/* Name or rename input */}
        {isRenaming ? (
          <input
            id={`rename-${node.id}`}
            type="text"
            value={newName}
            onChange={(e) => setNewName(e.target.value)}
            onBlur={handleRename}
            onKeyDown={handleKeyDown}
            className="bg-gray-600 text-white px-1 py-0 text-sm rounded border-none outline-none flex-1"
            draggable={false}
          />
        ) : (
          <span className="text-white flex-1 select-none font-normal">
            {node.name}
          </span>
        )}

        {/* Action buttons */}
        {(node.type === 'folder' ? (isHovered || isSelected) : (isSelected || isHovered)) && 
         !isDragging && 
         !isRenaming && (
          <div className="flex items-center gap-0.5 ml-auto opacity-0 group-hover:opacity-100 transition-opacity">
            {/* Folder actions */}
            {node.type === 'folder' && (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onCreateFile(node.id);
                  }}
                  className="p-0.5 hover:bg-gray-600 rounded text-gray-100 hover:text-white"
                  title="New File"
                  aria-label="Create new file"
                >
                  <Plus className="w-2 h-2" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onCreateFolder(node.id);
                  }}
                  className="p-0.5 hover:bg-gray-600 rounded text-gray-100 hover:text-white"
                  title="New Folder"
                  aria-label="Create new folder"
                >
                  <Folder className="w-2 h-2" />
                </button>
              </>
            )}

            {/* Rename action */}
            <button
              onClick={startRename}
              className="p-0.5 hover:bg-gray-600 rounded text-gray-100 hover:text-white"
              title="Rename"
              aria-label="Rename item"
            >
              <Edit className="w-2 h-2" />
            </button>
          </div>
        )}

        {/* Selection indicator */}
        {isSelected && !isRenaming && !isDragging && (
          <div className="absolute right-2 text-xs text-gray-500">
            Press Enter to rename
          </div>
        )}

        {/* Drag position indicator */}
        {isDragOver && dragPosition === 'inside' && node.type === 'folder' && (
          <div className="absolute right-2 text-xs text-blue-400">
            Drop inside folder
          </div>
        )}
      </div>

      {/* Children */}
      {node.type === 'folder' && node.isExpanded && node.children && (
        <div role="group">
          {node.children.map((child) => (
            <TreeItem
              key={child.id}
              node={child}
              level={level + 1}
              selectedId={selectedId}
              onSelect={onSelect}
              onToggle={onToggle}
              onContextMenu={onContextMenu}
              onRename={onRename}
              onCreateFile={onCreateFile}
              onCreateFolder={onCreateFolder}
              onMoveNode={onMoveNode}
              onDelete={onDelete}
            />
          ))}
        </div>
      )}
    </div>
  );
};
