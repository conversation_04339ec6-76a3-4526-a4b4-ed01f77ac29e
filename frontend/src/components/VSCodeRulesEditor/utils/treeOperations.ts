import { TreeNode, DragPosition } from '../types/TreeNode';

/**
 * Recursively finds and updates a node in the tree
 */
export const updateNodeInTree = (
  nodes: TreeNode[],
  nodeId: string,
  updater: (node: TreeNode) => TreeNode
): TreeNode[] => {
  return nodes.map(node => {
    if (node.id === nodeId) {
      return updater(node);
    }
    if (node.children) {
      return { ...node, children: updateNodeInTree(node.children, nodeId, updater) };
    }
    return node;
  });
};

/**
 * Recursively finds a node in the tree by ID
 */
export const findNodeById = (nodes: TreeNode[], nodeId: string): TreeNode | null => {
  for (const node of nodes) {
    if (node.id === nodeId) {
      return node;
    }
    if (node.children) {
      const found = findNodeById(node.children, nodeId);
      if (found) return found;
    }
  }
  return null;
};

/**
 * Removes a node from the tree
 */
export const removeNodeFromTree = (nodes: TreeNode[], nodeId: string): TreeNode[] => {
  return nodes
    .filter(node => node.id !== nodeId)
    .map(node => ({
      ...node,
      children: node.children ? removeNodeFromTree(node.children, nodeId) : undefined
    }));
};

/**
 * Adds a new node to the tree at the specified parent
 */
export const addNodeToTree = (
  nodes: TreeNode[],
  parentId: string,
  newNode: TreeNode
): TreeNode[] => {
  return updateNodeInTree(nodes, parentId, parent => ({
    ...parent,
    children: [...(parent.children || []), newNode],
    isExpanded: true // Expand parent when adding child
  }));
};

/**
 * Moves a node within the tree structure
 */
export const moveNodeInTree = (
  nodes: TreeNode[],
  draggedId: string,
  targetId: string,
  position: DragPosition
): TreeNode[] => {
  // Find the dragged node
  const draggedNode = findNodeById(nodes, draggedId);
  if (!draggedNode || !position) return nodes;

  // Remove the dragged node from its current position
  let updatedNodes = removeNodeFromTree(nodes, draggedId);

  // Find the target node
  const targetNode = findNodeById(updatedNodes, targetId);
  if (!targetNode) return nodes;

  if (position === 'inside' && targetNode.type === 'folder') {
    // Add to target folder
    updatedNodes = addNodeToTree(updatedNodes, targetId, draggedNode);
  } else {
    // Add before or after target node
    updatedNodes = insertNodeRelativeToTarget(updatedNodes, draggedNode, targetId, position);
  }

  return updatedNodes;
};

/**
 * Inserts a node before or after a target node
 */
const insertNodeRelativeToTarget = (
  nodes: TreeNode[],
  nodeToInsert: TreeNode,
  targetId: string,
  position: 'before' | 'after'
): TreeNode[] => {
  const insertInArray = (arr: TreeNode[]): TreeNode[] => {
    const targetIndex = arr.findIndex(node => node.id === targetId);
    if (targetIndex !== -1) {
      const newArr = [...arr];
      const insertIndex = position === 'before' ? targetIndex : targetIndex + 1;
      newArr.splice(insertIndex, 0, nodeToInsert);
      return newArr;
    }
    
    return arr.map(node => ({
      ...node,
      children: node.children ? insertInArray(node.children) : undefined
    }));
  };

  return insertInArray(nodes);
};

/**
 * Filters tree nodes based on search query
 */
export const filterTreeNodes = (nodes: TreeNode[], query: string): TreeNode[] => {
  if (!query.trim()) return nodes;

  const searchTerm = query.toLowerCase();

  const filterNode = (node: TreeNode): TreeNode | null => {
    const matchesSearch = node.name.toLowerCase().includes(searchTerm);
    
    if (node.type === 'file') {
      return matchesSearch ? node : null;
    }

    // For folders, check if any children match
    const filteredChildren = node.children
      ?.map(filterNode)
      .filter(Boolean) as TreeNode[];

    if (matchesSearch || (filteredChildren && filteredChildren.length > 0)) {
      return {
        ...node,
        children: filteredChildren,
        isExpanded: filteredChildren && filteredChildren.length > 0 ? true : node.isExpanded
      };
    }

    return null;
  };

  return nodes.map(filterNode).filter(Boolean) as TreeNode[];
};

/**
 * Generates a unique ID for new nodes
 */
export const generateNodeId = (): string => {
  return `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Validates node name for creation/rename operations
 */
export const validateNodeName = (
  name: string,
  parentNode: TreeNode | null,
  excludeId?: string
): { isValid: boolean; error?: string } => {
  if (!name.trim()) {
    return { isValid: false, error: 'Name cannot be empty' };
  }

  if (name.includes('/') || name.includes('\\')) {
    return { isValid: false, error: 'Name cannot contain slashes' };
  }

  // Check for duplicate names in the same parent
  if (parentNode?.children) {
    const duplicate = parentNode.children.find(
      child => child.name === name && child.id !== excludeId
    );
    if (duplicate) {
      return { isValid: false, error: 'Name already exists in this folder' };
    }
  }

  return { isValid: true };
};

/**
 * Gets the file extension from a filename
 */
export const getFileExtension = (filename: string): string | undefined => {
  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1 || lastDotIndex === filename.length - 1) {
    return undefined;
  }
  return filename.substring(lastDotIndex + 1);
};
