import React from 'react';
import { Folder, FolderOpen } from 'lucide-react';
import { TreeNode, FILE_TYPE_COLORS } from '../types/TreeNode';

/**
 * Creates a VSCode-style file icon with colored background and extension label
 */
const createFileIcon = (color: string, symbol?: string): React.ReactElement => (
  <div className="w-5 h-4 flex items-center justify-center relative">
    <svg width="18" height="16" viewBox="0 0 18 16" fill="none">
      <path 
        d="M2 1.5V14.5H16V4.5L13 1.5H2Z" 
        fill={color} 
        opacity="0.9"
      />
      <path 
        d="M13 1.5V4.5H16" 
        stroke="#ffffff" 
        strokeWidth="0.5" 
        fill="none" 
        opacity="0.3"
      />
      <path 
        d="M2 1.5H13V4.5H16V14.5H2V1.5Z" 
        stroke="#ffffff" 
        strokeWidth="0.3" 
        fill="none" 
        opacity="0.2"
      />
    </svg>
    {symbol && (
      <span 
        className="absolute text-white text-xs font-semibold select-none" 
        style={{ 
          fontSize: '6px', 
          top: '4px', 
          left: '50%', 
          transform: 'translateX(-50%)' 
        }}
      >
        {symbol}
      </span>
    )}
  </div>
);

/**
 * Gets the appropriate icon for a tree node based on its type and extension
 */
export const getFileIcon = (node: TreeNode): React.ReactElement => {
  if (node.type === 'folder') {
    return node.isExpanded ? (
      <FolderOpen className="w-4 h-4 text-blue-500" />
    ) : (
      <Folder className="w-4 h-4 text-blue-500" />
    );
  }

  const extension = node.extension?.toLowerCase();
  const color = FILE_TYPE_COLORS[extension || 'default'] || FILE_TYPE_COLORS.default;

  switch (extension) {
    case 'yaml':
    case 'yml':
      return createFileIcon(color, 'YML');
    case 'ts':
      return createFileIcon(color, 'TS');
    case 'tsx':
      return createFileIcon(color, 'TSX');
    case 'json':
      return createFileIcon(color, 'JSON');
    case 'jsonl':
      return createFileIcon(color, 'JSONL');
    case 'js':
    case 'jsx':
      return createFileIcon(color, 'JS');
    case 'md':
      return createFileIcon(color, 'MD');
    case 'css':
    case 'scss':
      return createFileIcon(color, 'CSS');
    case 'html':
      return createFileIcon(color, 'HTML');
    case 'py':
      return createFileIcon(color, 'PY');
    default:
      return createFileIcon(color, '');
  }
};

/**
 * Gets Monaco editor language from file extension
 */
export const getMonacoLanguage = (extension?: string): string => {
  if (!extension) return 'plaintext';
  
  switch (extension.toLowerCase()) {
    case 'json':
    case 'jsonl':
      return 'json';
    case 'yaml':
    case 'yml':
      return 'yaml';
    case 'js':
      return 'javascript';
    case 'jsx':
      return 'javascriptreact';
    case 'ts':
      return 'typescript';
    case 'tsx':
      return 'typescriptreact';
    case 'css':
      return 'css';
    case 'scss':
      return 'scss';
    case 'html':
      return 'html';
    case 'md':
      return 'markdown';
    case 'py':
      return 'python';
    default:
      return 'plaintext';
  }
};
