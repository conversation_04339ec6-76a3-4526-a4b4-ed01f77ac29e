import React, { useState, useCallback, useRef, useImperativeHandle, forwardRef, useMemo } from 'react';
import { ChevronLeft, ChevronRight, Search, File } from 'lucide-react';
import { TreeNode, VSCodeRulesEditorProps, VSCodeRulesEditorHandle, ContextMenuState } from './types/TreeNode';
import { TreeItem } from './components/TreeItem';
import { ContextMenu } from './components/ContextMenu';
import { MonacoEditor } from './components/MonacoEditor';
import { useTheme } from '@/stores/appStore';
import {
  updateNodeInTree,
  removeNodeFromTree,
  addNodeToTree,
  moveNodeInTree,
  filterTreeNodes,
  generateNodeId,
  getFileExtension,
} from './utils/treeOperations';

/**
 * VSCode-like Rules Editor component with file tree and Monaco editor
 * 
 * Features:
 * - File tree with drag & drop
 * - Context menus for file operations
 * - Monaco editor integration
 * - Search and filtering
 * - Resizable sidebar
 * - Keyboard shortcuts
 * - Accessibility support
 */
const VSCodeRulesEditor = forwardRef<VSCodeRulesEditorHandle, VSCodeRulesEditorProps>(({
  initialTreeData = [],
  onFileChange,
  onFileSelect,
  theme,
  readOnly = false,
  className = '',
}, ref) => {
  // Get theme from app store
  const { theme: appTheme } = useTheme();

  // Determine Monaco editor theme based on app theme
  const getMonacoTheme = () => {
    if (theme) return theme; // Use explicit theme if provided

    if (appTheme === 'dark') return 'vs-dark';
    if (appTheme === 'light') return 'vs-light';

    // Auto mode - detect system preference
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'vs-dark';
    }
    return 'vs-light';
  };

  const monacoTheme = getMonacoTheme();

  // Determine if we're using dark theme
  const isDarkTheme = monacoTheme === 'vs-dark';
  // State management
  const [treeData, setTreeData] = useState<TreeNode[]>(initialTreeData);
  const [selectedFile, setSelectedFile] = useState<TreeNode | null>(null);
  const [contextMenu, setContextMenu] = useState<ContextMenuState | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(320);
  const [isResizing, setIsResizing] = useState(false);
  const [nextId, setNextId] = useState(100);

  // Monaco editor refs
  const monacoEditorRef = useRef<any>(null);
  const monacoApiRef = useRef<any>(null);

  // Keep internal state in sync if the incoming initialTreeData prop changes
  React.useEffect(() => {
    setTreeData(initialTreeData);
  }, [initialTreeData]);

  // Filtered tree data based on search query
  const filteredTreeData = useMemo(() => {
    return filterTreeNodes(treeData, searchQuery);
  }, [treeData, searchQuery]);

  // File selection handler
  const handleSelect = useCallback((node: TreeNode) => {
    if (node.type === 'file') {
      setSelectedFile(node);
      onFileSelect?.(node);
    }
  }, [onFileSelect]);

  // Folder toggle handler
  const handleToggle = useCallback((nodeId: string) => {
    setTreeData(prevData =>
      updateNodeInTree(prevData, nodeId, node => ({
        ...node,
        isExpanded: !node.isExpanded
      }))
    );
  }, []);

  // Context menu handlers
  const handleContextMenu = useCallback((event: React.MouseEvent, node: TreeNode) => {
    event.preventDefault();
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      node
    });
  }, []);

  const handleContextAction = useCallback((action: string, node: TreeNode) => {
    switch (action) {
      case 'createFile':
        handleCreateFile(node.id);
        break;
      case 'createFolder':
        handleCreateFolder(node.id);
        break;
      case 'rename':
        // Handled by TreeItem component
        break;
      case 'copy':
        handleDuplicateNode(node.id);
        break;
      case 'delete':
        handleDeleteNode(node.id);
        break;
    }
  }, []);

  // File operations
  const handleCreateFile = useCallback((parentId: string) => {
    const newId = generateNodeId();
    const fileName = `new-file-${nextId}.txt`;

    const newFile: TreeNode = {
      id: newId,
      name: fileName,
      type: 'file',
      extension: 'txt',
      content: ''
    };

    setTreeData(prevData => addNodeToTree(prevData, parentId, newFile));
    setNextId(prev => prev + 1);
  }, [nextId]);

  const handleCreateFolder = useCallback((parentId: string) => {
    const newId = generateNodeId();
    const folderName = `new-folder-${nextId}`;

    const newFolder: TreeNode = {
      id: newId,
      name: folderName,
      type: 'folder',
      children: [],
      isExpanded: false
    };

    setTreeData(prevData => addNodeToTree(prevData, parentId, newFolder));
    setNextId(prev => prev + 1);
  }, [nextId]);

  const handleRename = useCallback((nodeId: string, newName: string) => {
    setTreeData(prevData =>
      updateNodeInTree(prevData, nodeId, node => {
        const extension = node.type === 'file' ? getFileExtension(newName) : undefined;
        return {
          ...node,
          name: newName,
          extension
        };
      })
    );
  }, []);

  const handleDeleteNode = useCallback((nodeId: string) => {
    setTreeData(prevData => removeNodeFromTree(prevData, nodeId));

    // Clear selection if deleted node was selected
    if (selectedFile?.id === nodeId) {
      setSelectedFile(null);
    }
  }, [selectedFile]);

  const handleDuplicateNode = useCallback((nodeId: string) => {
    // Implementation for duplicating nodes
    console.log('Duplicate node:', nodeId);
    // TODO: Implement node duplication
  }, []);

  const handleMoveNode = useCallback((draggedId: string, targetId: string, position: any) => {
    setTreeData(prevData => moveNodeInTree(prevData, draggedId, targetId, position));
  }, []);

  // Code change handler
  const handleCodeChange = useCallback((newContent: string) => {
    if (selectedFile) {
      setTreeData(prevData =>
        updateNodeInTree(prevData, selectedFile.id, node => ({
          ...node,
          content: newContent
        }))
      );
      setSelectedFile(prev => prev ? { ...prev, content: newContent } : null);
      onFileChange?.(selectedFile.id, newContent);
    }
  }, [selectedFile, onFileChange]);

  // Sidebar resize handlers
  const startResizing = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
  }, []);

  React.useEffect(() => {
    const onMouseMove = (e: MouseEvent) => {
      const min = 200;
      const max = 600;
      const next = Math.max(min, Math.min(max, e.clientX));
      if (isSidebarCollapsed) {
        setIsSidebarCollapsed(false);
      }
      setSidebarWidth(next);
    };

    const onMouseUp = () => setIsResizing(false);

    if (isResizing) {
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
      document.body.style.userSelect = 'none';
      document.body.style.cursor = 'col-resize';
    }

    return () => {
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
      document.body.style.userSelect = '';
      document.body.style.cursor = '';
    };
  }, [isResizing, isSidebarCollapsed]);

  // Close context menu on click outside
  React.useEffect(() => {
    const handleClick = () => setContextMenu(null);
    if (contextMenu) {
      document.addEventListener('click', handleClick);
      return () => document.removeEventListener('click', handleClick);
    }
  }, [contextMenu]);

  // Imperative API
  useImperativeHandle(ref, () => ({
    format: () => {
      if (monacoEditorRef.current && monacoApiRef.current) {
        monacoEditorRef.current.trigger('', 'editor.action.formatDocument', {});
      }
    },
    save: () => {
      if (!selectedFile) return undefined;
      const modelContent = monacoEditorRef.current ?
        monacoEditorRef.current.getValue() :
        selectedFile.content;
      return {
        fileId: selectedFile.id,
        fileName: selectedFile.name,
        content: modelContent
      };
    },
    reload: () => {
      setTreeData(initialTreeData);
      setSelectedFile(null);
      setSearchQuery('');
    },
    run: () => {
      console.log('Run action triggered for', selectedFile?.name);
    }
  }), [selectedFile, initialTreeData]);

  return (
    <div className={`flex h-full ${isDarkTheme ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'} ${className}`}>
      {/* Sidebar - File Explorer */}
      <div
        className={`${isDarkTheme ? 'bg-gray-800 border-r border-gray-700' : 'bg-white border-r border-gray-300'} flex flex-col`}
        style={{
          width: isSidebarCollapsed ? 0 : sidebarWidth,
          transition: 'width 0.1s ease',
          overflow: 'hidden'
        }}
      >
        {/* Sidebar Header */}
        {/* No header for now */}

        {/* File Tree */}
        <div className="flex-1 overflow-y-auto" role="tree">
          {filteredTreeData.map((node) => (
            <TreeItem
              key={node.id}
              node={node}
              selectedId={selectedFile?.id}
              isDarkTheme={isDarkTheme}
              onSelect={handleSelect}
              onToggle={handleToggle}
              onContextMenu={handleContextMenu}
              onRename={handleRename}
              onCreateFile={handleCreateFile}
              onCreateFolder={handleCreateFolder}
              onMoveNode={handleMoveNode}
              onDelete={handleDeleteNode}
            />
          ))}
        </div>
      </div>

      {/* Vertical Resizer */}
      <div
        className={`w-px ${isResizing ? 'bg-blue-400' : 'bg-gray-200 hover:bg-gray-300'} cursor-col-resize relative`}
        onMouseDown={startResizing}
      >
        <button
          onClick={(e) => {
            e.stopPropagation();
            setIsSidebarCollapsed((v) => !v);
          }}
          className="absolute top-1 left-1/2 -translate-x-1/2 transform bg-white border border-gray-300 rounded-full p-0 hover:bg-gray-100"
          title={isSidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          aria-label={isSidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {isSidebarCollapsed ? (
            <ChevronRight className="w-2 h-2 text-gray-500" />
          ) : (
            <ChevronLeft className="w-2 h-2 text-gray-500" />
          )}
        </button>
      </div>

      {/* Main Editor Area */}
      <div className="flex-1 flex flex-col">
        {selectedFile ? (
          <MonacoEditor
            value={selectedFile.content || ''}
            language={selectedFile.extension}
            theme={monacoTheme}
            fileName={selectedFile.name}
            readOnly={readOnly}
            onChange={handleCodeChange}
            onEditorMount={(editor, monaco) => {
              monacoEditorRef.current = editor;
              monacoApiRef.current = monaco;
            }}
          />
        ) : (
          <div className={`flex-1 flex items-center justify-center ${isDarkTheme ? 'bg-gray-900' : 'bg-white'}`}>
            <div className="text-center">
              <File className={`w-16 h-16 ${isDarkTheme ? 'text-gray-500' : 'text-gray-400'} mx-auto mb-4`} />
              <p className={`${isDarkTheme ? 'text-gray-300' : 'text-gray-600'} mb-2`}>Select a file to start editing</p>
              <p className={`${isDarkTheme ? 'text-gray-400' : 'text-gray-500'} text-sm`}>Monaco Editor will load automatically</p>
            </div>
          </div>
        )}
      </div>

      {/* Context Menu */}
      {contextMenu && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          node={contextMenu.node}
          onClose={() => setContextMenu(null)}
          onAction={handleContextAction}
        />
      )}
    </div>
  );
});

VSCodeRulesEditor.displayName = 'VSCodeRulesEditor';

export default VSCodeRulesEditor;
export type { VSCodeRulesEditorHandle, VSCodeRulesEditorProps };
export type { TreeNode } from './types/TreeNode';
