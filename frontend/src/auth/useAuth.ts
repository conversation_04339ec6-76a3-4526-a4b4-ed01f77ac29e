import { useContext } from 'react'

import { AuthContextType } from '@/types/auth'
import { AuthProvider } from './AuthContext'

// Re-export the hook for convenience
export { useAuth } from './AuthContext'

// Additional auth utilities
export const useAuthToken = (): string | null => {
  const { accessToken } = useAuth()
  return accessToken
}

export const useIsAuthenticated = (): boolean => {
  const { isAuthenticated } = useAuth()
  return isAuthenticated
}

export const useCurrentUser = () => {
  const { user } = useAuth()
  return user
}

/**
 * Hook to check if current user has a specific permission
 */
export const useHasPermission = (resource: string, action: string): boolean => {
  const { user } = useAuth()
  
  if (!user) return false
  
  return user.roles.some(role =>
    role.permissions.some(permission =>
      permission.resource === resource && permission.action === action
    )
  )
}

/**
 * Hook to check if current user has any of the specified roles
 */
export const useHasRole = (roleNames: string[]): boolean => {
  const { user } = useAuth()
  
  if (!user) return false
  
  return user.roles.some(role => roleNames.includes(role.name))
}

/**
 * Hook to get user roles by scope
 */
export const useRolesByScope = (scope: 'platform' | 'tenant' | 'project' | 'evaluation') => {
  const { user } = useAuth()
  
  if (!user) return []
  
  return user.roles.filter(role => role.scope === scope)
}