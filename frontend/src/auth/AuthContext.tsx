import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { message } from 'antd'

import { AuthContextType, User, LoginRequest, LoginResponse, RefreshTokenResponse } from '@/types/auth'
import { tokenStore } from './tokenStore'

const AuthContext = createContext<AuthContextType | null>(null)

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Initialize authentication state on mount
  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      // Try to refresh token on app start
      const token = await refreshToken()
      if (token) {
        const userData = await fetchUserProfile(token)
        setUser(userData)
      }
    } catch (error) {
      console.warn('Failed to check auth status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string): Promise<void> => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password } as LoginRequest),
        credentials: 'include', // Important for httpOnly cookies
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Login failed')
      }

      const { accessToken, user: userData }: LoginResponse = await response.json()
      
      tokenStore.setAccessToken(accessToken)
      setUser(userData)
      
      message.success('Login successful')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed'
      message.error(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async (): Promise<void> => {
    try {
      // Call logout endpoint to clear httpOnly cookie
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      })
    } catch (error) {
      console.warn('Logout request failed:', error)
    } finally {
      // Clear local state regardless of API call success
      tokenStore.clearToken()
      setUser(null)
      message.success('Logged out successfully')
    }
  }

  const refreshToken = async (): Promise<string | null> => {
    // Prevent multiple concurrent refresh attempts
    const existingPromise = tokenStore.getTokenRefreshPromise()
    if (existingPromise) {
      return existingPromise
    }

    const refreshPromise = performTokenRefresh()
    tokenStore.setTokenRefreshPromise(refreshPromise)

    try {
      const result = await refreshPromise
      return result
    } finally {
      tokenStore.clearTokenRefreshPromise()
    }
  }

  const performTokenRefresh = async (): Promise<string | null> => {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('Token refresh failed')
      }

      const { accessToken }: RefreshTokenResponse = await response.json()
      tokenStore.setAccessToken(accessToken)
      return accessToken
    } catch (error) {
      console.warn('Token refresh failed:', error)
      // Clear invalid state
      tokenStore.clearToken()
      setUser(null)
      return null
    }
  }

  const fetchUserProfile = async (token: string): Promise<User> => {
    const response = await fetch('/api/auth/me', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    if (!response.ok) {
      throw new Error('Failed to fetch user profile')
    }

    return response.json()
  }

  const value: AuthContextType = {
    user,
    accessToken: tokenStore.getAccessToken(),
    isAuthenticated: !!user && tokenStore.hasValidToken(),
    isLoading,
    login,
    logout,
    refreshToken,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}