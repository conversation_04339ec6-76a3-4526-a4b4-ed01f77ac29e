/**
 * Token store for managing JWT tokens in memory
 * Access tokens are stored in memory for security (not in localStorage)
 * Refresh tokens are handled via httpOnly cookies
 */

class TokenStore {
  private accessToken: string | null = null
  private tokenRefreshPromise: Promise<string | null> | null = null

  setAccessToken(token: string | null): void {
    this.accessToken = token
  }

  getAccessToken(): string | null {
    return this.accessToken
  }

  hasValidToken(): boolean {
    return !!this.accessToken
  }

  clearToken(): void {
    this.accessToken = null
    this.tokenRefreshPromise = null
  }

  /**
   * Sets a promise for token refresh to prevent multiple concurrent refresh attempts
   */
  setTokenRefreshPromise(promise: Promise<string | null>): void {
    this.tokenRefreshPromise = promise
  }

  getTokenRefreshPromise(): Promise<string | null> | null {
    return this.tokenRefreshPromise
  }

  clearTokenRefreshPromise(): void {
    this.tokenRefreshPromise = null
  }

  /**
   * Checks if token is expired (basic JWT payload parsing)
   * Returns true if expired or invalid
   */
  isTokenExpired(): boolean {
    if (!this.accessToken) return true

    try {
      const payload = JSON.parse(atob(this.accessToken.split('.')[1]))
      const now = Date.now() / 1000
      return payload.exp < now
    } catch {
      return true
    }
  }

  /**
   * Gets token expiration time in seconds
   */
  getTokenExpiration(): number | null {
    if (!this.accessToken) return null

    try {
      const payload = JSON.parse(atob(this.accessToken.split('.')[1]))
      return payload.exp
    } catch {
      return null
    }
  }
}

export const tokenStore = new TokenStore()