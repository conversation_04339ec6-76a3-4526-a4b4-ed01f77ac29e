import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface AppState {
  // Layout state
  sidebarCollapsed: boolean
  sidebarWidth: number
  
  // Theme state
  theme: 'light' | 'dark' | 'auto'
  
  // Navigation state
  currentPath: string
  breadcrumbs: Array<{ title: string; path: string }>
  
  // UI preferences
  tablePageSize: number
  compactMode: boolean
  
  // Loading states
  globalLoading: boolean
  
  // Actions
  setSidebarCollapsed: (collapsed: boolean) => void
  toggleSidebar: () => void
  setSidebarWidth: (width: number) => void
  setTheme: (theme: 'light' | 'dark' | 'auto') => void
  setCurrentPath: (path: string) => void
  setBreadcrumbs: (breadcrumbs: Array<{ title: string; path: string }>) => void
  setTablePageSize: (size: number) => void
  setCompactMode: (compact: boolean) => void
  setGlobalLoading: (loading: boolean) => void
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      sidebarCollapsed: false,
      sidebarWidth: 256,
      theme: 'auto',
      currentPath: '/',
      breadcrumbs: [],
      tablePageSize: 20,
      compactMode: false,
      globalLoading: false,

      // Actions
      setSidebarCollapsed: (collapsed) => 
        set({ sidebarCollapsed: collapsed }),

      toggleSidebar: () => 
        set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed })),

      setSidebarWidth: (width) => 
        set({ sidebarWidth: Math.max(200, Math.min(400, width)) }),

      setTheme: (theme) => 
        set({ theme }),

      setCurrentPath: (path) => 
        set({ currentPath: path }),

      setBreadcrumbs: (breadcrumbs) => 
        set({ breadcrumbs }),

      setTablePageSize: (size) => 
        set({ tablePageSize: size }),

      setCompactMode: (compact) => 
        set({ compactMode: compact }),

      setGlobalLoading: (loading) => 
        set({ globalLoading: loading }),
    }),
    {
      name: 'sacra2-app-store',
      // Only persist UI preferences, not loading states
      partialize: (state) => ({
        sidebarCollapsed: state.sidebarCollapsed,
        sidebarWidth: state.sidebarWidth,
        theme: state.theme,
        tablePageSize: state.tablePageSize,
        compactMode: state.compactMode,
      }),
    }
  )
)

// Selectors for common use cases
export const useSidebarState = () => useAppStore((state) => ({
  collapsed: state.sidebarCollapsed,
  width: state.sidebarWidth,
  toggle: state.toggleSidebar,
  setCollapsed: state.setSidebarCollapsed,
  setWidth: state.setSidebarWidth,
}))

export const useTheme = () => useAppStore((state) => ({
  theme: state.theme,
  setTheme: state.setTheme,
}))

export const useBreadcrumbs = () => useAppStore((state) => ({
  breadcrumbs: state.breadcrumbs,
  setBreadcrumbs: state.setBreadcrumbs,
}))