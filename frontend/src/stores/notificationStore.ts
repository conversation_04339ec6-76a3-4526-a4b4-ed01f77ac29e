import { create } from 'zustand'

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  timestamp: number
  read: boolean
  actions?: Array<{
    label: string
    action: () => void
  }>
}

interface NotificationState {
  notifications: Notification[]
  unreadCount: number
  
  // Actions
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void
  removeNotification: (id: string) => void
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  clearAll: () => void
}

export const useNotificationStore = create<NotificationState>((set, get) => ({
  notifications: [],
  unreadCount: 0,

  addNotification: (notification) => {
    const newNotification: Notification = {
      ...notification,
      id: `notification-${Date.now()}-${Math.random()}`,
      timestamp: Date.now(),
      read: false,
    }

    set((state) => ({
      notifications: [newNotification, ...state.notifications.slice(0, 99)], // Keep max 100
      unreadCount: state.unreadCount + 1,
    }))
  },

  removeNotification: (id) =>
    set((state) => ({
      notifications: state.notifications.filter((n) => n.id !== id),
      unreadCount: state.notifications.find((n) => n.id === id && !n.read) 
        ? state.unreadCount - 1 
        : state.unreadCount,
    })),

  markAsRead: (id) =>
    set((state) => {
      const notification = state.notifications.find((n) => n.id === id)
      if (!notification || notification.read) return state

      return {
        notifications: state.notifications.map((n) =>
          n.id === id ? { ...n, read: true } : n
        ),
        unreadCount: state.unreadCount - 1,
      }
    }),

  markAllAsRead: () =>
    set((state) => ({
      notifications: state.notifications.map((n) => ({ ...n, read: true })),
      unreadCount: 0,
    })),

  clearAll: () =>
    set({
      notifications: [],
      unreadCount: 0,
    }),
}))

// Utility hooks
export const useUnreadNotifications = () =>
  useNotificationStore((state) => state.notifications.filter((n) => !n.read))

export const useRecentNotifications = (limit: number = 5) =>
  useNotificationStore((state) => state.notifications.slice(0, limit))

// Helper function to add different types of notifications
export const useNotificationHelpers = () => {
  const addNotification = useNotificationStore((state) => state.addNotification)

  return {
    success: (title: string, message: string) =>
      addNotification({ type: 'success', title, message }),
    
    error: (title: string, message: string) =>
      addNotification({ type: 'error', title, message }),
    
    warning: (title: string, message: string) =>
      addNotification({ type: 'warning', title, message }),
    
    info: (title: string, message: string) =>
      addNotification({ type: 'info', title, message }),
    
    evaluationCompleted: (evaluationName: string, score: number) =>
      addNotification({
        type: 'success',
        title: 'Evaluation Completed',
        message: `${evaluationName} finished with a score of ${score.toFixed(2)}`,
        actions: [
          {
            label: 'View Results',
            action: () => {
              // Navigation logic would go here
              console.log('Navigate to evaluation results')
            },
          },
        ],
      }),
    
    evaluationFailed: (evaluationName: string, error: string) =>
      addNotification({
        type: 'error',
        title: 'Evaluation Failed',
        message: `${evaluationName} failed: ${error}`,
        actions: [
          {
            label: 'Retry',
            action: () => {
              // Retry logic would go here
              console.log('Retry evaluation')
            },
          },
        ],
      }),
  }
}