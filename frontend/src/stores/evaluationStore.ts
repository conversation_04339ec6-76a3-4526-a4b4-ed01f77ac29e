import { create } from 'zustand'

import { Evaluation, ModelConfig } from '@/types/evaluation'

interface EvaluationFormState {
  // Form data for creating/editing evaluations
  name: string
  description: string
  assessmentId: string
  modelConfig: Partial<ModelConfig>
  
  // UI state
  step: number
  isValid: boolean
  
  // Actions
  setName: (name: string) => void
  setDescription: (description: string) => void
  setAssessmentId: (assessmentId: string) => void
  setModelConfig: (config: Partial<ModelConfig>) => void
  setStep: (step: number) => void
  setIsValid: (isValid: boolean) => void
  reset: () => void
}

interface EvaluationMonitorState {
  // Monitoring active evaluations
  activeEvaluations: Map<string, Evaluation>
  
  // Real-time updates
  lastUpdate: number
  
  // Actions
  addActiveEvaluation: (evaluation: Evaluation) => void
  updateEvaluation: (id: string, updates: Partial<Evaluation>) => void
  removeActiveEvaluation: (id: string) => void
  setLastUpdate: (timestamp: number) => void
  clearAll: () => void
}

// Store for evaluation form wizard
export const useEvaluationFormStore = create<EvaluationFormState>((set) => ({
  // Initial state
  name: '',
  description: '',
  assessmentId: '',
  modelConfig: {
    provider: '',
    model: '',
    temperature: 0.7,
    maxTokens: 4000,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0,
  },
  step: 0,
  isValid: false,

  // Actions
  setName: (name) => set({ name }),
  setDescription: (description) => set({ description }),
  setAssessmentId: (assessmentId) => set({ assessmentId }),
  
  setModelConfig: (config) => 
    set((state) => ({ 
      modelConfig: { ...state.modelConfig, ...config } 
    })),

  setStep: (step) => set({ step }),
  setIsValid: (isValid) => set({ isValid }),

  reset: () => set({
    name: '',
    description: '',
    assessmentId: '',
    modelConfig: {
      provider: '',
      model: '',
      temperature: 0.7,
      maxTokens: 4000,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
    },
    step: 0,
    isValid: false,
  }),
}))

// Store for monitoring active evaluations
export const useEvaluationMonitorStore = create<EvaluationMonitorState>((set, get) => ({
  // Initial state
  activeEvaluations: new Map(),
  lastUpdate: 0,

  // Actions
  addActiveEvaluation: (evaluation) =>
    set((state) => {
      const newMap = new Map(state.activeEvaluations)
      newMap.set(evaluation.id, evaluation)
      return { activeEvaluations: newMap }
    }),

  updateEvaluation: (id, updates) =>
    set((state) => {
      const newMap = new Map(state.activeEvaluations)
      const existing = newMap.get(id)
      if (existing) {
        newMap.set(id, { ...existing, ...updates })
      }
      return { activeEvaluations: newMap }
    }),

  removeActiveEvaluation: (id) =>
    set((state) => {
      const newMap = new Map(state.activeEvaluations)
      newMap.delete(id)
      return { activeEvaluations: newMap }
    }),

  setLastUpdate: (timestamp) => set({ lastUpdate: timestamp }),

  clearAll: () => set({ activeEvaluations: new Map(), lastUpdate: 0 }),
}))

// Selectors
export const useActiveEvaluations = () => 
  useEvaluationMonitorStore((state) => Array.from(state.activeEvaluations.values()))

export const useRunningEvaluationsCount = () => 
  useEvaluationMonitorStore((state) => 
    Array.from(state.activeEvaluations.values()).filter(
      (evaluation) => ['pending', 'queued', 'running'].includes(evaluation.status)
    ).length
  )

export const useEvaluationById = (id: string) =>
  useEvaluationMonitorStore((state) => state.activeEvaluations.get(id))