import { createBrowserRouter, Navigate } from 'react-router-dom'
import { lazy, Suspense } from 'react'
import { Spin } from 'antd'

import { AppLayout } from '@/components/layout/AppLayout'


// Lazy load pages for better performance
const DashboardPage = lazy(() => import('@/pages/Dashboard').then(m => ({ default: m.DashboardPage })))
const ProjectsPage = lazy(() => import('@/pages/Projects').then(m => ({ default: m.ProjectsPage })))
const EditProjectPage = lazy(() => import('@/pages/Projects/EditProject').then(m => ({ default: m.EditProjectPage })))
const ProjectDetailPage = lazy(() => import('@/pages/Projects/ProjectDetail').then(m => ({ default: m.ProjectDetailPage })))
const EvaluationsPage = lazy(() => import('@/pages/Evaluations').then(m => ({ default: m.EvaluationsPage })))
const EvaluationDetailPage = lazy(() => import('@/pages/Evaluations/EvaluationDetail').then(m => ({ default: m.EvaluationDetailPage })))
const ModelsPage = lazy(() => import('@/pages/Models').then(m => ({ default: m.ModelsPage })))
const ModelDetailPage = lazy(() => import('@/pages/Models/ModelDetail').then(m => ({ default: m.ModelDetailPage })))
const ProbesPage = lazy(() => import('@/pages/Probes').then(m => ({ default: m.ProbesPage })))
const NewProbePage = lazy(() => import('@/pages/Probes/NewProbe').then(m => ({ default: m.NewProbePage })))
const EditProbePage = lazy(() => import('@/pages/Probes/EditProbe').then(m => ({ default: m.EditProbePage })))
const ProbeDetailPage = lazy(() => import('@/pages/Probes/ProbeDetail').then(m => ({ default: m.ProbeDetailPage })))
const SettingsPage = lazy(() => import('@/pages/Settings').then(m => ({ default: m.SettingsPage })))
const LoginPage = lazy(() => import('@/pages/Login').then(m => ({ default: m.LoginPage })))
const RuleEditorDemoPage = lazy(() => import('@/pages/RuleEditorDemo').then(m => ({ default: m.RuleEditorDemoPage })))

// Loading component for lazy routes
const PageLoader = () => (
  <div
    style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '200px',
    }}
  >
    <Spin size='large' />
  </div>
)

// Wrapper for lazy loaded components
const LazyWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<PageLoader />}>
    {children}
  </Suspense>
)

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to='/dashboard' replace />,
  },
  {
    path: '/login',
    element: (
      <LazyWrapper>
        <LoginPage />
      </LazyWrapper>
    ),
  },
  {
    path: '/',
    element: <AppLayout />,
    children: [
      {
        path: 'dashboard',
        element: (
          <LazyWrapper>
            <DashboardPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'projects',
        element: (
          <LazyWrapper>
            <ProjectsPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'projects/:id',
        element: (
          <LazyWrapper>
            <ProjectDetailPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'projects/:id/edit',
        element: (
          <LazyWrapper>
            <EditProjectPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'evaluations',
        element: (
          <LazyWrapper>
            <EvaluationsPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'evaluations/:id',
        element: (
          <LazyWrapper>
            <EvaluationDetailPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'models',
        element: (
          <LazyWrapper>
            <ModelsPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'models/:id',
        element: (
          <LazyWrapper>
            <ModelDetailPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'probes',
        element: (
          <LazyWrapper>
            <ProbesPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'probes/new',
        element: (
          <LazyWrapper>
            <NewProbePage />
          </LazyWrapper>
        ),
      },
      {
        path: 'probes/:id',
        element: (
          <LazyWrapper>
            <ProbeDetailPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'probes/:id/edit',
        element: (
          <LazyWrapper>
            <EditProbePage />
          </LazyWrapper>
        ),
      },
      {
        path: 'settings',
        element: (
          <LazyWrapper>
            <SettingsPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'demo/rules',
        element: (
          <LazyWrapper>
            <RuleEditorDemoPage />
          </LazyWrapper>
        ),
      },
    ],
  },
  {
    path: '*',
    element: <Navigate to='/dashboard' replace />,
  },
])