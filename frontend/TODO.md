# SACRA2 Frontend TODO

This plan is aligned with `docs/sacra2-frontend-architecture.md` and `.windsurf/rules/react-frontend-engineer.md`. It is tailored to the current code in `frontend/src/`.

Conventions: TypeScript strict, React 18, Vite 5, Ant Design 5, <PERSON><PERSON><PERSON><PERSON>, React Router 6, TanStack React Query 4, <PERSON>ust<PERSON>, Axios. Use pnpm for scripts and dependencies.

## Priority 0 – Foundation and DX

- [ ] __Project scripts and configs__
  - Ensure pnpm scripts cover: dev, build, preview, lint, format, test, test:watch, typecheck.
  - Add React Query DevTools only in dev.
  - Acceptance: `pnpm dev` runs, HMR OK; `pnpm test` passes sample tests.

- [ ] __Route protection sanity check__ (`src/routes.tsx`, `src/auth/*`)
  - Verify `ProtectedRoute` flow, loading states, and redirections.
  - Add lazy-loaded routes + Suspense fallbacks.
  - Acceptance: unauthenticated -> `/login`; authenticated -> `/dashboard`.

- [ ] __Axios interceptors + token refresh__ (`src/api/client.ts`, `src/auth/useAuth.ts`)
  - Confirm Authorization header injection and 401 refresh retry.
  - Acceptance: expired token auto-refreshes once; original request retried.

## Priority 1 – Dashboard

- [ ] __Dashboard metrics page__ (`src/pages/Dashboard/`)
  - Build `DashboardMetrics.tsx` using AntD Cards/Statistic and charts (`@ant-design/charts`).
  - Data: wire to `src/api/queries/useAnalytics.ts` (already present) and display KPI tiles, recent activity table, and trends.
  - Acceptance: charts render, loading/skeleton and error states implemented, a11y labels provided.

## Priority 2 – CRUD: Projects

- [ ] __Projects list__ (exists: `ProjectsTable.tsx`, `pages/Projects/index.tsx`)
  - Hook up filters, sorting, pagination to backend params (if not yet).
  - Add column actions: view, edit, delete with confirm.
  - Acceptance: server-driven pagination compatible; optimistic delete with React Query invalidation.

- [ ] __Project detail page__ (exists: `pages/Projects/ProjectDetail.tsx`)
  - Show project metadata, evaluations list (link to Evaluations detail), and activity timeline.
  - Acceptance: deep-linking works; loading/error states; empty state UX.

- [ ] __Project create/edit form__ (`src/components/forms/ProjectForm.tsx` – missing)
  - Implement AntD vertical form with validation, using `useCreateProject`/`useUpdateProject`.
  - Include cancel/back and success notifications.
  - Acceptance: create/edit flows navigate back and invalidate `['projects']`.

## Priority 3 – CRUD: Evaluations

- [ ] __Evaluations list__ (exists: `EvaluationsTable.tsx`, `pages/Evaluations/index.tsx`)
  - Ensure filters (by project, status, date) and server params.
  - Acceptance: filter state persists in URL query params; pagination OK.

- [ ] __Evaluation detail__ (exists: `pages/Evaluations/EvaluationDetail.tsx`)
  - Show metadata, results summary, charts for metrics, and related models.
  - Acceptance: charts render with real data; error boundaries in place.

- [ ] __Evaluation create form__ (`src/components/forms/EvaluationForm.tsx` – missing)
  - Use AntD/ProForm with selects for project/model and parameters.
  - On submit, trigger backend job; show processing status.
  - Acceptance: mutation success leads to detail page; invalidates `['evaluations']`.

## Priority 4 – CRUD: Models

- [ ] __Models list__ (exists: `ModelsTable.tsx`, `pages/Models/index.tsx` minimal)
  - Complete `pages/Models/index.tsx` with table filters/sorts and actions.
  - Acceptance: table fully operational with loading/empty/error states.

- [ ] __Model detail__ (`pages/Models/ModelDetail.tsx` – missing)
  - Show model metadata, linked evaluations, and configuration.
  - Acceptance: deep links work; related items navigable.

- [ ] __Model config form__ (`src/components/forms/ModelConfigForm.tsx` – missing)
  - Editable configuration (JSON editor optional) with validation.
  - Acceptance: update mutation with optimistic UI and rollback on error.

## Priority 5 – Rule Editing UX (Tree + Code Editor)

- [ ] __Tree view for probe rules__ (`src/components/rules/RuleTree.tsx` – new)
  - Use `react-arborist` with VSCode-like icons; CRUD: create/rename/delete/move; keyboard navigation; async loading.
  - State model: `TreeNode { id, name, type: 'folder'|'file', children? }`.
  - Acceptance: operations persist via API, React Query cache invalidation occurs.

- [ ] __Code editor__ (`src/components/rules/RuleEditor.tsx` – new)
  - Use `@codeium/react-code-editor` for JSON/YAML/TS rules with syntax highlighting.
  - Acceptance: save with debounce; error markers displayed; dirty state warning on navigation.

- [ ] __Rules page__ (`src/pages/Rules/index.tsx` – new)
  - Split layout: left tree, right editor and preview.
  - Acceptance: end‑to‑end editing flow works and is protected by auth.

## Priority 6 – Layout and Navigation

- [ ] __App layout__ (exists: `components/layout/AppLayout.tsx`)
  - Ensure responsive sidebar, header actions (`NotificationDropdown.tsx`), breadcrumbs, and content container.
  - Acceptance: collapsible sidebar persisted via Zustand store (`stores/*`).

- [ ] __Global theming__
  - Use AntD tokens; provide light/dark/system theme toggle stored in Zustand (`stores/appStore.ts` – add if missing).
  - Acceptance: theme choice persists (localStorage) and honors prefers-color-scheme when system.

## Priority 7 – API Layer and Types

- [ ] __Endpoint typing__ (`src/api/endpoints.ts`, `src/types/*`)
  - Ensure all request/response shapes are fully typed and reused in components/hooks.
  - Acceptance: no `any` in API layer; strict mode passes.

- [ ] __Queries hooks audit__ (`src/api/queries/*`)
  - Confirm query keys, caching times, retries, and optimistic updates are appropriate.
  - Acceptance: staleTime and retry policies documented near hooks.

## Priority 8 – Cross‑Cutting Concerns

- [ ] __Error and empty states__
  - Consistent `Result`, `Empty`, `Alert`, and `message` usage across pages.
  - Acceptance: visually consistent across Projects/Evaluations/Models.

- [ ] __Accessibility (WCAG 2.1 AA)__
  - Keyboard/focus order, ARIA labels for interactive elements, skip‑to‑content link.
  - Acceptance: keyboard‑only navigation works; color contrast meets AA.

- [ ] __Performance__
  - Lazy routes, memoize heavy tables, virtualization for long lists, bundle analysis.
  - Acceptance: Lighthouse perf > 85 on dashboard; first load < 3s on dev machine.

## Priority 9 – Testing

- [ ] __Unit tests__ (Vitest + RTL)
  - Components: tables, forms, layout pieces.
  - Hooks: auth, queries, stores.
  - Acceptance: coverage baseline ≥ 60% lines; green in CI.

- [ ] __Integration tests__ (MSW)
  - Auth flow, list + create/edit/delete flows, error paths.
  - Acceptance: realistic mocked APIs; no network flakiness.

- [ ] __E2E tests__ (Playwright)
  - Smoke: login, navigate, CRUD happy paths.
  - Acceptance: CI job runs headless and artifacts saved on failure.

## Priority 10 – DevEx and CI

- [ ] __Husky + lint‑staged__
  - Pre‑commit: lint and format staged files.
  - Acceptance: commit blocked on lint errors; auto‑format applies.

- [ ] __Storybook (optional but recommended)__
  - For complex components (tables/forms/editor/tree).
  - Acceptance: stories render with controls; basic a11y addon enabled.

## Quick File Map (planned additions)

- `src/components/forms/ProjectForm.tsx`
- `src/components/forms/EvaluationForm.tsx`
- `src/components/forms/ModelConfigForm.tsx`
- `src/components/rules/RuleTree.tsx`
- `src/components/rules/RuleEditor.tsx`
- `src/pages/Rules/index.tsx`
- `src/stores/appStore.ts` (if not present)

## Notes

- Follow SOLID and clean architecture. Keep components small and reusable.
- Prefer composition over inheritance. Keep state close to where it’s used; use React Query for server state.
- Do not store access tokens in localStorage. Refresh token in httpOnly cookie only.
- Use descriptive names and inline documentation for public components and hooks.
