import React, { useState, useCallback, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { ChevronRight, ChevronLeft, ChevronDown, File, Folder, FolderOpen, Plus, MoreHorizontal, Edit, Trash2, Co<PERSON>, Search } from 'lucide-react';

declare global {
  interface Window {
    monaco: any;
    require: any;
  }
}

// Tree Node Interface
interface TreeNode {
  id: string;
  name: string;
  type: 'folder' | 'file';
  extension?: string;
  content?: string;
  children?: TreeNode[];
  isExpanded?: boolean;
}

// Mock data structure for probe rules
const mockTreeData: TreeNode[] = [
  {
    id: '1',
    name: 'probe-001',
    type: 'folder',
    isExpanded: true,
    children: [
      {
        id: '2',
        name: 'rules',
        type: 'folder',
        isExpanded: true,
        children: [
          {
            id: '3',
            name: 'validation.json',
            type: 'file',
            extension: 'json',
            content: '{\n  "rules": [\n    {\n      "id": "rule-001",\n      "name": "Check response time",\n      "condition": "response_time < 1000",\n      "severity": "warning"\n    }\n  ]\n}'
          },
          {
            id: '4',
            name: 'transform.yaml',
            type: 'file',
            extension: 'yaml',
            content: 'transform:\n  - name: normalize_data\n    type: json_path\n    path: "$.data"\n  - name: validate_schema\n    type: schema_validation\n    schema: "./schemas/response.json"'
          }
        ]
      },
      {
        id: '5',
        name: 'config',
        type: 'folder',
        children: [
          {
            id: '6',
            name: 'settings.ts',
            type: 'file',
            extension: 'ts',
            content: 'export interface ProbeSettings {\n  interval: number;\n  timeout: number;\n  retries: number;\n}\n\nexport const defaultSettings: ProbeSettings = {\n  interval: 30000,\n  timeout: 5000,\n  retries: 3\n};'
          }
        ]
      }
    ]
  },
  {
    id: '7',
    name: 'probe-002',
    type: 'folder',
    children: [
      {
        id: '8',
        name: 'rules',
        type: 'folder',
        children: [
          {
            id: '9',
            name: 'security.json',
            type: 'file',
            extension: 'json',
            content: '{\n  "security_rules": [\n    {\n      "check": "ssl_cert_validity",\n      "min_days": 30\n    },\n    {\n      "check": "header_security",\n      "required_headers": ["X-Frame-Options", "X-Content-Type-Options"]\n    }\n  ]\n}'
          }
        ]
      }
    ]
  }
];

// VSCode-style File Icon Component
const getFileIcon = (node: TreeNode) => {
  if (node.type === 'folder') {
    return node.isExpanded ? (
      <FolderOpen className="w-4 h-4 text-blue-500" />
    ) : (
      <Folder className="w-4 h-4 text-blue-500" />
    );
  }

  // VSCode-style file icons based on extension
  const extension = node.extension?.toLowerCase();
  
  // Create VSCode-style file icons
  const createFileIcon = (color: string, symbol?: string) => (
    <div className="w-5 h-4 flex items-center justify-center relative">
      <svg width="18" height="16" viewBox="0 0 18 16" fill="none">
        <path d="M2 1.5V14.5H16V4.5L13 1.5H2Z" fill={color} opacity="0.9"/>
        <path d="M13 1.5V4.5H16" stroke="#ffffff" strokeWidth="0.5" fill="none" opacity="0.3"/>
        <path d="M2 1.5H13V4.5H16V14.5H2V1.5Z" stroke="#ffffff" strokeWidth="0.3" fill="none" opacity="0.2"/>
      </svg>
      {symbol && (
        <span className="absolute text-white text-xs font-semibold" style={{ fontSize: '6px', top: '4px', left: '50%', transform: 'translateX(-50%)' }}>
          {symbol}
        </span>
      )}
    </div>
  );

  switch (extension) {
    case 'yaml':
    case 'yml':
      return createFileIcon('#ff6b6b', 'YML');
    case 'ts':
      return createFileIcon('#3178c6', 'TS');
    case 'tsx':
      return createFileIcon('#61dafb', 'TSX');
    case 'json':
      return createFileIcon('#ffd700', 'JSON');
    case 'jsonl':
      return createFileIcon('#ffd700', 'JSONL');
    case 'js':
    case 'jsx':
      return createFileIcon('#f7df1e', 'JS');
    case 'md':
      return createFileIcon('#083fa1', 'MD');
    case 'css':
    case 'scss':
      return createFileIcon('#1572b6', 'CSS');
    case 'html':
      return createFileIcon('#e34f26', 'HTML');
    case 'py':
      return createFileIcon('#3776ab', 'PY');
    default:
      return createFileIcon('#6b7280', '');
  }
};

// Monaco Editor language mapping
const getMonacoLanguage = (extension?: string) => {
  switch (extension) {
    case 'json':
      return 'json';
    case 'yaml':
    case 'yml':
      return 'yaml';
    case 'ts':
      return 'typescript';
    case 'js':
      return 'javascript';
    case 'md':
      return 'markdown';
    case 'xml':
      return 'xml';
    case 'html':
      return 'html';
    case 'css':
      return 'css';
    case 'scss':
      return 'scss';
    case 'sql':
      return 'sql';
    case 'py':
      return 'python';
    case 'java':
      return 'java';
    case 'cpp':
    case 'cc':
    case 'cxx':
      return 'cpp';
    case 'c':
      return 'c';
    case 'cs':
      return 'csharp';
    case 'php':
      return 'php';
    case 'rb':
      return 'ruby';
    case 'go':
      return 'go';
    case 'rs':
      return 'rust';
    case 'kt':
      return 'kotlin';
    case 'scala':
      return 'scala';
    case 'sh':
    case 'bash':
      return 'shell';
    case 'dockerfile':
      return 'dockerfile';
    case 'ini':
    case 'toml':
      return 'ini';
    case 'vue':
      return 'vue';
    case 'jsx':
      return 'javascriptreact';
    case 'tsx':
      return 'typescriptreact';
    default:
      return 'plaintext';
  }
};

// Monaco Editor Component using CDN
const MonacoEditor = ({ value, language, onChange, fileName, theme = 'vs-dark', onEditorMount }: { value: string; language?: string; onChange?: (v: string) => void; fileName: string; theme?: string; onEditorMount?: (editor: any, monaco: any) => void; }) => {
  const editorRef = useRef(null);
  const containerRef = useRef(null);
  const monacoRef = useRef(null);

  useEffect(() => {
    // Load Monaco Editor from CDN
    const loadMonaco = async () => {
      if (window.monaco) {
        initializeEditor();
        return;
      }

      // Create script tag for Monaco loader
      const script = document.createElement('script');
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs/loader.min.js';
      script.onload = () => {
        window.require.config({
          paths: { vs: 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs' }
        });

        window.require(['vs/editor/editor.main'], () => {
          initializeEditor();
        });
      };
      document.head.appendChild(script);
    };

    const initializeEditor = () => {
      if (!containerRef.current || editorRef.current) return;

      // Configure Monaco themes and languages
      window.monaco.editor.defineTheme('vscode-dark', {
        base: 'vs-dark',
        inherit: true,
        rules: [
          { token: 'comment', foreground: '6A9955' },
          { token: 'keyword', foreground: 'C586C0' },
          { token: 'string', foreground: 'CE9178' },
          { token: 'number', foreground: 'B5CEA8' },
          { token: 'type', foreground: '4EC9B0' },
        ],
        colors: {
          'editor.background': '#1e1e1e',
          'editor.foreground': '#d4d4d4',
          'editor.lineHighlightBackground': '#2d2d30',
          'editor.selectionBackground': '#264f78',
          'editorCursor.foreground': '#ffffff',
          'editorLineNumber.foreground': '#858585',
          'editorLineNumber.activeForeground': '#ffffff',
        }
      });

      // Create the editor
      const chosenTheme = theme === 'vs-dark' ? 'vscode-dark' : theme;
      const editor = window.monaco.editor.create(containerRef.current, {
        value: value || '',
        language: getMonacoLanguage(language),
        theme: chosenTheme,
        automaticLayout: true,
        fontSize: 14,
        fontFamily: 'Consolas, "Courier New", monospace',
        lineNumbers: 'on',
        roundedSelection: false,
        scrollBeyondLastLine: false,
        readOnly: false,
        minimap: { enabled: true },
        folding: true,
        lineHeight: 20,
        letterSpacing: 0.5,
        wordWrap: 'off',
        scrollbar: {
          verticalScrollbarSize: 10,
          horizontalScrollbarSize: 10,
        },
        suggest: {
          insertMode: 'replace',
        },
        quickSuggestions: {
          other: true,
          comments: false,
          strings: false
        },
        parameterHints: {
          enabled: true
        },
        hover: {
          enabled: true
        },
        contextmenu: true,
        mouseWheelZoom: true,
        cursorBlinking: 'smooth',
        cursorSmoothCaretAnimation: true,
        smoothScrolling: true,
        bracketPairColorization: {
          enabled: true
        },
        guides: {
          bracketPairs: true,
          indentation: true
        }
      });

      // Store references
      editorRef.current = editor;
      monacoRef.current = window.monaco;

      // Notify parent
      onEditorMount?.(editor, window.monaco);

      // Listen for changes
      editor.onDidChangeModelContent(() => {
        const value = editor.getValue();
        onChange?.(value);
      });

      // Add custom key bindings
      editor.addCommand(window.monaco.KeyMod.CtrlCmd | window.monaco.KeyCode.KeyS, () => {
        console.log('Save shortcut pressed');
        // Here you could implement save functionality
      });

      // Format document on Shift+Alt+F
      editor.addCommand(
        window.monaco.KeyMod.Shift | window.monaco.KeyMod.Alt | window.monaco.KeyCode.KeyF,
        () => {
          editor.trigger('', 'editor.action.formatDocument', {});
        }
      );
    };

    loadMonaco();

    return () => {
      if (editorRef.current) {
        editorRef.current.dispose();
        editorRef.current = null;
      }
    };
  }, []);

  // Update editor value when prop changes
  useEffect(() => {
    if (editorRef.current && editorRef.current.getValue() !== value) {
      editorRef.current.setValue(value || '');
    }
  }, [value]);

  // Update editor language when prop changes
  useEffect(() => {
    if (editorRef.current && monacoRef.current) {
      const model = editorRef.current.getModel();
      if (model) {
        monacoRef.current.editor.setModelLanguage(model, getMonacoLanguage(language));
      }
    }
  }, [language]);

  return (
    <div className="flex-1 flex flex-col">
      {/* Editor Header */}
      <div className="bg-gray-800 px-4 py-2 border-b border-gray-600 flex items-center gap-2">
        {language ? getFileIcon({ id: 'temp', name: fileName, type: 'file', extension: language }) : getFileIcon({ id: 'temp', name: fileName, type: 'file' })}
        <span className="text-gray-200 text-sm">{fileName}</span>
        <div className="ml-auto text-xs text-gray-400 uppercase">
          {getMonacoLanguage(language)}
        </div>
        <div className="w-2 h-2 rounded-full bg-white opacity-70"></div>
      </div>

      {/* Monaco Editor Container */}
      <div
        ref={containerRef}
        className="flex-1"
        style={{ minHeight: '200px' }}
      />

      {/* Status Bar */}
      <div className="bg-blue-600 text-white text-xs px-4 py-1 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <span>Monaco Editor</span>
          <span>UTF-8</span>
          <span>{getMonacoLanguage(language).toUpperCase()}</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-green-300">●</span>
          <span>Ready</span>
        </div>
      </div>
    </div>
  );
};

// Context Menu Component
const ContextMenu = ({ x, y, onClose, onAction, node }) => {
  const handleAction = (action: string) => {
    onAction(action, node);
    onClose();
  };

  return (
    <div
      className="fixed bg-gray-800 border border-gray-600 rounded-md shadow-lg z-50 py-1 min-w-32"
      style={{ left: x, top: y }}
      onClick={(e) => e.stopPropagation()}
    >
      {node.type === 'folder' && (
        <>
          <button
            className="w-full px-3 py-1 text-left hover:bg-gray-700 text-sm flex items-center gap-2"
            onClick={() => handleAction('createFile')}
          >
            <File className="w-3 h-3" />
            New File
          </button>
          <button
            className="w-full px-3 py-1 text-left hover:bg-gray-700 text-sm flex items-center gap-2"
            onClick={() => handleAction('createFolder')}
          >
            <Folder className="w-3 h-3" />
            New Folder
          </button>
          <hr className="border-gray-600 my-1" />
        </>
      )}
      <button
        className="w-full px-3 py-1 text-left hover:bg-gray-700 text-sm flex items-center gap-2"
        onClick={() => handleAction('rename')}
      >
        <Edit className="w-3 h-3" />
        Rename
      </button>
      <button
        className="w-full px-3 py-1 text-left hover:bg-gray-700 text-sm flex items-center gap-2"
        onClick={() => handleAction('copy')}
      >
        <Copy className="w-3 h-3" />
        Duplicate
      </button>
      <hr className="border-gray-600 my-1" />
      <button
        className="w-full px-3 py-1 text-left hover:bg-red-600 text-sm flex items-center gap-2"
        onClick={() => handleAction('delete')}
      >
        <Trash2 className="w-3 h-3" />
        Delete
      </button>
    </div>
  );
};

// Tree Item Component with VSCode-like actions and improved drag & drop
const TreeItem = ({ node, level = 0, onSelect, selectedId, onToggle, onContextMenu, onRename, onCreateFile, onCreateFolder, onMoveNode, onDelete }) => {
  const [isRenaming, setIsRenaming] = useState(false);
  const [newName, setNewName] = useState(node.name);
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragPosition, setDragPosition] = useState<'before' | 'after' | 'inside' | null>(null);

  const handleRename = () => {
    if (newName.trim() && newName !== node.name) {
      onRename(node.id, newName.trim());
    }
    setIsRenaming(false);
    setNewName(node.name);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename();
    } else if (e.key === 'Escape') {
      setIsRenaming(false);
      setNewName(node.name);
    }
  };

  const handleItemClick = () => {
    onSelect(node);
  };

  const handleItemDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (node.type === 'folder') {
      onToggle(node.id);
    } else {
      onSelect(node);
    }
  };

  const handleItemKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && selectedId === node.id) {
      e.preventDefault();
      setIsRenaming(true);
    } else if (e.key === 'Delete' && selectedId === node.id) {
      onDelete(node.id);
    }
  };

  const startRename = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsRenaming(true);
  };

  // Improved Drag & Drop handlers
  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData('application/json', JSON.stringify({
      id: node.id,
      name: node.name,
      type: node.type
    }));
    e.dataTransfer.effectAllowed = 'move';
    setIsDragging(true);

    // Add visual feedback
    setTimeout(() => {
      const element = e.target as HTMLElement;
      element.style.opacity = '0.5';
    }, 0);
  };

  const handleDragEnd = (e: React.DragEvent) => {
    setIsDragging(false);
    const element = e.target as HTMLElement;
    element.style.opacity = '1';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';

    if (isDragging) return; // Don't allow drop on self

    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const y = e.clientY - rect.top;
    const height = rect.height;

    let position: 'before' | 'after' | 'inside' | null = null;

    if (node.type === 'folder') {
      // For folders: before (top 25%), inside (middle 50%), after (bottom 25%)
      if (y < height * 0.25) {
        position = 'before';
      } else if (y > height * 0.75) {
        position = 'after';
      } else {
        position = 'inside';
      }
    } else {
      // For files: before (top 50%), after (bottom 50%)
      position = y < height * 0.5 ? 'before' : 'after';
    }

    setDragPosition(position);
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // Only hide if leaving the item completely
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
      setDragPosition(null);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    setDragPosition(null);

    if (isDragging) return; // Don't allow drop on self

    try {
      const dragData = JSON.parse(e.dataTransfer.getData('application/json'));
      if (dragData.id === node.id) return; // Don't drop on self

      if (onMoveNode) {
        onMoveNode(dragData.id, node.id, dragPosition);
      }
    } catch (error) {
      console.error('Error parsing drag data:', error);
    }
  };

  React.useEffect(() => {
    if (isRenaming) {
      const input = document.getElementById(`rename-${node.id}`);
      if (input) {
        (input as HTMLInputElement).focus();
        (input as HTMLInputElement).select();
      }
    }
  }, [isRenaming, node.id]);

  // Drop indicator styles
  const getDropIndicatorClass = () => {
    if (!isDragOver || !dragPosition) return '';

    switch (dragPosition) {
      case 'before':
        return 'border-t-2 border-blue-400';
      case 'after':
        return 'border-b-2 border-blue-400';
      case 'inside':
        return 'bg-blue-900 bg-opacity-30 border border-blue-400 border-opacity-50';
      default:
        return '';
    }
  };

  return (
    <div>
      <div
        className={`group flex items-center gap-1 px-2 py-0.5 hover:bg-gray-700 cursor-pointer text-sm relative transition-all ${selectedId === node.id ? 'bg-gray-600' : ''
          } ${isDragging ? 'opacity-50' : ''} ${getDropIndicatorClass()}`}
        style={{ paddingLeft: `${level * 12 + 8}px`, color: '#ffffff' }}
        onClick={handleItemClick}
        onDoubleClick={handleItemDoubleClick}
        onContextMenu={(e) => onContextMenu(e, node)}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onKeyDown={handleItemKeyDown}
        tabIndex={0}
        draggable={!isRenaming}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {node.type === 'folder' ? (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggle(node.id);
            }}
            className="flex items-center justify-center w-2 h-2 hover:bg-gray-600 rounded text-gray-100 hover:text-white"
          >
            {node.isExpanded ? (
              <ChevronDown className="w-2 h-2" />
            ) : (
              <ChevronRight className="w-2 h-2" />
            )}
          </button>
        ) : (
          <div className="w-2" />
        )}

        {getFileIcon(node)}

        {isRenaming ? (
          <input
            id={`rename-${node.id}`}
            type="text"
            value={newName}
            onChange={(e) => setNewName(e.target.value)}
            onBlur={handleRename}
            onKeyDown={handleKeyDown}
            className="bg-gray-600 text-white px-1 py-0 text-sm rounded border-none outline-none flex-1"
            draggable={false}
          />
        ) : (
          <span className="text-white flex-1 select-none font-normal">{node.name}</span>
        )}

        {/* VSCode-style action buttons */}
        {(node.type === 'folder' ? (isHovered || selectedId === node.id) : (selectedId === node.id || isHovered)) && !isDragging && !isRenaming && (
          <div className="flex items-center gap-0.5 ml-auto opacity-0 group-hover:opacity-100 transition-opacity">
            {/* Folder actions */}
            {node.type === 'folder' && (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onCreateFile(node.id);
                  }}
                  className="p-0.5 hover:bg-gray-600 rounded text-gray-100 hover:text-white"
                  title="New File"
                >
                  <Plus className="w-2 h-2" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onCreateFolder(node.id);
                  }}
                  className="p-0.5 hover:bg-gray-600 rounded text-gray-100 hover:text-white"
                  title="New Folder"
                >
                  <Folder className="w-2 h-2" />
                </button>
              </>
            )}

            {/* Rename action for both files and folders */}
            <button
              onClick={startRename}
              className="p-0.5 hover:bg-gray-600 rounded text-gray-100 hover:text-white"
              title="Rename"
            >
              <Edit className="w-2 h-2" />
            </button>
          </div>
        )}

        {/* Show selection indicator when item is selected */}
        {selectedId === node.id && !isRenaming && !isDragging && (
          <div className="absolute right-2 text-xs text-gray-500">
            Press Enter to rename
          </div>
        )}

        {/* Drag position indicator */}
        {isDragOver && dragPosition === 'inside' && node.type === 'folder' && (
          <div className="absolute right-2 text-xs text-blue-400">
            Drop inside folder
          </div>
        )}
      </div>

      {node.type === 'folder' && node.isExpanded && node.children && (
        <div>
          {node.children.map((child) => (
            <TreeItem
              key={child.id}
              node={child}
              level={level + 1}
              onSelect={onSelect}
              selectedId={selectedId}
              onToggle={onToggle}
              onContextMenu={onContextMenu}
              onRename={onRename}
              onCreateFile={onCreateFile}
              onCreateFolder={onCreateFolder}
              onMoveNode={onMoveNode}
              onDelete={onDelete}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Main Editor Component Props
interface VSCodeRulesEditorProps {
  initialTreeData?: TreeNode[];
}

export interface VSCodeRulesEditorHandle {
  format: () => void;
  save: () => { fileId?: string; fileName?: string; content?: string } | undefined;
  reload: () => void;
  run: () => void;
}

// Main Editor Component
const VSCodeRulesEditor = forwardRef<VSCodeRulesEditorHandle, VSCodeRulesEditorProps>(({ initialTreeData }, ref) => {
  const [treeData, setTreeData] = useState(initialTreeData || mockTreeData);
  const [selectedFile, setSelectedFile] = useState<TreeNode | null>(null);
  const [contextMenu, setContextMenu] = useState<{ x: number, y: number, node: TreeNode } | null>(null);
  const [nextId, setNextId] = useState(10); // For generating new IDs
  const [searchQuery, setSearchQuery] = useState('');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(320); // px, ~w-80
  const [isResizing, setIsResizing] = useState(false);

  // Monaco editor refs
  const monacoEditorRef = useRef<any>(null);
  const monacoApiRef = useRef<any>(null);

  // Recursive function to find and update nodes
  const updateNodeInTree = (nodes: TreeNode[], nodeId: string, updater: (node: TreeNode) => TreeNode): TreeNode[] => {
    return nodes.map(node => {
      if (node.id === nodeId) {
        return updater(node);
      }
      if (node.children) {
        return { ...node, children: updateNodeInTree(node.children, nodeId, updater) };
      }
      return node;
    });
  };

  // Recursive function to add new nodes
  const addNodeToTree = (nodes: TreeNode[], parentId: string, newNode: TreeNode): TreeNode[] => {
    return nodes.map(node => {
      if (node.id === parentId) {
        return {
          ...node,
          children: [...(node.children || []), newNode],
          isExpanded: true // Expand parent when adding child
        };
      }
      if (node.children) {
        return { ...node, children: addNodeToTree(node.children, parentId, newNode) };
      }
      return node;
    });
  };

  // Recursive function to remove nodes
  const removeNodeFromTree = (nodes: TreeNode[], nodeId: string): TreeNode[] => {
    return nodes.filter(node => node.id !== nodeId).map(node => {
      if (node.children) {
        return { ...node, children: removeNodeFromTree(node.children, nodeId) };
      }
      return node;
    });
  };

  // Find node by ID recursively
  const findNodeById = (nodes: TreeNode[], nodeId: string): TreeNode | null => {
    for (const node of nodes) {
      if (node.id === nodeId) {
        return node;
      }
      if (node.children) {
        const found = findNodeById(node.children, nodeId);
        if (found) return found;
      }
    }
    return null;
  };

  // Filter tree based on search query
  const filterTree = (nodes: TreeNode[], query: string): TreeNode[] => {
    if (!query) return nodes;

    return nodes.filter(node => {
      if (node.name.toLowerCase().includes(query.toLowerCase())) {
        return true;
      }
      if (node.children) {
        const filteredChildren = filterTree(node.children, query);
        if (filteredChildren.length > 0) {
          return true;
        }
      }
      return false;
    }).map(node => {
      if (node.children) {
        const filteredChildren = filterTree(node.children, query);
        return {
          ...node,
          children: filteredChildren,
          isExpanded: filteredChildren.length > 0 || node.isExpanded
        };
      }
      return node;
    });
  };

  const filteredTreeData = filterTree(treeData, searchQuery);

  const handleToggle = useCallback((nodeId: string) => {
    setTreeData(prevData =>
      updateNodeInTree(prevData, nodeId, node => ({
        ...node,
        isExpanded: !node.isExpanded
      }))
    );
  }, []);

  const handleSelect = useCallback((node: TreeNode) => {
    if (node.type === 'file') {
      setSelectedFile(node);
    }
  }, []);

  const handleContextMenu = useCallback((e: React.MouseEvent, node: TreeNode) => {
    e.preventDefault();
    setContextMenu({
      x: e.clientX,
      y: e.clientY,
      node
    });
  }, []);

  const handleContextAction = useCallback((action: string, node: TreeNode) => {
    switch (action) {
      case 'createFile':
        handleCreateFile(node.id);
        break;
      case 'createFolder':
        handleCreateFolder(node.id);
        break;
      case 'rename':
        // Rename is handled by TreeItem component
        break;
      case 'copy':
        handleDuplicateNode(node);
        break;
      case 'delete':
        handleDeleteNode(node.id);
        break;
    }
  }, []);

  const handleRename = useCallback((nodeId: string, newName: string) => {
    setTreeData(prevData =>
      updateNodeInTree(prevData, nodeId, node => ({
        ...node,
        name: newName
      }))
    );

    // Update selected file if it's the one being renamed
    if (selectedFile && selectedFile.id === nodeId) {
      setSelectedFile(prev => prev ? { ...prev, name: newName } : null);
    }
  }, [selectedFile]);

  const handleCreateFile = useCallback((parentId: string) => {
    const newFile: TreeNode = {
      id: nextId.toString(),
      name: 'new-file.json',
      type: 'file',
      extension: 'json',
      content: '{\n  "new": "file"\n}'
    };

    setTreeData(prevData => addNodeToTree(prevData, parentId, newFile));
    setNextId(prev => prev + 1);

    // Auto-select the new file for editing
    setTimeout(() => setSelectedFile(newFile), 100);
  }, [nextId]);

  const handleCreateFolder = useCallback((parentId: string) => {
    const newFolder: TreeNode = {
      id: nextId.toString(),
      name: 'new-folder',
      type: 'folder',
      children: [],
      isExpanded: false
    };

    setTreeData(prevData => addNodeToTree(prevData, parentId, newFolder));
    setNextId(prev => prev + 1);
  }, [nextId]);

  const handleDeleteNode = useCallback((nodeId: string) => {
    if (confirm('Are you sure you want to delete this item?')) {
      setTreeData(prevData => removeNodeFromTree(prevData, nodeId));

      // Clear selected file if it's being deleted
      if (selectedFile && selectedFile.id === nodeId) {
        setSelectedFile(null);
      }
    }
  }, [selectedFile]);

  const handleDuplicateNode = useCallback((node: TreeNode) => {
    const duplicateNode = (original: TreeNode): TreeNode => ({
      ...original,
      id: (nextId + Math.random()).toString(),
      name: `${original.name.replace(/\.[^/.]+$/, '')}_copy${original.extension ? '.' + original.extension : ''}`,
      children: original.children?.map(duplicateNode)
    });

    const newNode = duplicateNode(node);

    // Find parent and add duplicated node
    const addToSameLevel = (nodes: TreeNode[]): TreeNode[] => {
      const index = nodes.findIndex(n => n.id === node.id);
      if (index !== -1) {
        const newNodes = [...nodes];
        newNodes.splice(index + 1, 0, newNode);
        return newNodes;
      }

      return nodes.map(n => ({
        ...n,
        children: n.children ? addToSameLevel(n.children) : undefined
      }));
    };

    setTreeData(prevData => addToSameLevel(prevData));
    setNextId(prev => prev + 1);
  }, [nextId]);

  const handleMoveNode = useCallback((dragId: string, dropId: string, position: 'before' | 'after' | 'inside' | null) => {
    if (!position) return;

    const dragNode = findNodeById(treeData, dragId);
    if (!dragNode) return;

    // Remove dragged node from tree
    let newTreeData = removeNodeFromTree(treeData, dragId);

    if (position === 'inside') {
      // Add to children of drop target
      newTreeData = addNodeToTree(newTreeData, dropId, dragNode);
    } else {
      // Add before or after drop target
      const insertNode = (nodes: TreeNode[]): TreeNode[] => {
        const index = nodes.findIndex(n => n.id === dropId);
        if (index !== -1) {
          const insertIndex = position === 'before' ? index : index + 1;
          const newNodes = [...nodes];
          newNodes.splice(insertIndex, 0, dragNode);
          return newNodes;
        }

        return nodes.map(n => ({
          ...n,
          children: n.children ? insertNode(n.children) : undefined
        }));
      };

      newTreeData = insertNode(newTreeData);
    }

    setTreeData(newTreeData);
  }, [treeData]);

  const handleCodeChange = useCallback((newContent: string) => {
    if (selectedFile) {
      setTreeData(prevData =>
        updateNodeInTree(prevData, selectedFile.id, node => ({
          ...node,
          content: newContent
        }))
      );
      setSelectedFile(prev => prev ? { ...prev, content: newContent } : null);
    }
  }, [selectedFile]);

  // Expose imperative actions to parent via ref
  useImperativeHandle(ref, () => ({
    format: () => {
      if (monacoEditorRef.current && monacoApiRef.current) {
        monacoEditorRef.current.trigger('', 'editor.action.formatDocument', {});
      }
    },
    save: () => {
      if (!selectedFile) return undefined;
      const modelContent = monacoEditorRef.current ? monacoEditorRef.current.getValue() : selectedFile.content;
      return { fileId: selectedFile.id, fileName: selectedFile.name, content: modelContent };
    },
    reload: () => {
      // Reset to initial tree data and clear selection
      setTreeData(initialTreeData || mockTreeData);
      setSelectedFile(null);
    },
    run: () => {
      // Placeholder for running validations/tests
      // eslint-disable-next-line no-console
      console.log('Run action triggered for', selectedFile?.name);
    }
  }), [selectedFile, initialTreeData]);

  // Close context menu when clicking elsewhere
  React.useEffect(() => {
    const handleClick = () => setContextMenu(null);
    if (contextMenu) {
      document.addEventListener('click', handleClick);
      return () => document.removeEventListener('click', handleClick);
    }
  }, [contextMenu]);

  // Sidebar resizing handlers
  const startResizing = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
  }, []);

  useEffect(() => {
    const onMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      const min = 200; // px
      const max = 600; // px
      const next = Math.max(min, Math.min(max, e.clientX));
      if (isSidebarCollapsed) {
        setIsSidebarCollapsed(false);
      }
      setSidebarWidth(next);
    };
    const onMouseUp = () => setIsResizing(false);

    if (isResizing) {
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
      document.body.style.userSelect = 'none';
      document.body.style.cursor = 'col-resize';
    }

    return () => {
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
      document.body.style.userSelect = '';
      document.body.style.cursor = '';
    };
  }, [isResizing, isSidebarCollapsed]);

  return (
    <div className="flex h-screen bg-gray-900 text-gray-200">
      {/* Sidebar - File Explorer */}
      <div
        className="bg-gray-800 border-r border-gray-600 flex flex-col"
        style={{ width: isSidebarCollapsed ? 0 : sidebarWidth, transition: 'width 0.1s ease', overflow: 'hidden' }}
      >
        <div className="p-3 border-b border-gray-600">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-sm font-medium text-gray-200">PROBE RULES EXPLORER</h2>
            <button
              onClick={() => setIsSidebarCollapsed(true)}
              className="p-1 rounded hover:bg-gray-700 text-gray-300"
              title="Collapse sidebar"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
          </div>
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search files..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-8 pr-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded text-gray-200 placeholder-gray-400 focus:outline-none focus:border-blue-400"
            />
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          {filteredTreeData.map((node) => (
            <TreeItem
              key={node.id}
              node={node}
              onSelect={handleSelect}
              selectedId={selectedFile?.id}
              onToggle={handleToggle}
              onContextMenu={handleContextMenu}
              onRename={handleRename}
              onCreateFile={handleCreateFile}
              onCreateFolder={handleCreateFolder}
              onMoveNode={handleMoveNode}
              onDelete={handleDeleteNode}
            />
          ))}
        </div>
      </div>

      {/* Vertical Resizer */}
      <div
        className={`w-1.5 ${isResizing ? 'bg-blue-500' : 'bg-gray-700 hover:bg-gray-600'} cursor-col-resize relative`}
        onMouseDown={startResizing}
      >
        <button
          onClick={(e) => { e.stopPropagation(); setIsSidebarCollapsed((v) => !v); }}
          className="absolute top-2 left-1/2 -translate-x-1/2 transform bg-gray-800 border border-gray-600 rounded-full p-0.5 hover:bg-gray-700"
          title={isSidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {isSidebarCollapsed ? (
            <ChevronRight className="w-3 h-3 text-gray-300" />
          ) : (
            <ChevronLeft className="w-3 h-3 text-gray-300" />
          )}
        </button>
      </div>

      {/* Main Editor Area */}
      <div className="flex-1 flex flex-col">
        {selectedFile ? (
          <MonacoEditor
            value={selectedFile.content || ''}
            {...(selectedFile.extension ? { language: selectedFile.extension } : {})}
            onChange={handleCodeChange}
            fileName={selectedFile.name}
            onEditorMount={(editor, monaco) => { monacoEditorRef.current = editor; monacoApiRef.current = monaco; }}
          />
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-900">
            <div className="text-center">
              <File className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400 mb-2">Select a file to start editing</p>
              <p className="text-gray-500 text-sm">Monaco Editor will load automatically</p>
            </div>
          </div>
        )}
      </div>

      {/* Context Menu */}
      {contextMenu && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          onClose={() => setContextMenu(null)}
          onAction={handleContextAction}
          node={contextMenu.node}
        />
      )}
    </div>
  );
});

export default VSCodeRulesEditor;
