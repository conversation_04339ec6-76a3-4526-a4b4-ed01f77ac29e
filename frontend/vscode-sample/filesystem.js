return [
    {
      id: '1',
      name: 'probe-001',
      type: 'folder',
      isExpanded: true,
      children: [
        {
          id: '2',
          name: 'rules',
          type: 'folder',
          isExpanded: true,
          children: [
            {
              id: '3',
              name: 'validation.json',
              type: 'file',
              extension: 'json',
              content: '{\n  "rules": [\n    {\n      "id": "rule-001",\n      "name": "Check response time",\n      "condition": "response_time < 1000",\n      "severity": "warning"\n    }\n  ]\n}'
            },
            {
              id: '4',
              name: 'transform.yaml',
              type: 'file',
              extension: 'yaml',
              content: 'transform:\n  - name: normalize_data\n    type: json_path\n    path: "$.data"\n  - name: validate_schema\n    type: schema_validation\n    schema: "./schemas/response.json"'
            }
          ]
        },
        {
          id: '5',
          name: 'config',
          type: 'folder',
          children: [
            {
              id: '6',
              name: 'settings.ts',
              type: 'file',
              extension: 'ts',
              content: 'export interface ProbeSettings {\n  interval: number;\n  timeout: number;\n  retries: number;\n}\n\nexport const defaultSettings: ProbeSettings = {\n  interval: 30000,\n  timeout: 5000,\n  retries: 3\n};'
            }
          ]
        }
      ]
    },
    {
      id: '7',
      name: 'probe-002',
      type: 'folder',
      children: [
        {
          id: '8',
          name: 'rules',
          type: 'folder',
          children: [
            {
              id: '9',
              name: 'security.json',
              type: 'file',
              extension: 'json',
              content: '{\n  "security_rules": [\n    {\n      "check": "ssl_cert_validity",\n      "min_days": 30\n    },\n    {\n      "check": "header_security",\n      "required_headers": ["X-Frame-Options", "X-Content-Type-Options"]\n    }\n  ]\n}'
            }
          ]
        }
      ]
    }
  ]