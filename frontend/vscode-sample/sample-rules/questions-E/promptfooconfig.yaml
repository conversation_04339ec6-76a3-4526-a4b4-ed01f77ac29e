description: 'System Prompt Leaking - Optimized Configuration with Python Assertions'

# Single prompt definition - shared by all tests

prompts:
  - file://system-prompt.yaml


# Provider configuration - configure your preferred provider
# For LiteLLM providers, requires LITELLM_API_KEY environment variable
providers:
  # OpenAI
  - id: openai:chat:gpt-5
    isReasoningModel: true
    config:
      # Suppress default max_tokens and use max_completion_tokens for gpt-5
      max_tokens: 0
      max_completion_tokens: 2048
  - id: openai:chat:gpt-5-mini
    isReasoningModel: true
    config:
      max_tokens: 0
      max_completion_tokens: 2048
  - id: openai:chat:gpt-5-nano
    isReasoningModel: true
    config:
      max_tokens: 0
      max_completion_tokens: 2048
  - id: litellm:chat:openai-gpt-4.1
  - id: litellm:chat:openai-gpt-4.1-mini
  - id: litellm:chat:openai-gpt-4.1-nano
  - id: litellm:chat:openai-gpt-4o
  - id: litellm:chat:openai-gpt-4o-mini
  - id: litellm:chat:openai-gpt-oss-120b
  - id: litellm:chat:openai-gpt-oss-20b

  # Misterious
  - id: litellm:chat:openrouter-horizon-beta
  # Anthropic
  - id: litellm:chat:anthropic-claude-opus-4-1
  - id: litellm:chat:anthropic-claude-opus-4
  - id: litellm:chat:anthropic-claude-3.7-sonnet:thinking
  - id: litellm:chat:anthropic-claude-sonnet-4
  - id: litellm:chat:anthropic-claude-3.7-sonnet
  - id: litellm:chat:anthropic-claude-3.5-sonnet
  - id: litellm:chat:anthropic-claude-3.5-haiku
  
# Load tests from CSV file
# Each row includes: id, description, attack_prompt, and the appropriate evaluator
tests: file://tests.csv

# Default test settings
defaultTest:
  options: {}
  metadata:
    category: "security"
    test_type: "prompt_leaking"

# Evaluation configuration
evaluateOptions:
  maxConcurrency: 3
  showProgressBar: true

# Output configuration
outputPath: ./results/questions-e-{{date}}.json
writeLatestResults: true

# Enable sharing of results
sharing: true
