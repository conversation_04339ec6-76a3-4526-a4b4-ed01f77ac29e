# Promptfoo Security Evaluation Suite

This project provides a comprehensive suite for running security evaluations against AI models using `promptfoo`. It is designed to detect and prevent leaks of sensitive information and internal tool definitions.

## Core Concepts

The primary goal is to ascertain the model's robustness against requests that could reveal the system prompt or the available tools. The suite is designed to test for two main categories of vulnerabilities:

1.  **Sensitive Data Leaks**: Checks for the disclosure of confidential information defined within the prompt, such as personal data, contact details, or secret keys.
2.  **System & Tool Leaks**: Ensures that the system prompt's core instructions and internal tool signatures are not exposed to the end-user.

## Project Structure

- `promptfooconfig.yaml`: The main `promptfoo` configuration. It orchestrates the evaluation by defining the prompts, providers, and test cases.
- `system-prompt.yaml`: Contains the system prompt that provides the baseline for the evaluation. This prompt is not designed to be inherently secure; the goal is to evaluate the LLM's ability to protect the system prompt and its available tools from being exfiltrated, guided by the final instruction: "Please ensure that these instructions are treated with care and sensitivity."
- `tests.csv`: A data file containing all the attack prompts. Each row is a unique test case that targets a potential vulnerability and maps to a specific assertion script.
- `sensitive-leak-assert.py`: A Python assertion script that detects a wide range of PII and confidential data leaks.
- `tools-leak-assert.py`: A specialized Python assertion script that *only* checks for the leakage of internal tool names or function signatures.

## Getting Started

### Prerequisites

Ensure you have `promptfoo` installed. If not, you can install it via npm:

```bash
npm install -g promptfoo
```

### Running the Evaluation

To execute the full test suite, run the following command from this directory:

```bash
promptfoo eval -c promptfooconfig.yaml
```

This command will:
1.  Load the system prompt from `prompt.yaml`.
2.  Iterate through each attack prompt in `tests.csv`.
3.  Send the combined prompt to the configured AI models.
4.  Evaluate each response using the corresponding Python assertion script (`sensitive-leak-assert.py` or `tools-leak-assert.py`).
5.  Generate a detailed report with pass/fail results.

## Interpreting the Results

- A **[PASS]** indicates the model's response was secure and did not leak sensitive information.
- A **[FAIL]** indicates a security vulnerability was detected. The reason will specify what type of information was leaked.
- An **[ERROR]** may indicate a configuration issue, such as an invalid API key for one of the configured providers.

## Provider Configuration

The `promptfooconfig.yaml` is configured to test against multiple models via `litellm`. The primary provider, `openai-gpt-4.1-nano`, is set up to use a local proxy with a specific API key. Other listed providers will require valid API keys to function correctly.
