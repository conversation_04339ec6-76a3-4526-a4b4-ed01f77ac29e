# SACRA Evaluator - Questions-F: Misinformation & Nuanced Scenarios

## 1. Core Concepts

This evaluation suite (`questions-F`) is designed to test a large language model's (LLM) ability to handle nuanced and sensitive topics that require more than a simple pass/fail judgment. Unlike the `questions-E` suite, which focuses on binary leak detection, `questions-F` assesses the model's performance on complex issues like:

- **Historical Accuracy:** Resisting requests to rewrite or deny well-documented historical events.
- **Propaganda and Misinformation:** Refusing to generate one-sided or biased content.
- **Conspiracy Theories:** Avoiding the promotion of unsubstantiated claims.
- **Ethical Boundaries:** Navigating requests that touch on sensitive topics like colonialism, hate speech, and human rights violations.

## 2. Evaluation Methodology

Due to the subjective nature of these scenarios, this suite uses `promptfoo`'s `llm-rubric` assertion. This approach uses another LLM (the "grader") to evaluate the model's output against a scenario-specific rubric, providing a continuous score from 0.0 to 1.0.

- **Grader Model:** `litellm:chat:openai-gpt-4.1-nano`
- **Rubric:** The `expected_response` field in each test case serves as the rubric, defining the criteria for a successful response (e.g., "The model should refuse to fabricate history or include factual disclaimers...").

This method allows for a more sophisticated analysis of the model's alignment with safety and ethical guidelines.

## 3. Project Structure

- `promptfooconfig.yaml`: The main configuration file. It defines the provider, prompts, and default test settings, including the `llm-rubric` assertion.
- `tests.jsonl`: Contains the test cases in JSONL format. Each line is a JSON object with a `vars` key, which nests the `system_prompt`, `user_prompt`, and `expected_response` for that test.
- `questions-F.json`: The original, legacy source file for the test cases. It is preserved for reference and should not be modified.

## 4. How to Run the Evaluation

To execute the evaluation, run the following command from this directory (`sacra-evaluator/promptfoo-questions/questions-F`):

```bash
promptfoo eval -c promptfooconfig.yaml
```

## 5. Interpreting Results

The output will show a `PASS` or `FAIL` for each test case based on the grader's assessment. It is important to manually review failures, as LLM-based grading can sometimes produce false negatives—incorrectly failing a response that is actually compliant.
