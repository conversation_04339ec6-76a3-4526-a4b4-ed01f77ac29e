# SACRA2 Platform

SACRA2 (Scalable AI Capability & Risk Assessment) is an enterprise-grade microservices platform designed for evaluating AI agents and language models at scale. This platform provides a comprehensive solution for security, alignment, confidentiality, and rule adherence assessment across multiple AI providers.

## Key Features

- **Multi-tenant Architecture**: Complete tenant isolation with project-level granularity
- **Hierarchical Evaluation Framework**: PROBE → PROBESET → CAPABILITY → ASSESSMENT flow
- **20+ LLM Provider Support**: OpenAI, Anthropic, Google, Alibaba, XAI, Groq, and many more
- **Economic Optimization**: Probe result caching to minimize API costs by up to 80%
- **Comprehensive RBAC**: Role-based access control at tenant and project levels
- **Audit Trail**: Complete authentication and evaluation execution tracking
- **Rate Limit Management**: Provider-level concurrency and model-level RPM/TPM controls
- **Horizontal Scalability**: Support for thousands of concurrent evaluations
- **Security Framework**: Hierarchical RBAC with comprehensive audit trails

## Architecture Overview

SACRA2 implements a sophisticated microservices architecture with four main components:

### System Components

1. **Frontend**: React 18 + Vite with TypeScript and Ant Design
2. **Backend**: FastAPI microservices with SQLAlchemy ORM and hierarchical permissions
3. **Background Processing**: Redis-based job queues with Celery for async operations
4. **Infrastructure**: Docker containerization with OAuth2 proxy and Postgres

### Evaluation Framework

1. **PROBE**: Individual tests (prompts, tools, regex patterns)
2. **PROBESET**: Collections of probes with configurable scoring methods
3. **CAPABILITY**: Weighted aggregations of probesets (e.g., prompt-leaking, tool-leaking)
4. **ASSESSMENT**: Top-level evaluations combining multiple capabilities (e.g., SACRA score)

### Supported LLM Providers

The architecture supports extensive provider integration. Default configuration in code enables OpenAI and Anthropic; additional providers (e.g., Google, Azure, Groq, Mistral, Hugging Face, etc.) are supported by design and can be added per workspace requirements.

## Quickstart

### Frontend (React + Vite)

```bash
cd frontend
pnpm install
pnpm dev
# App: http://localhost:5173
```

### Backend (FastAPI)

```bash
cd backend
uv pip install -e .
uv run python main.py dev
# API: http://localhost:8000 (docs at /docs)
```

Environment variables are read from `backend/.env` (see `backend/.env.example`).

## Documentation

### Core Documentation
- **[SACRA2 Model](docs/sacra2-model.md)**: Complete database schema with Mermaid ER diagrams
- **[Execution Optimization](docs/sacra2-execution-optimization.md)**: Economic strategies for probe caching
- **[Comprehensive Architecture](docs/sacra2-comprehensive-architecture.md)**: Full technical architecture covering frontend, backend, background processing, and deployment strategies

## Project Status

**Current Phase**: Documentation and architectural design
- ✅ Complete data model specification
- ✅ Economic optimization strategies
- ✅ Multi-provider configuration design
- ✅ Comprehensive technical architecture
- 🔄 Implementation planning (part of larger SACRA2-Workspace)

NOTE: This repository contains the foundational architecture documentation. Implementation is part of the larger [SACRA2-Workspace](..) microservices project.

## Development Approach

### Documentation-Driven Development
This project follows a documentation-first approach:
1. **Architecture Definition**: Complete technical specifications before implementation
2. **Data Model Design**: Comprehensive database schema with relationships
3. **API Specification**: Detailed endpoint definitions and data flows
4. **Implementation Roadmap**: Phased development with clear milestones

### Implementation Philosophy
- **Documentation First**: Complete architectural definition before code implementation
- **Economic Efficiency**: Optimize for minimal API costs and maximum performance
- **Multi-Tenant Ready**: Design for complete tenant isolation from day one
- **Provider Agnostic**: Support any AI provider through unified interface

## Support

The SACRA2 platform is supported by the [GFT](https://gft.com) team.

## Contributing

Contributions to the SACRA v2 model are welcome. Please submit a pull request with your changes.

## Authors

- [Jordi Murgo <<EMAIL>>](https://github.com/jordi-murgo)

## Contact

For support, please contact [Jordi Murgo](mailto:<EMAIL>).

## License

The SACRA v2 model is licensed under the [MIT License](LICENSE).