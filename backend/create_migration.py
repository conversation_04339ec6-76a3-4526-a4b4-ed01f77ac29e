"""
<PERSON><PERSON><PERSON> to create initial migration for SACRA2 models.
"""

import os
import sys
from pathlib import Path

# Add src to Python path
backend_path = Path(__file__).parent
src_path = backend_path / "src"
sys.path.insert(0, str(src_path))

from alembic.config import Config
from alembic import command

def create_migration():
    """Create initial migration."""
    # Configure Alembic
    alembic_cfg = Config(str(backend_path / "alembic.ini"))
    
    # Create migration
    command.revision(
        alembic_cfg,
        autogenerate=True,
        message="Initial migration - SACRA2 data model"
    )
    print("Migration created successfully!")

if __name__ == "__main__":
    create_migration()