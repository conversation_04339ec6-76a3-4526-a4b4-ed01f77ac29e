# SACRA2 Backend - Plan de Mejora Arquitectónica

## Resumen Ejecutivo

**Estado Actual**: La implementación presenta deuda técnica crítica (8/10) con patrones arquitectónicos empresariales ausentes.

**Objetivo**: Transformar la implementación básica actual en un sistema empresarial que siga los patrones definidos en `docs/sacra2-backend-architecture.md`.

**Duración Estimada**: 5-6 semanas de desarrollo

---

## Fase 1: Fundamentos Arquitectónicos (Semana 1)

### 🔴 CRÍTICO - Unit of Work Pattern

**Archivos a crear/modificar:**
- `src/infrastructure/uow.py` - Implementación principal del UoW
- `src/infrastructure/repositories/base.py` - Integración con UoW
- `src/core/dependencies.py` - Dependency injection para UoW

**Tareas específicas:**
- [ ] Implementar clase `UnitOfWork` con manejo de transacciones SQLAlchemy
- [ ] Crear factory para repositorios dentro del UoW
- [ ] Integrar context manager para transacciones automáticas
- [ ] Actualizar `base.py` repository para trabajar con UoW
- [ ] Crear tests unitarios para UoW

**Ejemplo de implementación requerida:**
```python
class UnitOfWork:
    def __init__(self, session_factory):
        self._session_factory = session_factory
        self._repositories = {}
    
    async def __aenter__(self):
        self._session = self._session_factory()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            await self.rollback()
        else:
            await self.commit()
        await self._session.close()
```

### 🔴 CRÍTICO - Specification Pattern

**Archivos a crear:**
- `src/domain/specifications/base.py` - Specification base class
- `src/domain/specifications/evaluation.py` - Specifications específicas
- `src/domain/specifications/tenant.py` - Tenant-related specifications

**Tareas específicas:**
- [ ] Implementar `Specification` abstract class
- [ ] Crear `CompositeSpecification` para operaciones AND/OR
- [ ] Implementar specifications comunes (TenantSpec, StatusSpec, DateRangeSpec)
- [ ] Integrar specifications con repositories
- [ ] Tests para specification pattern

---

## Fase 2: Capa de Dominio (Semana 2)

### 🔴 CRÍTICO - Domain Aggregates

**Archivos a crear:**
- `src/domain/aggregates/evaluation.py` - EvaluationAggregate
- `src/domain/aggregates/tenant.py` - TenantAggregate  
- `src/domain/aggregates/project.py` - ProjectAggregate
- `src/domain/aggregates/assessment.py` - AssessmentAggregate

**Tareas específicas:**
- [ ] Implementar `AggregateRoot` base class con event sourcing
- [ ] Crear `EvaluationAggregate` con métodos de negocio
- [ ] Implementar state transitions y business rules
- [ ] Agregar event tracking (`_uncommitted_events`)
- [ ] Factory methods para creación de agregados
- [ ] Tests unitarios para cada agregado

### 🟡 ALTA PRIORIDAD - Value Objects

**Archivos a crear:**
- `src/domain/value_objects/email.py` - Email validation
- `src/domain/value_objects/score.py` - Score con constraints
- `src/domain/value_objects/model_config.py` - ModelConfiguration
- `src/domain/value_objects/evaluation_status.py` - Status enum

**Tareas específicas:**
- [ ] Implementar Value Objects inmutables con `@dataclass(frozen=True)`
- [ ] Agregar validación en `__post_init__`
- [ ] Crear métodos de conversión y formateo
- [ ] Tests de validación para cada VO

### 🔴 CRÍTICO - Domain Events

**Archivos a crear:**
- `src/domain/events/base.py` - DomainEvent base class
- `src/domain/events/evaluation.py` - Evaluation-related events
- `src/domain/events/handlers.py` - Event handlers

**Tareas específicas:**
- [ ] Implementar `DomainEvent` base class
- [ ] Crear eventos específicos (EvaluationCreated, EvaluationStarted, etc.)
- [ ] Sistema de event handlers
- [ ] Integración con message bus
- [ ] Event versioning para backward compatibility

---

## Fase 3: Capa de Aplicación (Semana 3)

### 🔴 CRÍTICO - Application Services

**Archivos a crear:**
- `src/application/services/evaluation_service.py` - Orquestación de evaluaciones
- `src/application/services/tenant_service.py` - Gestión de tenants
- `src/application/services/scoring_service.py` - Cálculo de scores

**Tareas específicas:**
- [ ] Implementar `BaseService` con funcionalidad común
- [ ] Crear `EvaluationService` con métodos de orchestration
- [ ] Agregar manejo de transacciones via UoW
- [ ] Implementar business logic validation
- [ ] Integration con domain services
- [ ] Error handling y logging

### 🟡 ALTA PRIORIDAD - CQRS Implementation

**Archivos a crear:**
- `src/application/commands/` - Command handlers
- `src/application/queries/` - Query handlers
- `src/application/dto/` - Data transfer objects

**Tareas específicas:**
- [ ] Separar commands de queries
- [ ] Implementar command/query buses
- [ ] Crear DTOs para requests/responses
- [ ] Command validation
- [ ] Query optimization patterns

---

## Fase 4: Patrones Avanzados de Python (Semana 4)

### 🟡 ALTA PRIORIDAD - Decorators y Async Patterns

**Archivos a modificar/crear:**
- `src/core/decorators.py` - Decorators avanzados
- `src/core/async_patterns.py` - Async utilities
- `src/core/circuit_breaker.py` - Circuit breaker implementation

**Tareas específicas:**
- [ ] Decorator `@traced` para distributed tracing
- [ ] Decorator `@retry` con exponential backoff
- [ ] Decorator `@cached` para Redis caching
- [ ] `AsyncBatcher` para batch operations
- [ ] `AsyncConnectionPool` con health checks
- [ ] Circuit breaker pattern

**Ejemplo requerido:**
```python
@traced("evaluation_service")
@retry(max_attempts=3, backoff_factor=2.0)
@cached(ttl=3600)
async def get_evaluation_results(evaluation_id: str):
    # Implementation
    pass
```

### 🟡 ALTA PRIORIDAD - Event-Driven Architecture

**Archivos a crear:**
- `src/infrastructure/messaging/event_bus.py` - Event bus implementation
- `src/infrastructure/messaging/handlers.py` - Message handlers
- `src/workers/consumers/domain_events.py` - Event consumers

**Tareas específicas:**
- [ ] Implementar async event bus
- [ ] Integración con Redis/RabbitMQ
- [ ] Event serialization/deserialization
- [ ] Dead letter queue handling
- [ ] Event replay capabilities

---

## Fase 5: Refactoring de API (Semana 5)

### 🔴 CRÍTICO - API Layer Refactoring

**Archivos a modificar:**
- `src/api/v1/endpoints/` - Todos los endpoints existentes
- `src/api/dependencies.py` - Dependency injection
- `src/api/middleware/` - Custom middleware

**Tareas específicas:**
- [ ] Mover business logic de controllers a services
- [ ] Implementar proper dependency injection
- [ ] Agregar validation middleware
- [ ] Rate limiting implementation
- [ ] API versioning support
- [ ] OpenAPI schema enhancement

### 🟡 ALTA PRIORIDAD - Advanced API Features

**Archivos a crear:**
- `src/api/v1/graphql/` - GraphQL integration
- `src/api/v1/websockets/` - Real-time features
- `src/api/middleware/tracing.py` - Request tracing

**Tareas específicas:**
- [ ] GraphQL schema con Strawberry
- [ ] WebSocket connection management
- [ ] Real-time evaluation updates
- [ ] API metrics collection
- [ ] Request correlation IDs

---

## Fase 6: Testing y Quality Assurance (Semana 6)

### 🔴 CRÍTICO - Test Coverage

**Estructura de tests a completar:**
```
tests/
├── unit/
│   ├── domain/           # Domain logic tests
│   ├── application/      # Service tests  
│   └── infrastructure/   # Repository tests
├── integration/          # Cross-layer tests
├── e2e/                 # Full system tests
└── performance/         # Load tests
```

**Tareas específicas:**
- [ ] Unit tests para domain aggregates (>95% coverage)
- [ ] Integration tests para repositories
- [ ] Service layer testing con mocks
- [ ] End-to-end API tests
- [ ] Performance testing con locust
- [ ] Security testing (OWASP)

### 🟡 ALTA PRIORIDAD - Code Quality

**Herramientas a configurar:**
- [ ] Pre-commit hooks con black, isort, flake8
- [ ] Type checking con mypy
- [ ] Security scanning con bandit
- [ ] Dependency checking con safety
- [ ] Code complexity analysis

---

## Métricas de Progreso

### Definition of Done por Fase:

**Fase 1**: 
- [ ] UoW implementado y funcionando
- [ ] Specifications pattern funcional
- [ ] Tests unitarios >90%

**Fase 2**:
- [ ] Agregados implementados con business rules
- [ ] Value objects con validación
- [ ] Domain events funcionando

**Fase 3**:
- [ ] Application services orquestando business logic
- [ ] CQRS separando commands/queries
- [ ] DTOs definidos

**Fase 4**:
- [ ] Decorators avanzados implementados
- [ ] Event-driven architecture funcional
- [ ] Async patterns optimizados

**Fase 5**:
- [ ] API endpoints refactorizados
- [ ] Business logic movida a services
- [ ] Advanced features implementadas

**Fase 6**:
- [ ] Test coverage >95%
- [ ] Performance benchmarks establecidos
- [ ] Code quality metrics en verde

### Riesgos y Mitigaciones:

🚨 **Riesgo Alto**: Breaking changes durante refactoring
- **Mitigación**: Feature flags, gradual migration, comprehensive testing

🚨 **Riesgo Medio**: Performance degradation con nuevos patterns
- **Mitigación**: Performance testing, profiling, optimization

🚨 **Riesgo Bajo**: Team learning curve para DDD patterns
- **Mitigación**: Code reviews, documentation, pair programming

---

## Recursos Adicionales

- **Documentación**: `docs/sacra2-backend-architecture.md`
- **Patrones de referencia**: Domain-Driven Design (Eric Evans)
- **Ejemplos de implementación**: Ver architecture doc para code samples
- **Testing strategy**: Pyramid testing approach

## Contacto

Para dudas sobre implementación, revisar architecture document o consultar con el tech lead.