"""
Unit of Work (UoW) implementation for coordinating transactional work across repositories.

This module provides an asynchronous UnitOfWork that:
- Manages an AsyncSession lifetime (begin/commit/rollback/close)
- Exposes a RepositoryFactory to obtain repositories bound to the session
- Implements an async context manager for safe transactional boundaries

Usage:

```python
from src.infrastructure.uow import UnitOfWork
from src.infrastructure.database import database_manager

uow = UnitOfWork(session_factory=database_manager.session_factory)

async with uow as tx:
    repo = tx.repositories.get_repository(MyModel)
    await repo.create(entity)
```
"""
from __future__ import annotations

from typing import Optional, Callable, Awaitable

from sqlalchemy.ext.asyncio import AsyncSession

from .repositories.base import RepositoryFactory


class UnitOfWork:
    """Coordinates a transaction using a shared AsyncSession.

    Responsibilities:
    - Lazily create an AsyncSession using the provided session_factory
    - Start a transaction on enter and commit on successful exit
    - Roll back on exceptions and always close the session
    - Provide a `RepositoryFactory` bound to the active session
    """

    def __init__(self, session_factory: Optional[Callable[[], AsyncSession]] = None) -> None:
        """Create a new UnitOfWork.

        Args:
            session_factory: A callable that returns a new AsyncSession when invoked.
        """
        self._session_factory = session_factory
        self._session: Optional[AsyncSession] = None
        self.repositories: Optional[RepositoryFactory] = None
        self._transaction_ctx = None

    @property
    def session(self) -> AsyncSession:
        """Get the active AsyncSession.

        Raises:
            RuntimeError: If accessed outside of an active context.
        """
        if self._session is None:
            raise RuntimeError("UnitOfWork session is not initialized. Use 'async with UnitOfWork(...):' context.")
        return self._session

    async def __aenter__(self) -> "UnitOfWork":
        if self._session_factory is None:
            # Delayed import to avoid circular dependencies for app initialization paths
            from .database import database_manager  # pylint: disable=import-outside-toplevel

            if database_manager.session_factory is None:
                # Ensure database is initialized so session_factory exists
                await database_manager.initialize()
            self._session_factory = database_manager.session_factory

        # Create session and begin transaction
        self._session = self._session_factory()  # type: ignore[misc]
        self._transaction_ctx = self._session.begin()
        await self._transaction_ctx.__aenter__()

        # Bind repositories
        self.repositories = RepositoryFactory(self._session)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        try:
            if self._transaction_ctx is not None:
                await self._transaction_ctx.__aexit__(exc_type, exc_val, exc_tb)
        finally:
            if self._session is not None:
                await self._session.close()

        self._session = None
        self._transaction_ctx = None
        self.repositories = None

    async def commit(self) -> None:
        """Explicitly commit the active transaction."""
        await self.session.commit()

    async def rollback(self) -> None:
        """Explicitly roll back the active transaction."""
        await self.session.rollback()
