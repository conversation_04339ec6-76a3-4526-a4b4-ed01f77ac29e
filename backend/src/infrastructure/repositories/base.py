"""
Base repository implementation for SACRA2.
Provides generic CRUD operations and common database functionality.

Extended with optional Specification pattern helpers to compose complex filters
without breaking the existing API surface.
"""

import uuid
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
from datetime import datetime
from sqlalchemy import select, delete, update, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError, SQLAlchemyError

# Import from core and domain
from ...core.types import PaginatedResult, PaginationParams, SortParam, SortDirection
from ...core.exceptions import EntityNotFound, DatabaseError, ValidationError
from ...domain.models.base import BaseEntity
from ...domain.specifications.base import Specification


T = TypeVar('T', bound=BaseEntity)


class BaseRepository(Generic[T]):
    """
    Generic repository for CRUD operations on domain entities.
    
    Provides common database operations with async support, pagination,
    filtering, and transaction management.
    """
    
    def __init__(self, model: Type[T], session: AsyncSession):
        """
        Initialize repository.
        
        Args:
            model: The SQLAlchemy model class
            session: Async database session
        """
        self.model = model
        self.session = session
    
    async def create(self, entity: T) -> T:
        """
        Create a new entity.
        
        Args:
            entity: Entity to create
            
        Returns:
            Created entity with generated ID
            
        Raises:
            ValidationError: If entity validation fails
            DatabaseError: If database operation fails
        """
        try:
            self.session.add(entity)
            await self.session.flush()
            await self.session.refresh(entity)
            return entity
        except IntegrityError as e:
            await self.session.rollback()
            raise ValidationError(
                field="id",
                value=str(entity.id),
                message=f"Entity creation failed: {str(e)}"
            ) from e
        except SQLAlchemyError as e:
            await self.session.rollback()
            raise DatabaseError(
                operation="create",
                entity_type=self.model.__name__,
                details=str(e)
            ) from e
    
    async def get_by_id(
        self, 
        entity_id: uuid.UUID, 
        include_deleted: bool = False
    ) -> Optional[T]:
        """
        Get entity by ID.
        
        Args:
            entity_id: Entity ID
            include_deleted: Whether to include soft-deleted entities
            
        Returns:
            Entity if found, None otherwise
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            query = select(self.model).where(self.model.id == entity_id)
            
            if not include_deleted:
                query = query.where(self.model.deleted_at.is_(None))
            
            result = await self.session.execute(query)
            return result.scalar_one_or_none()
        except SQLAlchemyError as e:
            raise DatabaseError(
                operation="get_by_id",
                entity_type=self.model.__name__,
                details=str(e)
            ) from e
    
    async def get_by_id_or_raise(
        self, 
        entity_id: uuid.UUID, 
        include_deleted: bool = False
    ) -> T:
        """
        Get entity by ID or raise EntityNotFound.
        
        Args:
            entity_id: Entity ID
            include_deleted: Whether to include soft-deleted entities
            
        Returns:
            Entity
            
        Raises:
            EntityNotFound: If entity not found
            DatabaseError: If database operation fails
        """
        entity = await self.get_by_id(entity_id, include_deleted)
        if not entity:
            raise EntityNotFound(
                entity_type=self.model.__name__,
                entity_id=str(entity_id)
            )
        return entity
    
    async def list(
        self,
        filters: Optional[Dict[str, Any]] = None,
        pagination: Optional[PaginationParams] = None,
        sort: Optional[SortParam] = None,
        include_deleted: bool = False
    ) -> PaginatedResult[T]:
        """
        List entities with filtering, sorting, and pagination.
        
        Args:
            filters: Dictionary of field filters
            pagination: Pagination parameters
            sort: Sort parameters
            include_deleted: Whether to include soft-deleted entities
            
        Returns:
            Paginated result with entities
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            query = select(self.model)
            
            # Apply soft delete filter
            if not include_deleted:
                query = query.where(self.model.deleted_at.is_(None))
            
            # Apply filters
            if filters:
                query = self._apply_filters(query, filters)
            
            # Apply sorting
            if sort:
                query = self._apply_sorting(query, sort)
            
            # Get total count
            count_query = select(func.count()).select_from(query.subquery())
            total_result = await self.session.execute(count_query)
            total = total_result.scalar()
            
            # Apply pagination
            if pagination:
                query = query.offset(pagination.offset).limit(pagination.per_page)
            
            # Execute query
            result = await self.session.execute(query)
            items = result.scalars().all()
            
            # Build paginated result
            params = pagination or PaginationParams(page=1, per_page=len(items) or 1)
            return PaginatedResult.create(items=list(items), total=total, params=params)
        except SQLAlchemyError as e:
            raise DatabaseError(
                operation="list",
                entity_type=self.model.__name__,
                details=str(e)
            ) from e

    async def list_by_spec(
        self,
        spec: Specification,
        pagination: Optional[PaginationParams] = None,
        sort: Optional[SortParam] = None,
        include_deleted: bool = False,
    ) -> PaginatedResult[T]:
        """List entities matching a specification with optional pagination and sorting."""
        try:
            query = select(self.model)

            if not include_deleted:
                query = query.where(self.model.deleted_at.is_(None))

            query = self._apply_spec(query, spec)

            # Get total count
            count_query = select(func.count()).select_from(query.subquery())
            total_result = await self.session.execute(count_query)
            total = total_result.scalar()

            # Apply sorting
            if sort:
                query = self._apply_sorting(query, sort)

            # Pagination
            if pagination:
                query = query.offset(pagination.offset).limit(pagination.per_page)

            result = await self.session.execute(query)
            items = result.scalars().all()

            params = pagination or PaginationParams(page=1, per_page=len(items) or 1)
            return PaginatedResult.create(items=list(items), total=total, params=params)
        except SQLAlchemyError as e:
            raise DatabaseError(
                operation="list_by_spec",
                entity_type=self.model.__name__,
                details=str(e),
            ) from e
    
    async def update(
        self, 
        entity_id: uuid.UUID, 
        update_data: Dict[str, Any],
        user_id: Optional[str] = None
    ) -> T:
        """
        Update entity.
        
        Args:
            entity_id: Entity ID
            update_data: Dictionary of fields to update
            user_id: ID of user making the update
            
        Returns:
            Updated entity
            
        Raises:
            EntityNotFound: If entity not found
            ValidationError: If update validation fails
            DatabaseError: If database operation fails
        """
        try:
            # Get entity
            entity = await self.get_by_id_or_raise(entity_id)
            
            # Check if entity is soft deleted
            if entity.is_deleted:
                raise EntityNotFound(
                    entity_type=self.model.__name__,
                    entity_id=str(entity_id)
                )
            
            # Update fields
            for field, value in update_data.items():
                if hasattr(entity, field):
                    setattr(entity, field, value)
                else:
                    raise ValidationError(
                        field=field,
                        value=str(value),
                        message=f"Invalid field: {field}"
                    )
            
            # Update audit fields
            entity.update_audit_fields(user_id)
            entity.increment_version()
            
            # Flush changes
            await self.session.flush()
            await self.session.refresh(entity)
            
            return entity
        except IntegrityError as e:
            await self.session.rollback()
            raise ValidationError(
                field="update",
                value=str(entity_id),
                message=f"Entity update failed: {str(e)}"
            ) from e
        except SQLAlchemyError as e:
            await self.session.rollback()
            raise DatabaseError(
                operation="update",
                entity_type=self.model.__name__,
                details=str(e)
            ) from e
    
    async def delete(
        self, 
        entity_id: uuid.UUID, 
        soft_delete: bool = True,
        user_id: Optional[str] = None
    ) -> bool:
        """
        Delete entity.
        
        Args:
            entity_id: Entity ID
            soft_delete: Whether to soft delete or hard delete
            user_id: ID of user making the deletion
            
        Returns:
            True if deleted, False if not found
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            entity = await self.get_by_id(entity_id)
            if not entity:
                return False
            
            if soft_delete:
                entity.soft_delete(user_id)
                await self.session.flush()
            else:
                query = delete(self.model).where(self.model.id == entity_id)
                result = await self.session.execute(query)
                deleted_count = result.rowcount
                
                if deleted_count == 0:
                    return False
            
            return True
        except SQLAlchemyError as e:
            await self.session.rollback()
            raise DatabaseError(
                operation="delete",
                entity_type=self.model.__name__,
                details=str(e)
            ) from e
    
    async def bulk_create(self, entities: List[T]) -> List[T]:
        """
        Create multiple entities in bulk.
        
        Args:
            entities: List of entities to create
            
        Returns:
            List of created entities
            
        Raises:
            ValidationError: If validation fails
            DatabaseError: If database operation fails
        """
        try:
            self.session.add_all(entities)
            await self.session.flush()
            
            for entity in entities:
                await self.session.refresh(entity)
            
            return entities
        except IntegrityError as e:
            await self.session.rollback()
            raise ValidationError(
                field="bulk_create",
                value=str(len(entities)),
                message=f"Bulk creation failed: {str(e)}"
            ) from e
        except SQLAlchemyError as e:
            await self.session.rollback()
            raise DatabaseError(
                operation="bulk_create",
                entity_type=self.model.__name__,
                details=str(e)
            ) from e
    
    async def bulk_update(
        self, 
        updates: List[Dict[str, Any]],
        user_id: Optional[str] = None
    ) -> List[T]:
        """
        Update multiple entities in bulk.
        
        Args:
            updates: List of dictionaries with 'id' and update fields
            user_id: ID of user making the updates
            
        Returns:
            List of updated entities
            
        Raises:
            ValidationError: If validation fails
            DatabaseError: If database operation fails
        """
        try:
            updated_entities = []
            
            for update_data in updates:
                entity_id = update_data.pop('id')
                entity = await self.update(entity_id, update_data, user_id)
                updated_entities.append(entity)
            
            return updated_entities
        except SQLAlchemyError as e:
            await self.session.rollback()
            raise DatabaseError(
                operation="bulk_update",
                entity_type=self.model.__name__,
                details=str(e)
            ) from e
    
    async def count(
        self, 
        filters: Optional[Dict[str, Any]] = None,
        include_deleted: bool = False
    ) -> int:
        """
        Count entities matching criteria.
        
        Args:
            filters: Dictionary of field filters
            include_deleted: Whether to include soft-deleted entities
            
        Returns:
            Count of matching entities
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            query = select(func.count(self.model.id))
            
            if not include_deleted:
                query = query.where(self.model.deleted_at.is_(None))
            
            if filters:
                query = self._apply_filters(query, filters)
            
            result = await self.session.execute(query)
            return result.scalar()
        except SQLAlchemyError as e:
            raise DatabaseError(
                operation="count",
                entity_type=self.model.__name__,
                details=str(e)
            ) from e

    async def count_by_spec(
        self,
        spec: Specification,
        include_deleted: bool = False,
    ) -> int:
        """Count entities matching a specification."""
        try:
            query = select(func.count(self.model.id))

            if not include_deleted:
                query = query.where(self.model.deleted_at.is_(None))

            query = self._apply_spec(query, spec)

            result = await self.session.execute(query)
            return result.scalar()
        except SQLAlchemyError as e:
            raise DatabaseError(
                operation="count_by_spec",
                entity_type=self.model.__name__,
                details=str(e),
            ) from e
    
    async def exists(
        self, 
        entity_id: uuid.UUID, 
        include_deleted: bool = False
    ) -> bool:
        """
        Check if entity exists.
        
        Args:
            entity_id: Entity ID
            include_deleted: Whether to include soft-deleted entities
            
        Returns:
            True if entity exists
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            query = select(func.count(self.model.id)).where(self.model.id == entity_id)
            
            if not include_deleted:
                query = query.where(self.model.deleted_at.is_(None))
            
            result = await self.session.execute(query)
            return result.scalar() > 0
        except SQLAlchemyError as e:
            raise DatabaseError(
                operation="exists",
                entity_type=self.model.__name__,
                details=str(e)
            ) from e

    async def exists_by_spec(
        self,
        spec: Specification,
        include_deleted: bool = False,
    ) -> bool:
        """Check existence of any entity matching a specification."""
        try:
            query = select(func.count(self.model.id))

            if not include_deleted:
                query = query.where(self.model.deleted_at.is_(None))

            query = self._apply_spec(query, spec)

            result = await self.session.execute(query)
            return result.scalar() > 0
        except SQLAlchemyError as e:
            raise DatabaseError(
                operation="exists_by_spec",
                entity_type=self.model.__name__,
                details=str(e),
            ) from e
    
    def _apply_filters(self, query, filters: Dict[str, Any]):
        """Apply filters to query"""
        for field, value in filters.items():
            if hasattr(self.model, field):
                column = getattr(self.model, field)
                
                if isinstance(value, dict):
                    # Handle complex filters
                    if 'eq' in value:
                        query = query.where(column == value['eq'])
                    elif 'ne' in value:
                        query = query.where(column != value['ne'])
                    elif 'gt' in value:
                        query = query.where(column > value['gt'])
                    elif 'gte' in value:
                        query = query.where(column >= value['gte'])
                    elif 'lt' in value:
                        query = query.where(column < value['lt'])
                    elif 'lte' in value:
                        query = query.where(column <= value['lte'])
                    elif 'in' in value:
                        query = query.where(column.in_(value['in']))
                    elif 'like' in value:
                        query = query.where(column.like(f"%{value['like']}%"))
                    elif 'ilike' in value:
                        query = query.where(column.ilike(f"%{value['ilike']}%"))
                else:
                    # Simple equality
                    query = query.where(column == value)
        
        return query
    
    def _apply_sorting(self, query, sort: SortParam):
        """Apply sorting to query"""
        if hasattr(self.model, sort.field):
            column = getattr(self.model, sort.field)
            
            if sort.direction == SortDirection.DESC:
                query = query.order_by(column.desc())
            else:
                query = query.order_by(column.asc())
        
        return query
    
    def _apply_spec(self, query, spec: Specification):
        """Apply a Specification to the query."""
        return query.where(spec.to_expression(self.model))
    
    async def get_session(self) -> AsyncSession:
        """Get the current database session"""
        return self.session
    
    async def refresh(self, entity: T) -> T:
        """Refresh entity from database"""
        await self.session.refresh(entity)
        return entity
    
    async def flush(self) -> None:
        """Flush pending changes"""
        await self.session.flush()


class RepositoryFactory:
    """Factory for creating repository instances"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self._repositories: Dict[str, BaseRepository] = {}
    
    def get_repository(self, model_class: Type[T]) -> BaseRepository[T]:
        """Get repository for model class"""
        model_name = model_class.__name__
        
        if model_name not in self._repositories:
            self._repositories[model_name] = BaseRepository(model_class, self.session)
        
        return self._repositories[model_name]
    
    def clear(self):
        """Clear repository cache"""
        self._repositories.clear()