"""
Driver models for SACRA2 system.
Models for driver and provider management with rate limiting capabilities.
"""

import uuid
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, String, Integer, Boolean, JSON, ForeignKey, UniqueConstraint, CheckConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ....domain.models.base import Base


class Driver(Base):
    """Driver entity for LLM provider implementations."""
    
    __tablename__ = "drivers"
    
    # Core fields
    name: Mapped[str] = mapped_column(String(100), nullable=False, unique=True, index=True)
    type: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # Configuration
    default_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    
    # Status
    active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Relationships
    providers: Mapped[List["Provider"]] = relationship("Provider", back_populates="driver", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        CheckConstraint("type IN ('litellm', 'openai', 'openrouter', 'vertex', 'bedrock', 'azure', 'anthropic', 'groq', 'huggingface')", name='driver_type_check'),
        CheckConstraint("length(name) >= 1", name='driver_name_not_empty'),
        CheckConstraint("length(type) >= 1", name='driver_type_not_empty'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'name': self.name,
            'type': self.type,
            'description': self.description,
            'default_config': self.default_config,
            'active': self.active,
            'provider_count': len(self.providers) if self.providers else 0
        })
        return base_dict
    
    def __repr__(self) -> str:
        return f"<Driver(id={self.id}, name={self.name}, type={self.type})>"


class Provider(Base):
    """Provider entity for LLM service providers."""
    
    __tablename__ = "providers"
    
    # Core fields
    name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    api_key: Mapped[Optional[str]] = mapped_column(String(1000), nullable=True)  # Encrypted in production
    
    # Rate limiting
    max_concurrent_requests: Mapped[int] = mapped_column(Integer, default=10, nullable=False)
    
    # Foreign keys
    driver_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("drivers.id"), nullable=False, index=True)
    
    # Relationships
    driver: Mapped["Driver"] = relationship("Driver", back_populates="providers")
    models: Mapped[List["LLMModel"]] = relationship("LLMModel", back_populates="provider", cascade="all, delete-orphan")
    tenant_configs: Mapped[List["TenantProviderConfig"]] = relationship("TenantProviderConfig", back_populates="provider", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('driver_id', 'name', name='provider_driver_name_unique'),
        CheckConstraint("max_concurrent_requests > 0", name='provider_max_concurrent_positive'),
        CheckConstraint("length(name) >= 1", name='provider_name_not_empty'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'name': self.name,
            'url': self.url,
            'api_key': '***' if self.api_key else None,  # Mask API key
            'max_concurrent_requests': self.max_concurrent_requests,
            'driver_id': str(self.driver_id),
            'driver_name': self.driver.name if self.driver else None,
            'model_count': len(self.models) if self.models else 0
        })
        return base_dict
    
    def __repr__(self) -> str:
        return f"<Provider(id={self.id}, name={self.name}, driver={self.driver.name if self.driver else 'None'})>"


class TenantProviderConfig(Base):
    """Tenant-specific provider configuration."""
    
    __tablename__ = "tenant_provider_configs"
    
    # Foreign keys
    tenant_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    provider_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("providers.id"), nullable=False, index=True)
    
    # Configuration
    api_key: Mapped[str] = mapped_column(String(1000), nullable=False)  # Tenant-specific API key
    max_concurrent_requests: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # Override provider default
    enabled: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Configuration options
    config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    
    # Relationships
    tenant: Mapped["Tenant"] = relationship("Tenant", back_populates="provider_configs")  # Forward reference
    provider: Mapped["Provider"] = relationship("Provider", back_populates="tenant_configs")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('tenant_id', 'provider_id', name='tenant_provider_unique'),
        CheckConstraint("max_concurrent_requests > 0 OR max_concurrent_requests IS NULL", name='tenant_max_concurrent_positive'),
        CheckConstraint("length(api_key) >= 1", name='tenant_api_key_not_empty'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'tenant_id': str(self.tenant_id),
            'provider_id': str(self.provider_id),
            'provider_name': self.provider.name if self.provider else None,
            'api_key': '***',  # Always mask API key
            'max_concurrent_requests': self.max_concurrent_requests,
            'enabled': self.enabled,
            'config': self.config
        })
        return base_dict
    
    def __repr__(self) -> str:
        return f"<TenantProviderConfig(tenant_id={self.tenant_id}, provider={self.provider.name if self.provider else 'None'})>"


class LLMModel(Base):
    """LLM Model entity with rate limiting."""
    
    __tablename__ = "llm_models"
    
    # Core fields
    name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    model_version: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    description: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # Rate limits
    requests_per_minute: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    tokens_per_minute: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Model capabilities
    max_tokens: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    supports_functions: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    supports_vision: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # Foreign keys
    provider_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("providers.id"), nullable=False, index=True)
    
    # Status
    active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Relationships
    provider: Mapped["Provider"] = relationship("Provider", back_populates="models")
    default_params: Mapped[List["ModelDefaultParam"]] = relationship("ModelDefaultParam", back_populates="model", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('provider_id', 'name', 'model_version', name='model_provider_name_version_unique'),
        CheckConstraint("requests_per_minute > 0 OR requests_per_minute IS NULL", name='model_rpm_positive'),
        CheckConstraint("tokens_per_minute > 0 OR tokens_per_minute IS NULL", name='model_tpm_positive'),
        CheckConstraint("max_tokens > 0 OR max_tokens IS NULL", name='model_max_tokens_positive'),
        CheckConstraint("length(name) >= 1", name='model_name_not_empty'),
    )
    
    @property
    def full_name(self) -> str:
        """Get full model name with version."""
        if self.model_version:
            return f"{self.name}:{self.model_version}"
        return self.name
    
    @property
    def version(self) -> Optional[str]:
        """Compatibility alias for model_version."""
        return self.model_version
    
    @version.setter
    def version(self, value: Optional[str]) -> None:
        """Compatibility alias for model_version setter."""
        self.model_version = value
    
    def get_default_param(self, key: str) -> Optional[str]:
        """Get default parameter value."""
        for param in self.default_params:
            if param.key == key:
                return param.value
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'name': self.name,
            'version': self.model_version,
            'full_name': self.full_name,
            'description': self.description,
            'requests_per_minute': self.requests_per_minute,
            'tokens_per_minute': self.tokens_per_minute,
            'max_tokens': self.max_tokens,
            'supports_functions': self.supports_functions,
            'supports_vision': self.supports_vision,
            'provider_id': str(self.provider_id),
            'provider_name': self.provider.name if self.provider else None,
            'active': self.active,
            'default_params': {param.key: param.value for param in self.default_params} if self.default_params else {}
        })
        return base_dict
    
    def __repr__(self) -> str:
        return f"<LLMModel(id={self.id}, name={self.full_name}, provider={self.provider.name if self.provider else 'None'})>"


class ModelDefaultParam(Base):
    """Default parameters for LLM models."""
    
    __tablename__ = "model_default_params"
    
    # Core fields
    key: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    value: Mapped[str] = mapped_column(String(1000), nullable=False)
    param_type: Mapped[str] = mapped_column(String(20), default='string', nullable=False)
    description: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # Foreign keys
    model_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("llm_models.id"), nullable=False, index=True)
    
    # Relationships
    model: Mapped["LLMModel"] = relationship("LLMModel", back_populates="default_params")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('model_id', 'key', name='model_param_key_unique'),
        CheckConstraint("param_type IN ('string', 'integer', 'float', 'boolean', 'json')", name='param_type_check'),
        CheckConstraint("length(key) >= 1", name='param_key_not_empty'),
        CheckConstraint("length(value) >= 1", name='param_value_not_empty'),
    )
    
    @property
    def typed_value(self) -> Any:
        """Get value with correct type conversion."""
        if self.param_type == 'integer':
            return int(self.value)
        elif self.param_type == 'float':
            return float(self.value)
        elif self.param_type == 'boolean':
            return self.value.lower() in ('true', '1', 'yes', 'on')
        elif self.param_type == 'json':
            import json
            return json.loads(self.value)
        return self.value
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'key': self.key,
            'value': self.value,
            'typed_value': self.typed_value,
            'param_type': self.param_type,
            'description': self.description,
            'model_id': str(self.model_id),
            'model_name': self.model.name if self.model else None
        })
        return base_dict
    
    def __repr__(self) -> str:
        return f"<ModelDefaultParam(model={self.model.name if self.model else 'None'}, key={self.key}, value={self.value})>"