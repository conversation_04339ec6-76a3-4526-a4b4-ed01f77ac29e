"""
SQLAlchemy models for SACRA2 system.
Implements the complete data model with multi-tenant architecture and evaluation framework.
"""

# Import base models
from ....domain.models.base import Base, BaseEntity

# Import all model classes
from .drivers import (
    Driver,
    Provider,
    TenantProviderConfig,
    LLMModel,
    ModelDefaultParam
)

from .organization import (
    Tenant,
    Project
)

from .evaluation import (
    Probe,
    Probeset,
    ProbesetProbe,
    Capability,
    CapabilitySet,
    Assessment,
    AssessmentCapability
)

from .execution import (
    EvaluationRun,
    RunModel,
    RunProbeResult,
    RunProbesetResult,
    RunCapabilityScore,
    RunAssessmentScore
)

# Export all models
__all__ = [
    # Base
    'Base',
    'BaseEntity',
    
    # Drivers and Models
    'Driver',
    'Provider',
    'TenantProviderConfig',
    'LLMModel',
    'ModelDefaultParam',
    
    # Organization
    'Tenant',
    'Project',
    
    # Evaluation Framework
    'Probe',
    'Probeset',
    'ProbesetProbe',
    'Capability',
    'CapabilitySet',
    'Assessment',
    'AssessmentCapability',
    
    # Execution and Results
    'EvaluationRun',
    'RunModel',
    'RunProbeResult',
    'RunProbesetResult',
    'RunCapabilityScore',
    'RunAssessmentScore',
]

# Model categories for easier access
DRIVER_MODELS = [Driver, Provider, TenantProviderConfig, LLMModel, ModelDefaultParam]
ORGANIZATION_MODELS = [Tenant, Project]
EVALUATION_MODELS = [Probe, Probeset, ProbesetProbe, Capability, CapabilitySet, Assessment, AssessmentCapability]
EXECUTION_MODELS = [EvaluationRun, RunModel, RunProbeResult, RunProbesetResult, RunCapabilityScore, RunAssessmentScore]

# All models for migration and metadata purposes
ALL_MODELS = DRIVER_MODELS + ORGANIZATION_MODELS + EVALUATION_MODELS + EXECUTION_MODELS

# Model registry for dynamic access
MODEL_REGISTRY = {
    # Driver models
    'driver': Driver,
    'provider': Provider,
    'tenant_provider_config': TenantProviderConfig,
    'llm_model': LLMModel,
    'model_default_param': ModelDefaultParam,
    
    # Organization models
    'tenant': Tenant,
    'project': Project,
    
    # Evaluation models
    'probe': Probe,
    'probeset': Probeset,
    'probeset_probe': ProbesetProbe,
    'capability': Capability,
    'capability_set': CapabilitySet,
    'assessment': Assessment,
    'assessment_capability': AssessmentCapability,
    
    # Execution models
    'evaluation_run': EvaluationRun,
    'run_model': RunModel,
    'run_probe_result': RunProbeResult,
    'run_probeset_result': RunProbesetResult,
    'run_capability_score': RunCapabilityScore,
    'run_assessment_score': RunAssessmentScore,
}

def get_model_by_name(name: str):
    """Get model class by name."""
    return MODEL_REGISTRY.get(name.lower())

def get_all_table_names():
    """Get all table names."""
    return [model.__tablename__ for model in ALL_MODELS]

def get_models_by_category(category: str):
    """Get models by category."""
    categories = {
        'driver': DRIVER_MODELS,
        'drivers': DRIVER_MODELS,
        'organization': ORGANIZATION_MODELS,
        'evaluation': EVALUATION_MODELS,
        'execution': EXECUTION_MODELS,
        'all': ALL_MODELS,
    }
    return categories.get(category.lower(), [])