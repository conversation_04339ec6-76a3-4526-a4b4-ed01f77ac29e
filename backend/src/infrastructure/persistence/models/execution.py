"""
Execution and results models for SACRA2 system.
Models for evaluation runs and optimized result caching.
"""

import uuid
from typing import Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy import Column, String, Integer, Boolean, JSON, ForeignKey, UniqueConstraint, CheckConstraint, Text, Float, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy import func

from ....domain.models.base import Base


class EvaluationRun(Base):
    """Evaluation run entity - specific launch of an assessment."""
    
    __tablename__ = "evaluation_runs"
    
    # Core fields
    name: Mapped[str] = mapped_column(String(200), nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Status tracking
    status: Mapped[str] = mapped_column(String(20), nullable=False, default='queued', index=True)
    
    # Timing
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Error handling
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    retry_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # Configuration
    config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    
    # Progress tracking
    total_probes: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    completed_probes: Mapped[Optional[int]] = mapped_column(Integer, default=0, nullable=True)
    
    # Foreign keys
    assessment_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("assessments.id"), nullable=False, index=True)
    project_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False, index=True)
    
    # Relationships
    assessment: Mapped["Assessment"] = relationship("Assessment", back_populates="evaluation_runs")
    project: Mapped["Project"] = relationship("Project", back_populates="evaluation_runs")
    run_models: Mapped[List["RunModel"]] = relationship("RunModel", back_populates="evaluation_run", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        CheckConstraint("status IN ('queued', 'running', 'completed', 'failed', 'cancelled')", name='evaluation_status_check'),
        CheckConstraint("retry_count >= 0", name='evaluation_retry_count_non_negative'),
        CheckConstraint("total_probes >= 0 OR total_probes IS NULL", name='evaluation_total_probes_non_negative'),
        CheckConstraint("completed_probes >= 0 OR completed_probes IS NULL", name='evaluation_completed_probes_non_negative'),
        CheckConstraint("length(name) >= 1", name='evaluation_name_not_empty'),
    )
    
    @property
    def duration(self) -> Optional[float]:
        """Get run duration in seconds."""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    @property
    def progress(self) -> float:
        """Get progress percentage."""
        if self.total_probes and self.total_probes > 0:
            return (self.completed_probes or 0) / self.total_probes * 100
        return 0.0
    
    @property
    def is_running(self) -> bool:
        """Check if evaluation is currently running."""
        return self.status == 'running'
    
    @property
    def is_completed(self) -> bool:
        """Check if evaluation is completed."""
        return self.status in ['completed', 'failed', 'cancelled']
    
    def start(self) -> None:
        """Start the evaluation."""
        self.status = 'running'
        self.started_at = datetime.utcnow()
    
    def complete(self) -> None:
        """Complete the evaluation successfully."""
        self.status = 'completed'
        self.completed_at = datetime.utcnow()
    
    def fail(self, error_message: str) -> None:
        """Fail the evaluation."""
        self.status = 'failed'
        self.completed_at = datetime.utcnow()
        self.error_message = error_message
        self.retry_count += 1
    
    def cancel(self) -> None:
        """Cancel the evaluation."""
        self.status = 'cancelled'
        self.completed_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'name': self.name,
            'description': self.description,
            'status': self.status,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'duration': self.duration,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'config': self.config,
            'total_probes': self.total_probes,
            'completed_probes': self.completed_probes,
            'progress': self.progress,
            'assessment_id': str(self.assessment_id),
            'project_id': str(self.project_id),
            'assessment_name': self.assessment.name if self.assessment else None,
            'project_name': self.project.name if self.project else None,
            'model_count': len(self.run_models) if self.run_models else 0,
            'is_running': self.is_running,
            'is_completed': self.is_completed
        })
        return base_dict
    
    def __repr__(self) -> str:
        return f"<EvaluationRun(id={self.id}, name={self.name}, status={self.status})>"


class RunModel(Base):
    """Model configuration for a specific evaluation run."""
    
    __tablename__ = "run_models"
    
    # Configuration snapshot
    model_params_snapshot: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False)
    
    # Status
    status: Mapped[str] = mapped_column(String(20), nullable=False, default='pending', index=True)
    
    # Timing
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Progress
    completed_probes: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    failed_probes: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # Foreign keys
    evaluation_run_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("evaluation_runs.id"), nullable=False, index=True)
    model_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("llm_models.id"), nullable=False, index=True)
    
    # Relationships
    evaluation_run: Mapped["EvaluationRun"] = relationship("EvaluationRun", back_populates="run_models")
    model: Mapped["LLMModel"] = relationship("LLMModel")
    probeset_results: Mapped[List["RunProbesetResult"]] = relationship("RunProbesetResult", back_populates="run_model", cascade="all, delete-orphan")
    capability_scores: Mapped[List["RunCapabilityScore"]] = relationship("RunCapabilityScore", back_populates="run_model", cascade="all, delete-orphan")
    assessment_scores: Mapped[List["RunAssessmentScore"]] = relationship("RunAssessmentScore", back_populates="run_model", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('evaluation_run_id', 'model_id', name='run_model_unique'),
        CheckConstraint("status IN ('pending', 'running', 'completed', 'failed')", name='run_model_status_check'),
        CheckConstraint("completed_probes >= 0", name='run_model_completed_probes_non_negative'),
        CheckConstraint("failed_probes >= 0", name='run_model_failed_probes_non_negative'),
    )
    
    @property
    def total_probes(self) -> int:
        """Get total probes executed."""
        return self.completed_probes + self.failed_probes
    
    @property
    def success_rate(self) -> float:
        """Get success rate percentage."""
        total = self.total_probes
        if total == 0:
            return 0.0
        return (self.completed_probes / total) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'model_params_snapshot': self.model_params_snapshot,
            'status': self.status,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'completed_probes': self.completed_probes,
            'failed_probes': self.failed_probes,
            'total_probes': self.total_probes,
            'success_rate': self.success_rate,
            'evaluation_run_id': str(self.evaluation_run_id),
            'model_id': str(self.model_id),
            'model_name': self.model.name if self.model else None
        })
        return base_dict
    
    def __repr__(self) -> str:
        model_name = self.model.name if self.model else 'None'
        return f"<RunModel(id={self.id}, model={model_name}, status={self.status})>"


class RunProbeResult(Base):
    """Individual probe execution result cache per model+probe+version.

    Mirrors Alembic table 'run_probe_results' used to cache raw probe executions.
    """
    
    __tablename__ = "run_probe_results"
    
    # Result data and execution metadata
    score: Mapped[float] = mapped_column(Float, nullable=False)
    execution_log: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    model_params_used: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False)
    executed_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())
    status: Mapped[str] = mapped_column(String(20), nullable=False, default='success')
    version_hash: Mapped[str] = mapped_column(String(64), nullable=False, index=True)
    execution_time_ms: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    tokens_used: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    cost_usd: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    
    # Foreign keys
    model_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("llm_models.id"), nullable=False, index=True)
    probe_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("probes.id"), nullable=False, index=True)
    
    # Relationships
    model: Mapped["LLMModel"] = relationship("LLMModel")
    probe: Mapped["Probe"] = relationship("Probe", back_populates="probe_results")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('model_id', 'probe_id', 'version_hash', name='probe_result_cache_unique'),
        CheckConstraint("score >= 0 AND score <= 1", name='probe_result_score_range'),
        CheckConstraint("status IN ('success', 'failed', 'timeout', 'error')", name='probe_result_status_check'),
        CheckConstraint("execution_time_ms >= 0 OR execution_time_ms IS NULL", name='probe_result_execution_time_non_negative'),
        CheckConstraint("tokens_used >= 0 OR tokens_used IS NULL", name='probe_result_tokens_non_negative'),
        CheckConstraint("cost_usd >= 0 OR cost_usd IS NULL", name='probe_result_cost_non_negative'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'score': self.score,
            'execution_log': self.execution_log,
            'model_params_used': self.model_params_used,
            'executed_at': self.executed_at.isoformat(),
            'status': self.status,
            'version_hash': self.version_hash,
            'execution_time_ms': self.execution_time_ms,
            'tokens_used': self.tokens_used,
            'cost_usd': self.cost_usd,
            'model_id': str(self.model_id),
            'probe_id': str(self.probe_id),
            'model_name': self.model.name if self.model else None,
            'probe_code': self.probe.code if self.probe else None,
        })
        return base_dict
    
    def __repr__(self) -> str:
        probe_code = self.probe.code if self.probe else 'None'
        return f"<RunProbeResult(probe={probe_code}, status={self.status}, score={self.score})>"


class RunProbesetResult(Base):
    """Aggregated probeset result for a specific run model."""
    
    __tablename__ = "run_probeset_results"
    
    # Result data
    score: Mapped[float] = mapped_column(Float, nullable=False)
    details: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    calculated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())
    
    # Metadata
    probe_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    successful_probes: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    
    # Foreign keys
    run_model_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("run_models.id"), nullable=False, index=True)
    probeset_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("probesets.id"), nullable=False, index=True)
    
    # Relationships
    run_model: Mapped["RunModel"] = relationship("RunModel", back_populates="probeset_results")
    probeset: Mapped["Probeset"] = relationship("Probeset", back_populates="probeset_results")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('run_model_id', 'probeset_id', name='probeset_result_unique'),
        CheckConstraint("score >= 0 AND score <= 1", name='probeset_result_score_range'),
        CheckConstraint("probe_count >= 0", name='probeset_result_probe_count_non_negative'),
        CheckConstraint("successful_probes >= 0", name='probeset_result_successful_probes_non_negative'),
        CheckConstraint("successful_probes <= probe_count", name='probeset_result_successful_probes_valid'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        base_dict = super().to_dict()
        base_dict.update({
            'score': self.score,
            'calculated_at': self.calculated_at.isoformat(),
            'details': self.details,
            'probe_count': self.probe_count,
            'successful_probes': self.successful_probes,
            'run_model_id': str(self.run_model_id),
            'probeset_id': str(self.probeset_id),
            'probeset_code': self.probeset.code if self.probeset else None,
        })
        return base_dict
    
    def __repr__(self) -> str:
        probeset_code = self.probeset.code if self.probeset else 'None'
        return f"<RunProbesetResult(probeset={probeset_code}, score={self.score})>"


class RunCapabilityScore(Base):
    """Capability score aggregated from probeset results for a run model."""
    
    __tablename__ = "run_capability_scores"
    
    # Result data
    score: Mapped[float] = mapped_column(Float, nullable=False)
    calculated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())
    
    # Metadata
    probeset_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    details: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    
    # Foreign keys
    run_model_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("run_models.id"), nullable=False, index=True)
    capability_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("capabilities.id"), nullable=False, index=True)
    
    # Relationships
    run_model: Mapped["RunModel"] = relationship("RunModel", back_populates="capability_scores")
    capability: Mapped["Capability"] = relationship("Capability", back_populates="capability_scores")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('run_model_id', 'capability_id', name='capability_score_unique'),
        CheckConstraint("score >= 0 AND score <= 1", name='capability_score_range'),
        CheckConstraint("probeset_count >= 0", name='capability_score_probeset_count_non_negative'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        base_dict = super().to_dict()
        base_dict.update({
            'score': self.score,
            'calculated_at': self.calculated_at.isoformat(),
            'probeset_count': self.probeset_count,
            'details': self.details,
            'run_model_id': str(self.run_model_id),
            'capability_id': str(self.capability_id),
            'capability_name': self.capability.name if self.capability else None,
            'capability_code': self.capability.code if self.capability else None,
        })
        return base_dict
    
    def __repr__(self) -> str:
        capability_name = self.capability.name if self.capability else 'None'
        return f"<RunCapabilityScore(capability={capability_name}, score={self.score})>"


class RunAssessmentScore(Base):
    """Assessment score calculated from capability scores."""
    
    __tablename__ = "run_assessment_scores"
    
    # Result data
    score: Mapped[float] = mapped_column(Float, nullable=False)
    calculated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())
    
    # Metadata
    capability_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    details: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    
    # Foreign keys
    run_model_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("run_models.id"), nullable=False, index=True)
    assessment_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("assessments.id"), nullable=False, index=True)
    
    # Relationships
    run_model: Mapped["RunModel"] = relationship("RunModel", back_populates="assessment_scores")
    assessment: Mapped["Assessment"] = relationship("Assessment", back_populates="assessment_scores")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('run_model_id', 'assessment_id', name='assessment_score_unique'),
        CheckConstraint("score >= 0 AND score <= 1", name='assessment_score_range'),
        CheckConstraint("capability_count >= 0", name='assessment_score_capability_count_non_negative'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'score': self.score,
            'calculated_at': self.calculated_at.isoformat(),
            'capability_count': self.capability_count,
            'details': self.details,
            'run_model_id': str(self.run_model_id),
            'assessment_id': str(self.assessment_id),
            'assessment_name': self.assessment.name if self.assessment else None,
            'assessment_code': self.assessment.code if self.assessment else None
        })
        return base_dict
    
    def __repr__(self) -> str:
        assessment_name = self.assessment.name if self.assessment else 'None'
        return f"<RunAssessmentScore(assessment={assessment_name}, score={self.score})>"