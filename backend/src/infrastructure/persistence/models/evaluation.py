"""
Evaluation models for SACRA2 system.
Models for the hierarchical evaluation framework: PROBE → PROBESET → CAPABILITY → ASSESSMENT
"""

import uuid
from typing import Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy import Column, String, Integer, Boolean, JSON, ForeignKey, UniqueConstraint, CheckConstraint, Text, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ....domain.models.base import Base


class Probe(Base):
    """Individual test probe (prompt/tool/regex/etc.)."""
    
    __tablename__ = "probes"
    
    # Core fields
    code: Mapped[str] = mapped_column(String(100), nullable=False, unique=True, index=True)
    name: Mapped[str] = mapped_column(String(200), nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Configuration
    params: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    version: Mapped[str] = mapped_column('probe_version', String(50), nullable=False, default='1.0.0')
    
    # Metadata
    category: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, index=True)
    tags: Mapped[Optional[List[str]]] = mapped_column(JSON, nullable=True)
    
    # Status
    active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Relationships
    probeset_probes: Mapped[List["ProbesetProbe"]] = relationship("ProbesetProbe", back_populates="probe", cascade="all, delete-orphan")
    probe_results: Mapped[List["RunProbeResult"]] = relationship("RunProbeResult", back_populates="probe", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('name', 'probe_version', name='probe_name_version_unique'),
        CheckConstraint("length(code) >= 1", name='probe_code_not_empty'),
        CheckConstraint("length(name) >= 1", name='probe_name_not_empty'),
        CheckConstraint("length(probe_version) >= 1", name='probe_version_not_empty'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'code': self.code,
            'name': self.name,
            'description': self.description,
            'params': self.params,
            'version': self.version,
            'category': self.category,
            'tags': self.tags,
            'active': self.active,
            'probeset_count': len(self.probeset_probes) if self.probeset_probes else 0
        })
        return base_dict
    
    def __repr__(self) -> str:
        return f"<Probe(id={self.id}, code={self.code}, version={self.version})>"


class Probeset(Base):
    """Set of probes with scoring method."""
    
    __tablename__ = "probesets"
    
    # Core fields
    code: Mapped[str] = mapped_column(String(100), nullable=False, unique=True, index=True)
    name: Mapped[str] = mapped_column(String(200), nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    version: Mapped[str] = mapped_column('probeset_version', String(50), nullable=False, default='1.0.0')
    
    # Scoring configuration
    scoring_method: Mapped[str] = mapped_column(String(20), nullable=False, default='avg')
    scoring_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    
    # Metadata
    category: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, index=True)
    tags: Mapped[Optional[List[str]]] = mapped_column(JSON, nullable=True)
    
    # Status
    active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Relationships
    probeset_probes: Mapped[List["ProbesetProbe"]] = relationship("ProbesetProbe", back_populates="probeset", cascade="all, delete-orphan")
    capability_sets: Mapped[List["CapabilitySet"]] = relationship("CapabilitySet", back_populates="probeset", cascade="all, delete-orphan")
    probeset_results: Mapped[List["RunProbesetResult"]] = relationship("RunProbesetResult", back_populates="probeset", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('name', 'probeset_version', name='probeset_name_version_unique'),
        CheckConstraint("scoring_method IN ('binary', 'avg', 'weighted', 'rule', 'custom')", name='probeset_scoring_method_check'),
        CheckConstraint("length(code) >= 1", name='probeset_code_not_empty'),
        CheckConstraint("length(name) >= 1", name='probeset_name_not_empty'),
    )
    
    @property
    def probe_count(self) -> int:
        """Get number of probes in this probeset."""
        return len(self.probeset_probes) if self.probeset_probes else 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'code': self.code,
            'name': self.name,
            'description': self.description,
            'version': self.version,
            'scoring_method': self.scoring_method,
            'scoring_config': self.scoring_config,
            'category': self.category,
            'tags': self.tags,
            'active': self.active,
            'probe_count': self.probe_count
        })
        return base_dict
    
    def __repr__(self) -> str:
        return f"<Probeset(id={self.id}, code={self.code}, scoring_method={self.scoring_method})>"


class ProbesetProbe(Base):
    """Association between probeset and probes with configuration."""
    
    __tablename__ = "probeset_probes"
    
    # Foreign keys
    probeset_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("probesets.id"), nullable=False, index=True)
    probe_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("probes.id"), nullable=False, index=True)
    
    # Configuration
    order: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    weight: Mapped[float] = mapped_column(Float, nullable=False, default=1.0)
    override_params: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    
    # Status
    enabled: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Relationships
    probeset: Mapped["Probeset"] = relationship("Probeset", back_populates="probeset_probes")
    probe: Mapped["Probe"] = relationship("Probe", back_populates="probeset_probes")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('probeset_id', 'probe_id', name='probeset_probe_unique'),
        CheckConstraint("weight > 0", name='probeset_probe_weight_positive'),
        CheckConstraint("\"order\" >= 0", name='probeset_probe_order_non_negative'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'probeset_id': str(self.probeset_id),
            'probe_id': str(self.probe_id),
            'probeset_name': self.probeset.name if self.probeset else None,
            'probe_name': self.probe.name if self.probe else None,
            'probe_code': self.probe.code if self.probe else None,
            'order': self.order,
            'weight': self.weight,
            'override_params': self.override_params,
            'enabled': self.enabled
        })
        return base_dict
    
    def __repr__(self) -> str:
        probeset_name = self.probeset.name if self.probeset else 'None'
        probe_name = self.probe.name if self.probe else 'None'
        return f"<ProbesetProbe(probeset={probeset_name}, probe={probe_name}, order={self.order})>"


class Capability(Base):
    """Capability category (e.g., prompt-leaking, tool-leaking, pii-leaking)."""
    
    __tablename__ = "capabilities"
    
    # Core fields
    code: Mapped[str] = mapped_column(String(100), nullable=False, unique=True, index=True)
    name: Mapped[str] = mapped_column(String(200), nullable=False, unique=True, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Metadata
    category: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, index=True)
    severity: Mapped[str] = mapped_column(String(20), nullable=False, default='medium')
    
    # Status
    active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Relationships
    capability_sets: Mapped[List["CapabilitySet"]] = relationship("CapabilitySet", back_populates="capability", cascade="all, delete-orphan")
    assessment_capabilities: Mapped[List["AssessmentCapability"]] = relationship("AssessmentCapability", back_populates="capability", cascade="all, delete-orphan")
    capability_scores: Mapped[List["RunCapabilityScore"]] = relationship("RunCapabilityScore", back_populates="capability", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        CheckConstraint("severity IN ('low', 'medium', 'high', 'critical')", name='capability_severity_check'),
        CheckConstraint("length(code) >= 1", name='capability_code_not_empty'),
        CheckConstraint("length(name) >= 1", name='capability_name_not_empty'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'code': self.code,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'severity': self.severity,
            'active': self.active,
            'probeset_count': len(self.capability_sets) if self.capability_sets else 0
        })
        return base_dict
    
    def __repr__(self) -> str:
        return f"<Capability(id={self.id}, code={self.code}, severity={self.severity})>"


class CapabilitySet(Base):
    """Association between capability and probeset with weights."""
    
    __tablename__ = "capability_sets"
    
    # Foreign keys
    capability_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("capabilities.id"), nullable=False, index=True)
    probeset_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("probesets.id"), nullable=False, index=True)
    
    # Configuration
    weight: Mapped[float] = mapped_column(Float, nullable=False, default=1.0)
    
    # Relationships
    capability: Mapped["Capability"] = relationship("Capability", back_populates="capability_sets")
    probeset: Mapped["Probeset"] = relationship("Probeset", back_populates="capability_sets")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('capability_id', 'probeset_id', name='capability_probeset_unique'),
        CheckConstraint("weight >= 0", name='capability_set_weight_non_negative'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'capability_id': str(self.capability_id),
            'probeset_id': str(self.probeset_id),
            'capability_name': self.capability.name if self.capability else None,
            'capability_code': self.capability.code if self.capability else None,
            'probeset_name': self.probeset.name if self.probeset else None,
            'probeset_code': self.probeset.code if self.probeset else None,
            'weight': self.weight
        })
        return base_dict
    
    def __repr__(self) -> str:
        capability_name = self.capability.name if self.capability else 'None'
        probeset_name = self.probeset.name if self.probeset else 'None'
        return f"<CapabilitySet(capability={capability_name}, probeset={probeset_name}, weight={self.weight})>"


class Assessment(Base):
    """Top-level assessment (e.g., SACRA = Security + Alignment + Confidentiality + Rules)."""
    
    __tablename__ = "assessments"
    
    # Core fields
    code: Mapped[str] = mapped_column(String(100), nullable=False, unique=True, index=True)
    name: Mapped[str] = mapped_column(String(200), nullable=False, unique=True, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    version: Mapped[str] = mapped_column('assessment_version', String(50), nullable=False, default='1.0.0')
    
    # Metadata
    category: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, index=True)
    
    # Status
    active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Relationships
    assessment_capabilities: Mapped[List["AssessmentCapability"]] = relationship("AssessmentCapability", back_populates="assessment", cascade="all, delete-orphan")
    evaluation_runs: Mapped[List["EvaluationRun"]] = relationship("EvaluationRun", back_populates="assessment", cascade="all, delete-orphan")
    assessment_scores: Mapped[List["RunAssessmentScore"]] = relationship("RunAssessmentScore", back_populates="assessment", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        CheckConstraint("length(code) >= 1", name='assessment_code_not_empty'),
        CheckConstraint("length(name) >= 1", name='assessment_name_not_empty'),
        CheckConstraint("length(assessment_version) >= 1", name='assessment_version_not_empty'),
    )
    
    @property
    def capability_count(self) -> int:
        """Get number of capabilities in this assessment."""
        return len(self.assessment_capabilities) if self.assessment_capabilities else 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'code': self.code,
            'name': self.name,
            'description': self.description,
            'version': self.version,
            'category': self.category,
            'active': self.active,
            'capability_count': self.capability_count
        })
        return base_dict
    
    def __repr__(self) -> str:
        return f"<Assessment(id={self.id}, code={self.code}, version={self.version})>"


class AssessmentCapability(Base):
    """Association between assessment and capability with weights."""
    
    __tablename__ = "assessment_capabilities"
    
    # Foreign keys
    assessment_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("assessments.id"), nullable=False, index=True)
    capability_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("capabilities.id"), nullable=False, index=True)
    
    # Configuration
    weight: Mapped[float] = mapped_column(Float, nullable=False, default=1.0)
    
    # Relationships
    assessment: Mapped["Assessment"] = relationship("Assessment", back_populates="assessment_capabilities")
    capability: Mapped["Capability"] = relationship("Capability", back_populates="assessment_capabilities")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('assessment_id', 'capability_id', name='assessment_capability_unique'),
        CheckConstraint("weight >= 0", name='assessment_capability_weight_non_negative'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'assessment_id': str(self.assessment_id),
            'capability_id': str(self.capability_id),
            'assessment_name': self.assessment.name if self.assessment else None,
            'assessment_code': self.assessment.code if self.assessment else None,
            'capability_name': self.capability.name if self.capability else None,
            'capability_code': self.capability.code if self.capability else None,
            'weight': self.weight
        })
        return base_dict
    
    def __repr__(self) -> str:
        assessment_name = self.assessment.name if self.assessment else 'None'
        capability_name = self.capability.name if self.capability else 'None'
        return f"<AssessmentCapability(assessment={assessment_name}, capability={capability_name}, weight={self.weight})>"