"""
Database connection and session management for SACRA2.
Provides async database connections, session management, and connection pooling.
"""

import asyncio
from typing import AsyncGenerator, Optional
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.pool import N<PERSON><PERSON><PERSON>
import logging

# Import from core
from ..core.config import settings
from ..core.logging import get_logger


logger = get_logger(__name__)


class DatabaseManager:
    """Manages database connections and sessions"""
    
    def __init__(self):
        self.engine = None
        self.session_factory = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize database connection"""
        if self._initialized:
            return
        
        try:
            # Build database URL
            database_url = self._build_database_url()
            
            # Create engine
            db_conf = settings.database
            echo = getattr(db_conf, "echo", False)
            pool_pre_ping = getattr(db_conf, "pool_pre_ping", True)
            pool_recycle = getattr(db_conf, "pool_recycle", 1800)

            self.engine = create_async_engine(
                database_url,
                echo=echo,
                pool_size=db_conf.pool_size,
                max_overflow=db_conf.max_overflow,
                pool_pre_ping=pool_pre_ping,
                pool_recycle=pool_recycle,
                future=True
            )
            
            # Create session factory
            self.session_factory = async_sessionmaker(
                self.engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=False,
                autocommit=False
            )
            
            self._initialized = True
            logger.info("Database connection initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {str(e)}")
            raise
    
    def _build_database_url(self) -> str:
        """Build database URL from settings"""
        db_conf = settings.database
        # Use configured URL property
        return db_conf.url
    
    async def close(self) -> None:
        """Close database connection"""
        if self.engine:
            await self.engine.dispose()
            self._initialized = False
            logger.info("Database connection closed")
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session"""
        if not self._initialized:
            await self.initialize()
        
        async with self.session_factory() as session:
            try:
                yield session
            except Exception as e:
                await session.rollback()
                logger.error(f"Session error: {str(e)}")
                raise
            finally:
                await session.close()
    
    async def get_raw_session(self) -> AsyncSession:
        """Get raw database session (for repositories)"""
        if not self._initialized:
            await self.initialize()
        
        return self.session_factory()
    
    async def health_check(self) -> bool:
        """Check database health"""
        try:
            async with self.get_session() as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"Database health check failed: {str(e)}")
            return False
    
    async def create_tables(self) -> None:
        """Create all database tables"""
        from ..domain.models.base import Base
        
        if not self._initialized:
            await self.initialize()
        
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database tables created")
    
    async def drop_tables(self) -> None:
        """Drop all database tables"""
        from ..domain.models.base import Base
        
        if not self._initialized:
            await self.initialize()
        
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        
        logger.info("Database tables dropped")


# Global database manager
database_manager = DatabaseManager()


class DatabaseSession:
    """Context manager for database sessions"""
    
    def __init__(self, session: Optional[AsyncSession] = None):
        self.session = session
        self._managed_session = session is None
    
    async def __aenter__(self) -> AsyncSession:
        """Enter context"""
        if self._managed_session:
            self.session = await database_manager.get_raw_session()
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit context"""
        if self._managed_session and self.session:
            if exc_type:
                await self.session.rollback()
            else:
                await self.session.commit()
            await self.session.close()


# Utility functions
async def get_database_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session for FastAPI dependency"""
    async with database_manager.get_session() as session:
        yield session


async def initialize_database():
    """Initialize database connection"""
    await database_manager.initialize()


async def close_database():
    """Close database connection"""
    await database_manager.close()


async def check_database_health() -> dict:
    """Check database health status"""
    is_healthy = await database_manager.health_check()
    
    return {
        "status": "healthy" if is_healthy else "unhealthy",
        "database": "connected" if is_healthy else "disconnected"
    }


# Migration utilities
class MigrationManager:
    """Database migration manager"""
    
    def __init__(self):
        self.database_manager = database_manager
    
    async def create_migration(self, message: str) -> str:
        """Create new migration"""
        # This would integrate with Alembic
        # For now, just return placeholder
        return f"migration_{int(time.time())}_{message.replace(' ', '_')}"
    
    async def apply_migrations(self) -> None:
        """Apply pending migrations"""
        # This would integrate with Alembic
        logger.info("Applying database migrations")
    
    async def rollback_migration(self, migration_id: str) -> None:
        """Rollback specific migration"""
        # This would integrate with Alembic
        logger.info(f"Rolling back migration: {migration_id}")


# Connection pool monitoring
class ConnectionPoolMonitor:
    """Monitor database connection pool"""
    
    def __init__(self, database_manager: DatabaseManager):
        self.database_manager = database_manager
    
    async def get_pool_stats(self) -> dict:
        """Get connection pool statistics"""
        if not self.database_manager.engine:
            return {}
        
        pool = self.database_manager.engine.pool
        
        return {
            "size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow()
        }
    
    async def reset_pool(self) -> None:
        """Reset connection pool"""
        if self.database_manager.engine:
            await self.database_manager.engine.dispose()
            self.database_manager._initialized = False
            await self.database_manager.initialize()


# Transaction utilities
class Transaction:
    """Transaction context manager"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self._transaction = None
    
    async def __aenter__(self):
        """Start transaction"""
        self._transaction = self.session.begin()
        await self._transaction.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """End transaction"""
        if self._transaction:
            await self._transaction.__aexit__(exc_type, exc_val, exc_tb)


class TransactionManager:
    """Manage database transactions"""
    
    def __init__(self):
        self.active_transactions: Dict[str, Transaction] = {}
    
    async def begin_transaction(self, transaction_id: str) -> Transaction:
        """Begin new transaction"""
        session = await database_manager.get_raw_session()
        transaction = Transaction(session)
        self.active_transactions[transaction_id] = transaction
        return transaction
    
    async def commit_transaction(self, transaction_id: str) -> None:
        """Commit transaction"""
        if transaction_id in self.active_transactions:
            transaction = self.active_transactions[transaction_id]
            await transaction.session.commit()
            del self.active_transactions[transaction_id]
    
    async def rollback_transaction(self, transaction_id: str) -> None:
        """Rollback transaction"""
        if transaction_id in self.active_transactions:
            transaction = self.active_transactions[transaction_id]
            await transaction.session.rollback()
            del self.active_transactions[transaction_id]


# Export commonly used items
__all__ = [
    'database_manager',
    'DatabaseSession',
    'get_database_session',
    'initialize_database',
    'close_database',
    'check_database_health',
    'MigrationManager',
    'ConnectionPoolMonitor',
    'Transaction',
    'TransactionManager'
]