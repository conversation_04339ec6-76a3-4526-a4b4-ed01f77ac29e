"""
Advanced decorators for SACRA2 backend.
Implements retry, caching, rate limiting, and monitoring patterns.
"""

import asyncio
import functools
import time
import hashlib
import json
from typing import Any, Callable, Optional, Dict, List, Union, TypeVar, Generic
from datetime import datetime, timed<PERSON>ta
from functools import wraps
import logging
from dataclasses import dataclass
import inspect

from .exceptions import RateLimitExceeded, CircuitBreakerOpen
from .types import Result, ResultStatus


# Type variables
T = TypeVar('T')
F = TypeVar('F', bound=Callable[..., Any])


# Logger
logger = logging.getLogger(__name__)


# Cache configuration
@dataclass
class CacheConfig:
    """Cache configuration for caching decorator"""
    ttl: int = 3600  # seconds
    key_prefix: Optional[str] = None
    cache_none: bool = False
    version: str = "1"
    tags: Optional[List[str]] = None


# Rate limiting configuration
@dataclass
class RateLimitConfig:
    """Rate limit configuration"""
    requests: int
    window: int  # seconds
    key_func: Optional[Callable[..., str]] = None
    skip_if: Optional[Callable[..., bool]] = None


# Circuit breaker configuration
@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration"""
    failure_threshold: int = 5
    recovery_timeout: int = 60  # seconds
    success_threshold: int = 1
    expected_exceptions: tuple = (Exception,)


# Monitoring configuration
@dataclass
class MonitorConfig:
    """Monitoring configuration"""
    metric_name: Optional[str] = None
    tags: Optional[Dict[str, str]] = None
    log_args: bool = False
    log_result: bool = False
    log_exceptions: bool = True


class CacheManager:
    """In-memory cache manager for decorator"""
    
    def __init__(self):
        self._cache: Dict[str, Dict[str, Any]] = {}
    
    def _get_key(self, func: Callable, args: tuple, kwargs: dict, config: CacheConfig) -> str:
        """Generate cache key"""
        # Create key from function name and arguments
        key_parts = []
        
        if config.key_prefix:
            key_parts.append(config.key_prefix)
        
        key_parts.append(func.__module__)
        key_parts.append(func.__name__)
        
        # Hash arguments
        args_str = json.dumps(args, default=str, sort_keys=True)
        kwargs_str = json.dumps(kwargs, default=str, sort_keys=True)
        args_hash = hashlib.md5(f"{args_str}{kwargs_str}".encode()).hexdigest()
        key_parts.append(args_hash)
        
        if config.version:
            key_parts.append(config.version)
        
        return ":".join(key_parts)
    
    def get(self, key: str) -> Any:
        """Get value from cache"""
        if key in self._cache:
            entry = self._cache[key]
            if datetime.now() < entry['expires_at']:
                return entry['value']
            else:
                del self._cache[key]
        return None
    
    def set(self, key: str, value: Any, ttl: int):
        """Set value in cache"""
        expires_at = datetime.now() + timedelta(seconds=ttl)
        self._cache[key] = {
            'value': value,
            'expires_at': expires_at,
            'created_at': datetime.now()
        }
    
    def clear(self):
        """Clear all cache"""
        self._cache.clear()
    
    def invalidate_pattern(self, pattern: str):
        """Invalidate cache entries matching pattern"""
        keys_to_delete = [k for k in self._cache.keys() if pattern in k]
        for key in keys_to_delete:
            del self._cache[key]


# Global cache manager
_cache_manager = CacheManager()


def cache(
    ttl: int = 3600,
    key_prefix: Optional[str] = None,
    cache_none: bool = False,
    version: str = "1",
    tags: Optional[List[str]] = None
):
    """
    Decorator for caching function results.
    
    Args:
        ttl: Cache time to live in seconds
        key_prefix: Prefix for cache keys
        cache_none: Whether to cache None values
        version: Cache version for invalidation
        tags: Tags for cache invalidation
    """
    config = CacheConfig(
        ttl=ttl,
        key_prefix=key_prefix,
        cache_none=cache_none,
        version=version,
        tags=tags
    )
    
    def decorator(func: F) -> F:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            cache_key = _cache_manager._get_key(func, args, kwargs, config)
            
            # Try to get from cache
            cached_result = _cache_manager.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {cache_key}")
                return cached_result
            
            # Execute function
            result = await func(*args, **kwargs)
            
            # Cache result if not None or cache_none is True
            if result is not None or config.cache_none:
                _cache_manager.set(cache_key, result, config.ttl)
                logger.debug(f"Cached result for {cache_key}")
            
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            cache_key = _cache_manager._get_key(func, args, kwargs, config)
            
            # Try to get from cache
            cached_result = _cache_manager.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {cache_key}")
                return cached_result
            
            # Execute function
            result = func(*args, **kwargs)
            
            # Cache result if not None or cache_none is True
            if result is not None or config.cache_none:
                _cache_manager.set(cache_key, result, config.ttl)
                logger.debug(f"Cached result for {cache_key}")
            
            return result
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def rate_limit(
    requests: int,
    window: int,
    key_func: Optional[Callable[..., str]] = None,
    skip_if: Optional[Callable[..., bool]] = None
):
    """
    Decorator for rate limiting.
    
    Args:
        requests: Number of requests allowed
        window: Time window in seconds
        key_func: Function to generate rate limit key
        skip_if: Function to skip rate limiting
    """
    config = RateLimitConfig(
        requests=requests,
        window=window,
        key_func=key_func,
        skip_if=skip_if
    )
    
    # Rate limit storage
    _rate_limits: Dict[str, List[float]] = {}
    
    def decorator(func: F) -> F:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            if config.skip_if and config.skip_if(*args, **kwargs):
                return await func(*args, **kwargs)
            
            # Generate key
            if config.key_func:
                key = config.key_func(*args, **kwargs)
            else:
                key = f"{func.__module__}.{func.__name__}"
            
            # Check rate limit
            now = time.time()
            if key not in _rate_limits:
                _rate_limits[key] = []
            
            # Remove old entries
            _rate_limits[key] = [t for t in _rate_limits[key] if now - t < config.window]
            
            # Check if limit exceeded
            if len(_rate_limits[key]) >= config.requests:
                raise RateLimitExceeded(
                    service_name=func.__name__,
                    limit=config.requests,
                    window=config.window
                )
            
            # Record request
            _rate_limits[key].append(now)
            
            return await func(*args, **kwargs)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            if config.skip_if and config.skip_if(*args, **kwargs):
                return func(*args, **kwargs)
            
            # Generate key
            if config.key_func:
                key = config.key_func(*args, **kwargs)
            else:
                key = f"{func.__module__}.{func.__name__}"
            
            # Check rate limit
            now = time.time()
            if key not in _rate_limits:
                _rate_limits[key] = []
            
            # Remove old entries
            _rate_limits[key] = [t for t in _rate_limits[key] if now - t < config.window]
            
            # Check if limit exceeded
            if len(_rate_limits[key]) >= config.requests:
                raise RateLimitExceeded(
                    service_name=func.__name__,
                    limit=config.requests,
                    window=config.window
                )
            
            # Record request
            _rate_limits[key].append(now)
            
            return func(*args, **kwargs)
        
        # Return appropriate wrapper
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class CircuitBreaker:
    """Circuit breaker implementation"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self._state = "closed"
        self._failure_count = 0
        self._success_count = 0
        self._last_failure_time = None
        self._lock = asyncio.Lock()
    
    async def call(self, func: Callable, *args, **kwargs):
        """Call function with circuit breaker protection"""
        async with self._lock:
            # Check if circuit is open
            if self._state == "open":
                if self._last_failure_time and time.time() - self._last_failure_time > self.config.recovery_timeout:
                    self._state = "half_open"
                    self._success_count = 0
                else:
                    raise CircuitBreakerOpen(
                        service_name=func.__name__,
                        failure_count=self._failure_count,
                        timeout=self.config.recovery_timeout
                    )
            
            # Check if circuit is half-open
            if self._state == "half_open" and self._success_count >= self.config.success_threshold:
                self._state = "closed"
                self._failure_count = 0
                self._success_count = 0
        
        try:
            result = await func(*args, **kwargs)
            
            async with self._lock:
                if self._state == "half_open":
                    self._success_count += 1
                elif self._state == "closed":
                    self._failure_count = 0
            
            return result
            
        except self.config.expected_exceptions as e:
            async with self._lock:
                self._failure_count += 1
                self._last_failure_time = time.time()
                
                if self._failure_count >= self.config.failure_threshold:
                    self._state = "open"
            
            raise


def circuit_breaker(
    failure_threshold: int = 5,
    recovery_timeout: int = 60,
    success_threshold: int = 1,
    expected_exceptions: tuple = (Exception,)
):
    """
    Decorator for circuit breaker pattern.
    
    Args:
        failure_threshold: Number of failures before opening circuit
        recovery_timeout: Time to wait before attempting recovery
        success_threshold: Success count to close circuit in half-open state
        expected_exceptions: Exceptions to count as failures
    """
    config = CircuitBreakerConfig(
        failure_threshold=failure_threshold,
        recovery_timeout=recovery_timeout,
        success_threshold=success_threshold,
        expected_exceptions=expected_exceptions
    )
    
    def decorator(func: F) -> F:
        breaker = CircuitBreaker(config)
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            return await breaker.call(func, *args, **kwargs)
        
        return async_wrapper
    
    return decorator


def monitor(
    metric_name: Optional[str] = None,
    tags: Optional[Dict[str, str]] = None,
    log_args: bool = False,
    log_result: bool = False,
    log_exceptions: bool = True
):
    """
    Decorator for monitoring function execution.
    
    Args:
        metric_name: Name for the metric
        tags: Tags to attach to metrics
        log_args: Whether to log function arguments
        log_result: Whether to log function results
        log_exceptions: Whether to log exceptions
    """
    config = MonitorConfig(
        metric_name=metric_name,
        tags=tags or {},
        log_args=log_args,
        log_result=log_result,
        log_exceptions=log_exceptions
    )
    
    def decorator(func: F) -> F:
        metric_name_final = config.metric_name or f"{func.__module__}.{func.__name__}"
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                logger.info(
                    f"Starting {metric_name_final}",
                    extra={
                        "metric_name": metric_name_final,
                        "tags": config.tags,
                        "args": str(args) if config.log_args else None,
                        "kwargs": str(kwargs) if config.log_args else None
                    }
                )
                
                result = await func(*args, **kwargs)
                
                duration = time.time() - start_time
                logger.info(
                    f"Completed {metric_name_final}",
                    extra={
                        "metric_name": metric_name_final,
                        "duration": duration,
                        "tags": config.tags,
                        "result": str(result) if config.log_result else None
                    }
                )
                
                return result
                
            except Exception as e:
                if config.log_exceptions:
                    duration = time.time() - start_time
                    logger.error(
                        f"Failed {metric_name_final}",
                        extra={
                            "metric_name": metric_name_final,
                            "duration": duration,
                            "tags": config.tags,
                            "error": str(e)
                        },
                        exc_info=True
                    )
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                logger.info(
                    f"Starting {metric_name_final}",
                    extra={
                        "metric_name": metric_name_final,
                        "tags": config.tags,
                        "args": str(args) if config.log_args else None,
                        "kwargs": str(kwargs) if config.log_args else None
                    }
                )
                
                result = func(*args, **kwargs)
                
                duration = time.time() - start_time
                logger.info(
                    f"Completed {metric_name_final}",
                    extra={
                        "metric_name": metric_name_final,
                        "duration": duration,
                        "tags": config.tags,
                        "result": str(result) if config.log_result else None
                    }
                )
                
                return result
                
            except Exception as e:
                if config.log_exceptions:
                    duration = time.time() - start_time
                    logger.error(
                        f"Failed {metric_name_final}",
                        extra={
                            "metric_name": metric_name_final,
                            "duration": duration,
                            "tags": config.tags,
                            "error": str(e)
                        },
                        exc_info=True
                    )
                raise
        
        # Return appropriate wrapper
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def retry(
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    exceptions: tuple = (Exception,),
    max_delay: float = 60.0
):
    """
    Decorator for retrying failed operations.
    
    Args:
        max_attempts: Maximum number of attempts
        delay: Initial delay between attempts
        backoff: Backoff multiplier
        exceptions: Exceptions to retry on
        max_delay: Maximum delay between attempts
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt == max_attempts - 1:
                        raise
                    
                    logger.warning(
                        f"Retry attempt {attempt + 1}/{max_attempts} for {func.__name__}",
                        extra={"error": str(e), "delay": current_delay}
                    )
                    
                    await asyncio.sleep(current_delay)
                    current_delay = min(current_delay * backoff, max_delay)
            
            raise last_exception
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt == max_attempts - 1:
                        raise
                    
                    logger.warning(
                        f"Retry attempt {attempt + 1}/{max_attempts} for {func.__name__}",
                        extra={"error": str(e), "delay": current_delay}
                    )
                    
                    time.sleep(current_delay)
                    current_delay = min(current_delay * backoff, max_delay)
            
            raise last_exception
        
        # Return appropriate wrapper
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def validate_input(validator_func: Callable[[Any], bool], error_message: str = "Invalid input"):
    """
    Decorator for input validation.
    
    Args:
        validator_func: Function to validate input
        error_message: Error message for validation failure
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Validate positional arguments
            for arg in args:
                if not validator_func(arg):
                    raise ValueError(error_message)
            
            # Validate keyword arguments
            for key, value in kwargs.items():
                if not validator_func(value):
                    raise ValueError(f"{error_message} for parameter {key}")
            
            return await func(*args, **kwargs)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # Validate positional arguments
            for arg in args:
                if not validator_func(arg):
                    raise ValueError(error_message)
            
            # Validate keyword arguments
            for key, value in kwargs.items():
                if not validator_func(value):
                    raise ValueError(f"{error_message} for parameter {key}")
            
            return func(*args, **kwargs)
        
        # Return appropriate wrapper
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# Utility functions for cache management
def invalidate_cache(pattern: str):
    """Invalidate cache entries matching pattern"""
    _cache_manager.invalidate_pattern(pattern)


def clear_cache():
    """Clear all cache"""
    _cache_manager.clear()


# Combined decorator for common patterns
def resilient_service(
    cache_ttl: int = 300,
    rate_limit_requests: int = 100,
    rate_limit_window: int = 60,
    circuit_failure_threshold: int = 5,
    circuit_recovery_timeout: int = 60,
    max_retries: int = 3,
    retry_delay: float = 1.0
):
    """
    Combined decorator for resilient service operations.
    
    Includes: caching, rate limiting, circuit breaker, retry, and monitoring.
    """
    def decorator(func: F) -> F:
        return cache(cache_ttl)(
            rate_limit(rate_limit_requests, rate_limit_window)(
                circuit_breaker(circuit_failure_threshold, circuit_recovery_timeout)(
                    retry(max_attempts=max_retries, delay=retry_delay)(
                        monitor(metric_name=f"service.{func.__name__}")(
                            func
                        )
                    )
                )
            )
        )
    
    return decorator