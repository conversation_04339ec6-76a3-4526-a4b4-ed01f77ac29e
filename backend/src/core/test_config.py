"""
Test configuration for SACRA2 Backend.
"""

import os
import tempfile
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class AppConfig(BaseSettings):
    app_name: str = "SACRA2 Test"
    app_version: str = "1.0.0"
    api_v1_prefix: str = "/api/v1"
    environment: str = "testing"
    testing: bool = True
    debug: bool = True
    host: str = "127.0.0.1"
    port: int = 8000
    log_level: str = "DEBUG"
    log_file: Optional[str] = None

class DatabaseConfig(BaseSettings):
    db_uri: str = "sqlite:///./test_sacra2.db"
    db_echo: bool = False

class SecurityConfig(BaseSettings):
    cors_origins: str = "http://localhost:3000,http://127.0.0.1:3000"
    cors_allow_methods: str = "*"
    cors_allow_headers: str = "*"

class TestSettings(BaseSettings):
    """Test-specific settings with nested structure."""
    app: AppConfig = AppConfig()
    db: DatabaseConfig = DatabaseConfig()
    security: SecurityConfig = SecurityConfig()

    class Config:
        env_file = ".env.test"
        case_sensitive = False
        extra = "ignore"


def get_test_settings() -> TestSettings:
    """Get test settings."""
    return TestSettings()


# Global test settings instance
test_settings = get_test_settings()


def get_memory_db_url() -> str:
    """Get in-memory SQLite database URL."""
    return "sqlite:///:memory:"


def get_temp_db_url() -> str:
    """Get temporary file SQLite database URL."""
    temp_file = tempfile.mktemp(suffix='.db')
    return f"sqlite:///{temp_file}"


def get_test_db_url(use_memory: bool = True) -> str:
    """Get test database URL."""
    if use_memory:
        return get_memory_db_url()
    else:
        return get_temp_db_url()