"""
Common dependency providers used by the API and application layers.

- `get_uow`: yields a UnitOfWork for transactional operations
- `get_db_session`: yields an AsyncSession bound to the configured engine

These functions are framework-agnostic and can be used with FastAPI's Depends
or any other consumer that expects async generators.
"""
from __future__ import annotations

from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession

from src.infrastructure.uow import UnitOfWork
from src.infrastructure.database import get_database_session


async def get_uow() -> AsyncGenerator[UnitOfWork, None]:
    """Provide a UnitOfWork instance within a transactional scope.

    Usage in FastAPI:
        @router.post("/items")
        async def create_item(uow: UnitOfWork = Depends(get_uow)):
            async with uow as tx:
                repo = tx.repositories.get_repository(MyModel)
                ...
    """
    uow = UnitOfWork()
    async with uow as tx:
        yield tx


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Provide a raw AsyncSession (use UoW for most business operations)."""
    async for session in get_database_session():
        yield session
