"""
Custom exception classes for SACRA2 backend.
Implements comprehensive error handling with detailed error contexts.
"""

class BaseAppException(Exception):
    """Base exception for all application exceptions"""
    pass

from typing import Any, Dict, Optional, List
from http import HTTPStatus
from dataclasses import dataclass


@dataclass
class ErrorContext:
    """Error context for detailed error information"""
    field: Optional[str] = None
    value: Any = None
    constraint: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class SACRA2Exception(Exception):
    """Base exception for all SACRA2 custom exceptions"""
    
    def __init__(
        self,
        message: str,
        status_code: int = HTTPStatus.INTERNAL_SERVER_ERROR,
        error_code: Optional[str] = None,
        context: Optional[ErrorContext] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.error_code = error_code or self.__class__.__name__
        self.context = context
        self.details = details or {}


# Domain Exceptions
class DomainException(SACRA2Exception):
    """Base exception for domain-specific errors"""
    pass


class InvalidStateTransition(DomainException):
    """Raised when attempting invalid state transition"""
    
    def __init__(self, message: str, current_state: str, target_state: str):
        super().__init__(
            message,
            status_code=HTTPStatus.BAD_REQUEST,
            error_code="INVALID_STATE_TRANSITION",
            context=ErrorContext(
                details={
                    "current_state": current_state,
                    "target_state": target_state
                }
            )
        )


class BusinessRuleViolation(DomainException):
    """Raised when business rules are violated"""
    
    def __init__(self, message: str, rule_name: str, rule_details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message,
            status_code=HTTPStatus.BAD_REQUEST,
            error_code="BUSINESS_RULE_VIOLATION",
            context=ErrorContext(
                details={
                    "rule_name": rule_name,
                    "rule_details": rule_details or {}
                }
            )
        )


class EntityNotFound(DomainException):
    """Raised when entity is not found"""
    
    def __init__(self, entity_type: str, entity_id: Any, identifier_field: str = "id"):
        super().__init__(
            f"{entity_type} not found",
            status_code=HTTPStatus.NOT_FOUND,
            error_code="ENTITY_NOT_FOUND",
            context=ErrorContext(
                field=identifier_field,
                value=entity_id,
                details={"entity_type": entity_type}
            )
        )


class DuplicateEntity(DomainException):
    """Raised when attempting to create duplicate entity"""
    
    def __init__(self, entity_type: str, field: str, value: Any):
        super().__init__(
            f"{entity_type} with {field} '{value}' already exists",
            status_code=HTTPStatus.CONFLICT,
            error_code="DUPLICATE_ENTITY",
            context=ErrorContext(
                field=field,
                value=value,
                details={"entity_type": entity_type}
            )
        )


# Validation Exceptions
class ValidationException(SACRA2Exception):
    """Base exception for validation errors"""
    pass


class InvalidValueError(ValidationException):
    """Raised when value is invalid"""
    
    def __init__(self, field: str, value: Any, constraint: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            f"Invalid value for {field}: {value}",
            status_code=HTTPStatus.BAD_REQUEST,
            error_code="INVALID_VALUE",
            context=ErrorContext(
                field=field,
                value=value,
                constraint=constraint,
                details=details
            )
        )


class MissingRequiredField(ValidationException):
    """Raised when required field is missing"""
    
    def __init__(self, field: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            f"Missing required field: {field}",
            status_code=HTTPStatus.BAD_REQUEST,
            error_code="MISSING_REQUIRED_FIELD",
            context=ErrorContext(
                field=field,
                details=details
            )
        )


class ValidationError(ValidationException):
    """Generic validation error compatible with repository layer usage."""
    
    def __init__(self, field: Optional[str] = None, value: Any = None, message: str = "Validation error", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message,
            status_code=HTTPStatus.BAD_REQUEST,
            error_code="VALIDATION_ERROR",
            context=ErrorContext(
                field=field,
                value=value,
                details=details
            )
        )


# Infrastructure Exceptions
class InfrastructureException(SACRA2Exception):
    """Base exception for infrastructure errors"""
    pass


class DatabaseConnectionError(InfrastructureException):
    """Raised when database connection fails"""
    
    def __init__(self, message: str, connection_details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message,
            status_code=HTTPStatus.SERVICE_UNAVAILABLE,
            error_code="DATABASE_CONNECTION_ERROR",
            details=connection_details
        )


class CacheConnectionError(InfrastructureException):
    """Raised when cache connection fails"""
    
    def __init__(self, message: str, connection_details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message,
            status_code=HTTPStatus.SERVICE_UNAVAILABLE,
            error_code="CACHE_CONNECTION_ERROR",
            details=connection_details
        )


class MessageQueueError(InfrastructureException):
    """Raised when message queue operation fails"""
    
    def __init__(self, message: str, queue_name: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message,
            status_code=HTTPStatus.SERVICE_UNAVAILABLE,
            error_code="MESSAGE_QUEUE_ERROR",
            context=ErrorContext(
                details={"queue_name": queue_name, **(details or {})}
            )
        )


class DatabaseError(InfrastructureException):
    """Database operation error (adapter for repository layer)."""
    
    def __init__(self, message: str = "Database error", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message,
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            error_code="DATABASE_ERROR",
            details=details
        )


# Service Exceptions
class ServiceException(SACRA2Exception):
    """Base exception for service layer errors"""
    pass


class RateLimitExceeded(ServiceException):
    """Raised when rate limit is exceeded"""
    
    def __init__(self, service_name: str, limit: int, window: int, retry_after: Optional[int] = None):
        super().__init__(
            f"Rate limit exceeded for {service_name}",
            status_code=HTTPStatus.TOO_MANY_REQUESTS,
            error_code="RATE_LIMIT_EXCEEDED",
            context=ErrorContext(
                details={
                    "service_name": service_name,
                    "limit": limit,
                    "window": window,
                    "retry_after": retry_after
                }
            )
        )


class CircuitBreakerOpen(ServiceException):
    """Raised when circuit breaker is open"""
    
    def __init__(self, service_name: str, failure_count: int, timeout: int):
        super().__init__(
            f"Circuit breaker open for {service_name}",
            status_code=HTTPStatus.SERVICE_UNAVAILABLE,
            error_code="CIRCUIT_BREAKER_OPEN",
            context=ErrorContext(
                details={
                    "service_name": service_name,
                    "failure_count": failure_count,
                    "timeout": timeout
                }
            )
        )


class ExternalServiceError(ServiceException):
    """Raised when external service call fails"""
    
    def __init__(
        self,
        service_name: str,
        message: str,
        status_code: int = HTTPStatus.BAD_GATEWAY,
        response_data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            f"External service error: {message}",
            status_code=status_code,
            error_code="EXTERNAL_SERVICE_ERROR",
            context=ErrorContext(
                details={
                    "service_name": service_name,
                    "response_data": response_data
                }
            )
        )


# Authentication & Authorization Exceptions
class AuthException(SACRA2Exception):
    """Base exception for authentication and authorization errors"""
    pass


class InvalidCredentials(AuthException):
    """Raised when credentials are invalid"""
    
    def __init__(self, message: str = "Invalid credentials"):
        super().__init__(
            message,
            status_code=HTTPStatus.UNAUTHORIZED,
            error_code="INVALID_CREDENTIALS"
        )


class TokenExpired(AuthException):
    """Raised when token has expired"""
    
    def __init__(self, token_type: str = "access"):
        super().__init__(
            f"{token_type.title()} token has expired",
            status_code=HTTPStatus.UNAUTHORIZED,
            error_code="TOKEN_EXPIRED",
            context=ErrorContext(
                details={"token_type": token_type}
            )
        )


class InsufficientPermissions(AuthException):
    """Raised when user lacks required permissions"""
    
    def __init__(self, required_permission: str, user_permissions: List[str]):
        super().__init__(
            f"Insufficient permissions. Required: {required_permission}",
            status_code=HTTPStatus.FORBIDDEN,
            error_code="INSUFFICIENT_PERMISSIONS",
            context=ErrorContext(
                details={
                    "required_permission": required_permission,
                    "user_permissions": user_permissions
                }
            )
        )


# AI Provider Exceptions
class AIProviderException(SACRA2Exception):
    """Base exception for AI provider errors"""
    pass


class ModelNotFound(AIProviderException):
    """Raised when AI model is not found"""
    
    def __init__(self, model_name: str, provider: str):
        super().__init__(
            f"Model '{model_name}' not found in provider '{provider}'",
            status_code=HTTPStatus.BAD_REQUEST,
            error_code="MODEL_NOT_FOUND",
            context=ErrorContext(
                details={
                    "model_name": model_name,
                    "provider": provider
                }
            )
        )


class TokenLimitExceeded(AIProviderException):
    """Raised when token limit is exceeded"""
    
    def __init__(self, requested_tokens: int, max_tokens: int, model_name: str):
        super().__init__(
            f"Token limit exceeded for model {model_name}",
            status_code=HTTPStatus.BAD_REQUEST,
            error_code="TOKEN_LIMIT_EXCEEDED",
            context=ErrorContext(
                details={
                    "requested_tokens": requested_tokens,
                    "max_tokens": max_tokens,
                    "model_name": model_name
                }
            )
        )


class ProviderQuotaExceeded(AIProviderException):
    """Raised when provider quota is exceeded"""
    
    def __init__(self, provider: str, quota_type: str, limit: int):
        super().__init__(
            f"{provider} quota exceeded: {quota_type}",
            status_code=HTTPStatus.TOO_MANY_REQUESTS,
            error_code="PROVIDER_QUOTA_EXCEEDED",
            context=ErrorContext(
                details={
                    "provider": provider,
                    "quota_type": quota_type,
                    "limit": limit
                }
            )
        )


# Evaluation Exceptions
class EvaluationException(SACRA2Exception):
    """Base exception for evaluation-related errors"""
    pass


class EvaluationNotFound(EvaluationException):
    """Raised when evaluation is not found"""
    
    def __init__(self, evaluation_id: str):
        super().__init__(
            f"Evaluation not found",
            status_code=HTTPStatus.NOT_FOUND,
            error_code="EVALUATION_NOT_FOUND",
            context=ErrorContext(
                field="evaluation_id",
                value=evaluation_id
            )
        )


class EvaluationInProgress(EvaluationException):
    """Raised when evaluation is already in progress"""
    
    def __init__(self, evaluation_id: str, current_status: str):
        super().__init__(
            f"Evaluation is already in progress",
            status_code=HTTPStatus.CONFLICT,
            error_code="EVALUATION_IN_PROGRESS",
            context=ErrorContext(
                details={
                    "evaluation_id": evaluation_id,
                    "current_status": current_status
                }
            )
        )


class ProbeExecutionError(EvaluationException):
    """Raised when probe execution fails"""
    
    def __init__(self, probe_id: str, model_id: str, error_details: Dict[str, Any]):
        super().__init__(
            f"Probe execution failed",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            error_code="PROBE_EXECUTION_ERROR",
            context=ErrorContext(
                details={
                    "probe_id": probe_id,
                    "model_id": model_id,
                    "error_details": error_details
                }
            )
        )


# Error Response Models
class ErrorResponse:
    """Standard error response model"""
    
    def __init__(
        self,
        error: SACRA2Exception,
        request_id: Optional[str] = None,
        correlation_id: Optional[str] = None
    ):
        self.error = {
            "message": error.message,
            "code": error.error_code,
            "status_code": error.status_code,
            "context": error.context.__dict__ if error.context else None,
            "details": error.details,
            "request_id": request_id,
            "correlation_id": correlation_id
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return self.error