"""
Type definitions and common types for SACRA2 backend.
Provides shared type aliases and utility types used across the system.
"""

from typing import TypeVar, Generic, Callable, Any, Dict, List, Optional, Union, Protocol
from uuid import UUID
from datetime import datetime
from enum import Enum
from dataclasses import dataclass


# Generic type variables
T = TypeVar('T')
U = TypeVar('U')
K = TypeVar('K')
V = TypeVar('V')


# Entity identifiers
EntityId = UUID
TenantId = UUID
UserId = UUID
EvaluationId = UUID
AssessmentId = UUID
ProjectId = UUID


# JSON-like structures
JSONDict = Dict[str, Any]
JSONArray = List[Any]
JSONValue = Union[str, int, float, bool, None, JSONDict, JSONArray]


# Common result types
class ResultStatus(Enum):
    """Result status enumeration"""
    SUCCESS = "success"
    FAILURE = "failure"
    PARTIAL = "partial"


@dataclass
class Result(Generic[T]):
    """Generic result wrapper"""
    value: Optional[T] = None
    error: Optional[Exception] = None
    status: ResultStatus = ResultStatus.SUCCESS
    metadata: Dict[str, Any] = None  # type: ignore[assignment]
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def is_success(self) -> bool:
        """Check if result is successful"""
        return self.status == ResultStatus.SUCCESS and self.error is None
    
    @property
    def is_failure(self) -> bool:
        """Check if result is failure"""
        return self.status == ResultStatus.FAILURE or self.error is not None
    
    @classmethod
    def success(cls, value: T, metadata: Optional[Dict[str, Any]] = None) -> 'Result[T]':
        """Create success result"""
        return cls(value=value, status=ResultStatus.SUCCESS, metadata=metadata or {})
    
    @classmethod
    def failure(cls, error: Exception, metadata: Optional[Dict[str, Any]] = None) -> 'Result[T]':
        """Create failure result"""
        return cls(error=error, status=ResultStatus.FAILURE, metadata=metadata or {})
    
    @classmethod
    def partial(cls, value: T, error: Optional[Exception] = None, metadata: Optional[Dict[str, Any]] = None) -> 'Result[T]':
        """Create partial result"""
        return cls(value=value, error=error, status=ResultStatus.PARTIAL, metadata=metadata or {})


# Pagination types
@dataclass
class PaginationParams:
    """Pagination parameters"""
    page: int = 1
    per_page: int = 20
    
    @property
    def offset(self) -> int:
        """Calculate offset"""
        return (self.page - 1) * self.per_page


@dataclass
class PaginatedResult(Generic[T]):
    """Paginated result wrapper"""
    items: List[T]
    total: int
    page: int
    per_page: int
    total_pages: int
    
    @classmethod
    def create(cls, items: List[T], total: int, params: PaginationParams) -> 'PaginatedResult[T]':
        """Create paginated result"""
        total_pages = max(1, (total + params.per_page - 1) // params.per_page)
        return cls(
            items=items,
            total=total,
            page=params.page,
            per_page=params.per_page,
            total_pages=total_pages
        )


# Sorting types
class SortDirection(Enum):
    """Sort direction enumeration"""
    ASC = "asc"
    DESC = "desc"


@dataclass
class SortParam:
    """Sort parameter"""
    field: str
    direction: SortDirection = SortDirection.ASC


# Filter types
@dataclass
class FilterParam:
    """Filter parameter"""
    field: str
    operator: str
    value: Any


# Audit types
@dataclass
class AuditInfo:
    """Audit information"""
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UserId] = None
    updated_by: Optional[UserId] = None
    version: int = 1
    deleted_at: Optional[datetime] = None
    deleted_by: Optional[str] = None


# Entity metadata used by domain models
@dataclass
class EntityMetadata:
    """Metadata for an entity instance"""
    id: EntityId
    version: int
    is_deleted: bool = False


# Event types
@dataclass
class EventMetadata:
    """Event metadata"""
    event_id: UUID
    event_type: str
    aggregate_id: EntityId
    aggregate_type: str
    timestamp: datetime
    correlation_id: Optional[UUID] = None
    causation_id: Optional[UUID] = None
    user_id: Optional[UserId] = None
    tenant_id: Optional[TenantId] = None


# Service protocols
class ServiceProtocol(Protocol):
    """Protocol for services"""
    
    async def execute(self, *args, **kwargs) -> Result[Any]:
        """Execute service operation"""
        ...


class RepositoryProtocol(Protocol[T]):
    """Protocol for repositories"""
    
    async def get_by_id(self, entity_id: EntityId) -> Result[T]:
        """Get entity by ID"""
        ...
    
    async def save(self, entity: T) -> Result[T]:
        """Save entity"""
        ...
    
    async def delete(self, entity_id: EntityId) -> Result[bool]:
        """Delete entity"""
        ...


# Cache types
@dataclass
class CacheConfig:
    """Cache configuration"""
    ttl: int
    key_prefix: Optional[str] = None
    tags: Optional[List[str]] = None


# Rate limiting types
@dataclass
class RateLimitConfig:
    """Rate limit configuration"""
    requests: int
    window: int  # seconds
    key: str
    identifier: Optional[str] = None


# Circuit breaker types
class CircuitBreakerState(Enum):
    """Circuit breaker state enumeration"""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration"""
    failure_threshold: int
    recovery_timeout: int  # seconds
    expected_exception: type
    success_threshold: int = 1


# Webhook types
@dataclass
class WebhookPayload:
    """Webhook payload"""
    event_type: str
    event_data: JSONDict
    timestamp: datetime
    signature: Optional[str] = None


# File types
@dataclass
class FileInfo:
    """File information"""
    filename: str
    content_type: str
    size: int
    checksum: str
    path: Optional[str] = None
    metadata: Optional[JSONDict] = None


# Configuration types
@dataclass
class ConfigOverride:
    """Configuration override"""
    key: str
    value: Any
    scope: str = "global"
    expires_at: Optional[datetime] = None


# Utility type aliases
AsyncFunction = Callable[..., Any]
SyncFunction = Callable[..., Any]
HandlerFunction = Callable[[Any], Any]
ValidatorFunction = Callable[[Any], Result[bool]]
MapperFunction = Callable[[T], U]
PredicateFunction = Callable[[T], bool]


# Domain-specific type aliases
ModelId = str
ProviderName = str
ProbeId = UUID
EvaluationStatus = str
AssessmentStatus = str
ScoreValue = float
ConfidenceScore = float  # 0.0 to 1.0


# API response types
@dataclass
class ApiResponse(Generic[T]):
    """Standard API response"""
    data: Optional[T] = None
    message: Optional[str] = None
    errors: Optional[List[str]] = None
    metadata: Optional[JSONDict] = None
    
    @classmethod
    def success(cls, data: T, message: Optional[str] = None, metadata: Optional[JSONDict] = None) -> 'ApiResponse[T]':
        """Create success response"""
        return cls(data=data, message=message, metadata=metadata)
    
    @classmethod
    def error(cls, message: str, errors: Optional[List[str]] = None, metadata: Optional[JSONDict] = None) -> 'ApiResponse[T]':
        """Create error response"""
        return cls(message=message, errors=errors, metadata=metadata)


# Security types
@dataclass
class TokenClaims:
    """JWT token claims"""
    sub: UserId
    exp: datetime
    iat: datetime
    iss: str
    aud: str
    scope: List[str]
    tenant_id: Optional[TenantId] = None
    permissions: Optional[List[str]] = None


# Feature flag types
@dataclass
class FeatureFlag:
    """Feature flag configuration"""
    name: str
    enabled: bool
    rollout_percentage: Optional[int] = None
    user_whitelist: Optional[List[UserId]] = None
    tenant_whitelist: Optional[List[TenantId]] = None


# Background job types
@dataclass
class JobConfig:
    """Background job configuration"""
    job_id: str
    job_type: str
    priority: int = 5
    max_retries: int = 3
    timeout: int = 300  # seconds
    queue_name: str = "default"
