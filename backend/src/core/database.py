"""
Synchronous database helpers for tests and FastAPI dependency.
Provides get_db generator yielding a SQLAlchemy Session bound to a given engine.
Used only in tests with SQLite via overrides in tests/conftest.py.
"""
from typing import Generator, Optional
from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker
from .config import settings

# In production we use async engine in infrastructure.database.
# Tests override this dependency to use the in-memory SQLite session from fixtures.

# Lazily initialized synchronous engine/session for legacy sync routes
_engine = None  # type: Optional[object]
_SessionLocal = None  # type: Optional[sessionmaker]


def _build_sync_database_url() -> str:
    """Build a synchronous (psycopg2) database URL from settings.

    Uses the single source of truth in settings.database.sync_url so that
    DATABASE_URL or discrete DB_* variables are respected consistently.
    """
    return settings.database.sync_url


def _ensure_engine_initialized() -> None:
    """Initialize synchronous SQLAlchemy engine and sessionmaker if needed."""
    global _engine, _SessionLocal
    if _engine is not None and _SessionLocal is not None:
        return
    db = settings.database
    _engine = create_engine(
        _build_sync_database_url(),
        pool_size=getattr(db, "pool_size", 20),
        max_overflow=getattr(db, "max_overflow", 30),
        pool_pre_ping=True,
        pool_recycle=getattr(db, "pool_timeout", 30),
        future=True,
    )
    _SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=_engine, class_=Session)


def get_db() -> Generator[Session, None, None]:
    """FastAPI dependency that yields a synchronous SQLAlchemy Session.

    Note:
    - This is intended for runtime usage of existing sync-ORM based routes.
    - Tests may override this dependency in fixtures to provide an in-memory SQLite session.
    """
    _ensure_engine_initialized()
    assert _SessionLocal is not None  # for type checking
    db = _SessionLocal()
    try:
        yield db
    finally:
        db.close()
