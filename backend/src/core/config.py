"""
Configuration management for SACRA2 backend.
Implements environment-based configuration with validation and type safety.
"""

import os
import json
from typing import Optional, List, Dict, Any
from pydantic import Field, field_validator, computed_field
from pydantic_settings import BaseSettings, SettingsConfigDict
from enum import Enum


class Environment(str, Enum):
    """Environment enumeration"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


class AppBaseSettings(BaseSettings):
    """Base settings to be inherited by all other settings classes."""
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )


class DatabaseConfig(AppBaseSettings):
    """Database configuration"""
    host: str = Field(default="localhost", env="DB_HOST")
    port: int = Field(default=5432, env="DB_PORT")
    username: str = Field(default="sacra2", env="DB_USERNAME")
    password: str = Field(default="sacra2", env="DB_PASSWORD")
    database: str = Field(default="sacra2", env="DB_NAME")
    # Optional consolidated URL for convenience and docker-compose compatibility
    database_url: Optional[str] = Field(default=None, env="DATABASE_URL")
    ssl_mode: str = Field(default="prefer", env="DB_SSL_MODE")
    pool_size: int = Field(default=20, env="DB_POOL_SIZE")
    max_overflow: int = Field(default=30, env="DB_MAX_OVERFLOW")
    pool_timeout: int = Field(default=30, env="DB_POOL_TIMEOUT")
    
    @property
    def url(self) -> str:
        """Get async database URL.

        Precedence:
        1) If DATABASE_URL is set, return it as-is.
        2) Otherwise, build from discrete DB_* variables using asyncpg driver.
        """
        if self.database_url:
            return self.database_url
        # Fall back to live environment to avoid nested BaseSettings limitations
        user = os.getenv("DB_USERNAME", self.username)
        pwd = os.getenv("DB_PASSWORD", self.password)
        host = os.getenv("DB_HOST", self.host)
        port = os.getenv("DB_PORT", str(self.port))
        name = os.getenv("DB_NAME", self.database)
        return (
            f"postgresql+asyncpg://{user}:{pwd}"
            f"@{host}:{port}/{name}"
        )

    @property
    def sync_url(self) -> str:
        """Get synchronous (psycopg2) database URL derived from url.

        This converts any '+asyncpg' driver to '+psycopg2'. If the URL is not
        PostgreSQL or does not contain '+asyncpg', it is returned unchanged.
        """
        u = self.url
        if "+asyncpg" in u:
            return u.replace("+asyncpg", "+psycopg2", 1)
        # Support plain 'postgresql://' (no driver specified)
        if u.startswith("postgresql://"):
            return u.replace("postgresql://", "postgresql+psycopg2://", 1)
        return u


class RedisConfig(AppBaseSettings):
    """Redis configuration"""
    host: str = Field(default="localhost", env="REDIS_HOST")
    port: int = Field(default=6379, env="REDIS_PORT")
    password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    database: int = Field(default=0, env="REDIS_DB")
    ssl: bool = Field(default=False, env="REDIS_SSL")
    max_connections: int = Field(default=100, env="REDIS_MAX_CONNECTIONS")
    
    @property
    def url(self) -> str:
        """Get Redis URL"""
        protocol = "rediss" if self.ssl else "redis"
        auth = f":{self.password}@" if self.password else ""
        return f"{protocol}://{auth}{self.host}:{self.port}/{self.database}"


class SecurityConfig(AppBaseSettings):
    """Security configuration"""
    secret_key: str = Field(default="test-secret-key", env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    refresh_token_expire_days: int = Field(default=7, env="REFRESH_TOKEN_EXPIRE_DAYS")
    password_hash_algorithm: str = Field(default="bcrypt", env="PASSWORD_HASH_ALGORITHM")
    cors_origins: str = Field(default="*", env="CORS_ORIGINS")
    cors_allow_credentials: bool = Field(default=True, env="CORS_ALLOW_CREDENTIALS")
    cors_allow_methods: str = Field(default="*", env="CORS_ALLOW_METHODS")
    cors_allow_headers: str = Field(default="*", env="CORS_ALLOW_HEADERS")
    
    @field_validator("secret_key", mode='before')
    @classmethod
    def validate_secret_key(cls, v: Any):
        if not v and os.getenv("ENVIRONMENT") != "testing":
            raise ValueError("SECRET_KEY is required for non-testing environments")
        return v or "test-secret-key"

    @computed_field
    @property
    def cors_origins_list(self) -> List[str]:
        """Parse the CORS_ORIGINS string into a list."""
        if not self.cors_origins:
            return []
        if self.cors_origins == "*":
            return ["*"]
        return [origin.strip() for origin in self.cors_origins.split(",")]

    @computed_field
    @property
    def cors_allow_methods_list(self) -> List[str]:
        """Parse the CORS_ALLOW_METHODS string into a list."""
        if not self.cors_allow_methods:
            return []
        if self.cors_allow_methods == "*":
            return ["*"]
        return [method.strip() for method in self.cors_allow_methods.split(",")]

    @computed_field
    @property
    def cors_allow_headers_list(self) -> List[str]:
        """Parse the CORS_ALLOW_HEADERS string into a list."""
        if not self.cors_allow_headers:
            return []
        if self.cors_allow_headers == "*":
            return ["*"]
        return [header.strip() for header in self.cors_allow_headers.split(",")]


class OpenAIConfig(AppBaseSettings):
    """OpenAI configuration"""
    api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    organization: Optional[str] = Field(default=None, env="OPENAI_ORGANIZATION")
    base_url: str = Field(default="https://api.openai.com/v1", env="OPENAI_BASE_URL")
    timeout: int = Field(default=30, env="OPENAI_TIMEOUT")
    max_retries: int = Field(default=3, env="OPENAI_MAX_RETRIES")
    rate_limit_per_minute: int = Field(default=60, env="OPENAI_RATE_LIMIT")


class AnthropicConfig(AppBaseSettings):
    """Anthropic configuration"""
    api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    base_url: str = Field(default="https://api.anthropic.com", env="ANTHROPIC_BASE_URL")
    timeout: int = Field(default=30, env="ANTHROPIC_TIMEOUT")
    max_retries: int = Field(default=3, env="ANTHROPIC_MAX_RETRIES")
    rate_limit_per_minute: int = Field(default=60, env="ANTHROPIC_RATE_LIMIT")


class AIProviderConfig(AppBaseSettings):
    """AI Provider configuration"""
    openai: OpenAIConfig = Field(default_factory=OpenAIConfig)
    anthropic: AnthropicConfig = Field(default_factory=AnthropicConfig)
    default_provider: str = Field(default="openai", env="DEFAULT_AI_PROVIDER")
    cache_ttl: int = Field(default=3600, env="AI_CACHE_TTL")
    max_cache_size: int = Field(default=1000, env="AI_MAX_CACHE_SIZE")


class MonitoringConfig(AppBaseSettings):
    """Monitoring and observability configuration"""
    sentry_dsn: Optional[str] = Field(default=None, env="SENTRY_DSN")
    prometheus_port: int = Field(default=9090, env="PROMETHEUS_PORT")
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    tracing_enabled: bool = Field(default=True, env="TRACING_ENABLED")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")


class CeleryConfig(AppBaseSettings):
    """Celery configuration"""
    broker_url: str = Field(default="redis://localhost:6379/0", env="CELERY_BROKER_URL")
    result_backend: str = Field(default="redis://localhost:6379/0", env="CELERY_RESULT_BACKEND")
    task_serializer: str = Field(default="json", env="CELERY_TASK_SERIALIZER")
    accept_content: List[str] = Field(default=["json"], env="CELERY_ACCEPT_CONTENT")
    result_serializer: str = Field(default="json", env="CELERY_RESULT_SERIALIZER")
    timezone: str = Field(default="UTC", env="CELERY_TIMEZONE")
    enable_utc: bool = Field(default=True, env="CELERY_ENABLE_UTC")
    task_track_started: bool = Field(default=True, env="CELERY_TASK_TRACK_STARTED")
    task_time_limit: int = Field(default=300, env="CELERY_TASK_TIME_LIMIT")
    task_soft_time_limit: int = Field(default=240, env="CELERY_TASK_SOFT_TIME_LIMIT")


class EventStoreConfig(AppBaseSettings):
    """Event store configuration"""
    connection_string: str = Field(default="postgresql+asyncpg://localhost:5432/sacra2_events", env="EVENTSTORE_CONNECTION_STRING")
    stream_prefix: str = Field(default="sacra2", env="EVENTSTORE_STREAM_PREFIX")
    max_append_size: int = Field(default=1000, env="EVENTSTORE_MAX_APPEND_SIZE")
    snapshot_frequency: int = Field(default=100, env="EVENTSTORE_SNAPSHOT_FREQUENCY")


class Settings(AppBaseSettings):

    """Main application settings"""
    
    # Environment
    environment: Environment = Field(default=Environment.DEVELOPMENT, env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")
    testing: bool = Field(default=False, env="TESTING")
    
    # Application
    app_name: str = Field(default="SACRA2 Backend", env="APP_NAME")
    app_version: str = Field(default="2.0.0", env="APP_VERSION")
    api_v1_prefix: str = Field(default="/api/v1", env="API_V1_PREFIX")
    api_v2_prefix: str = Field(default="/api/v2", env="API_V2_PREFIX")
    
    # Server
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    reload: bool = Field(default=False, env="RELOAD")
    workers: int = Field(default=1, env="WORKERS")
    
    # Configuration sections
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    redis: RedisConfig = Field(default_factory=RedisConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    ai_provider: AIProviderConfig = Field(default_factory=AIProviderConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    celery: CeleryConfig = Field(default_factory=CeleryConfig)
    event_store: EventStoreConfig = Field(default_factory=EventStoreConfig)

    # Compatibility helpers
    @computed_field
    @property
    def DATABASE_URL(self) -> str:
        """Expose async database URL for Alembic and tooling compatibility."""
        return self.database.url

settings = Settings()