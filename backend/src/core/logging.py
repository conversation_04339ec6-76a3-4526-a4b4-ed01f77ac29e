"""
Advanced logging configuration for SACRA2 backend.
Provides structured logging with correlation IDs and JSON formatting.
"""

import logging
import json
import uuid
import time
from typing import Any, Dict, Optional, Union
from datetime import datetime
import os
import sys
from contextvars import ContextVar
from pathlib import Path


# Context variable for correlation ID
_correlation_id: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)


class StructuredFormatter(logging.Formatter):
    """JSON formatter for structured logging"""
    
    def __init__(self, service_name: str = "sacra2-backend", environment: str = "development"):
        super().__init__()
        self.service_name = service_name
        self.environment = environment
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        # Get correlation ID from context
        correlation_id = _correlation_id.get()
        if not correlation_id:
            correlation_id = str(uuid.uuid4())
        
        # Build log entry
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "service": self.service_name,
            "environment": self.environment,
            "correlation_id": correlation_id,
            "thread": record.threadName,
            "process": record.processName,
            "file": record.filename,
            "line": record.lineno,
            "function": record.funcName
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        # Add performance metrics
        if hasattr(record, 'duration'):
            log_entry["duration_ms"] = record.duration
        
        # Add request/response info
        if hasattr(record, 'request_id'):
            log_entry["request_id"] = record.request_id
        
        if hasattr(record, 'user_id'):
            log_entry["user_id"] = record.user_id
        
        if hasattr(record, 'endpoint'):
            log_entry["endpoint"] = record.endpoint
        
        if hasattr(record, 'method'):
            log_entry["method"] = record.method
        
        if hasattr(record, 'status_code'):
            log_entry["status_code"] = record.status_code
        
        # Add custom metrics
        for key, value in record.__dict__.items():
            if key.startswith('metric_') and key not in ['metric_name']:
                log_entry[key] = value
        
        return json.dumps(log_entry, ensure_ascii=False)


class HumanReadableFormatter(logging.Formatter):
    """Human-readable formatter for development"""
    
    def __init__(self):
        super().__init__(
            fmt='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )


class CorrelationIdFilter(logging.Filter):
    """Filter to add correlation ID to log records"""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add correlation ID to log record"""
        correlation_id = _correlation_id.get()
        if correlation_id:
            record.correlation_id = correlation_id
        else:
            record.correlation_id = str(uuid.uuid4())
        return True


class PerformanceFilter(logging.Filter):
    """Filter to add performance metrics to log records"""
    
    def __init__(self):
        super().__init__()
        self.start_times: Dict[str, float] = {}
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add performance metrics"""
        if hasattr(record, 'start_time'):
            record.duration = int((time.time() - record.start_time) * 1000)
        return True


class SecurityFilter(logging.Filter):
    """Filter to sanitize sensitive information from logs"""
    
    SENSITIVE_FIELDS = {
        'password', 'token', 'secret', 'key', 'authorization',
        'api_key', 'access_token', 'refresh_token', 'jwt'
    }
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Sanitize sensitive information"""
        if hasattr(record, 'extra_fields'):
            self._sanitize_dict(record.extra_fields)
        
        if hasattr(record, 'args'):
            record.args = self._sanitize_args(record.args)
        
        return True
    
    def _sanitize_dict(self, data: Dict[str, Any]) -> None:
        """Sanitize dictionary"""
        for key, value in list(data.items()):
            if any(sensitive in str(key).lower() for sensitive in self.SENSITIVE_FIELDS):
                data[key] = '***REDACTED***'
            elif isinstance(value, dict):
                self._sanitize_dict(value)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        self._sanitize_dict(item)
    
    def _sanitize_args(self, args: tuple) -> tuple:
        """Sanitize arguments"""
        sanitized = []
        for arg in args:
            if isinstance(arg, dict):
                self._sanitize_dict(arg)
                sanitized.append(arg)
            else:
                sanitized.append(arg)
        return tuple(sanitized)


class ContextLogger:
    """Logger with context management"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def _log_with_context(self, level: int, msg: str, **kwargs):
        """Log with context"""
        extra = kwargs.pop('extra', {})
        extra.update(kwargs)
        
        if 'start_time' not in extra:
            extra['start_time'] = time.time()
        
        self.logger.log(level, msg, extra={'extra_fields': extra})
    
    def debug(self, msg: str, **kwargs):
        """Debug log with context"""
        self._log_with_context(logging.DEBUG, msg, **kwargs)
    
    def info(self, msg: str, **kwargs):
        """Info log with context"""
        self._log_with_context(logging.INFO, msg, **kwargs)
    
    def warning(self, msg: str, **kwargs):
        """Warning log with context"""
        self._log_with_context(logging.WARNING, msg, **kwargs)
    
    def error(self, msg: str, **kwargs):
        """Error log with context"""
        self._log_with_context(logging.ERROR, msg, **kwargs)
    
    def critical(self, msg: str, **kwargs):
        """Critical log with context"""
        self._log_with_context(logging.CRITICAL, msg, **kwargs)
    
    def exception(self, msg: str, **kwargs):
        """Exception log with context"""
        self.logger.exception(msg, extra={'extra_fields': kwargs})


class LoggingManager:
    """Centralized logging manager"""
    
    def __init__(self):
        self.loggers: Dict[str, ContextLogger] = {}
        self.is_configured = False
    
    def configure_logging(
        self,
        level: str = "INFO",
        service_name: str = "sacra2-backend",
        environment: str = "development",
        log_file: Optional[str] = None,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5,
        enable_console: bool = True,
        enable_file: bool = True
    ):
        """Configure logging system"""
        
        # Create logs directory if needed
        if log_file:
            log_dir = Path(log_file).parent
            log_dir.mkdir(parents=True, exist_ok=True)
        
        # Set root logger level
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, level.upper()))
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Add correlation ID filter
        correlation_filter = CorrelationIdFilter()
        
        # Add security filter
        security_filter = SecurityFilter()
        
        # Add performance filter
        performance_filter = PerformanceFilter()
        
        # Console handler
        if enable_console:
            console_handler = logging.StreamHandler(sys.stdout)
            
            if environment == "development":
                console_handler.setFormatter(HumanReadableFormatter())
            else:
                console_handler.setFormatter(StructuredFormatter(service_name, environment))
            
            console_handler.addFilter(correlation_filter)
            console_handler.addFilter(security_filter)
            console_handler.addFilter(performance_filter)
            root_logger.addHandler(console_handler)
        
        # File handler
        if enable_file and log_file:
            from logging.handlers import RotatingFileHandler
            
            file_handler = RotatingFileHandler(
                log_file,
                maxBytes=max_file_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            
            file_handler.setFormatter(StructuredFormatter(service_name, environment))
            file_handler.addFilter(correlation_filter)
            file_handler.addFilter(security_filter)
            file_handler.addFilter(performance_filter)
            root_logger.addHandler(file_handler)
        
        # Configure third-party loggers
        self._configure_third_party_loggers()
        
        self.is_configured = True
    
    def _configure_third_party_loggers(self):
        """Configure log levels for third-party libraries"""
        third_party_configs = {
            'urllib3': logging.WARNING,
            'requests': logging.WARNING,
            'botocore': logging.WARNING,
            'boto3': logging.WARNING,
            'sqlalchemy': logging.WARNING,
            'alembic': logging.WARNING,
            'uvicorn': logging.INFO,
            'fastapi': logging.INFO,
            'celery': logging.INFO,
            'redis': logging.WARNING,
            'asyncio': logging.WARNING
        }
        
        for logger_name, level in third_party_configs.items():
            logging.getLogger(logger_name).setLevel(level)
    
    def get_logger(self, name: str) -> ContextLogger:
        """Get context logger for module"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = ContextLogger(logger)
        
        return self.loggers[name]


# Global logging manager
logging_manager = LoggingManager()


def get_logger(name: str) -> ContextLogger:
    """Get logger for module"""
    return logging_manager.get_logger(name)


def configure_logging(**kwargs):
    """Configure global logging"""
    logging_manager.configure_logging(**kwargs)


# Context management utilities
class LogContext:
    """Context manager for correlation ID"""
    
    def __init__(self, correlation_id: Optional[str] = None):
        self.correlation_id = correlation_id or str(uuid.uuid4())
        self.token = None
    
    def __enter__(self):
        """Enter context"""
        self.token = _correlation_id.set(self.correlation_id)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context"""
        if self.token:
            _correlation_id.reset(self.token)


def with_correlation_id(correlation_id: Optional[str] = None):
    """Decorator to set correlation ID for function execution"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            correlation_id_final = correlation_id or str(uuid.uuid4())
            with LogContext(correlation_id_final):
                return func(*args, **kwargs)
        return wrapper
    return decorator


# Request/response logging utilities
def log_request(
    logger: ContextLogger,
    method: str,
    url: str,
    headers: Optional[Dict[str, str]] = None,
    body: Optional[Any] = None,
    user_id: Optional[str] = None
):
    """Log HTTP request"""
    logger.info(
        f"HTTP {method} {url}",
        method=method,
        url=url,
        headers=headers,
        body=body,
        user_id=user_id,
        type="request"
    )


def log_response(
    logger: ContextLogger,
    method: str,
    url: str,
    status_code: int,
    duration: float,
    headers: Optional[Dict[str, str]] = None,
    body: Optional[Any] = None
):
    """Log HTTP response"""
    logger.info(
        f"HTTP {method} {url} - {status_code}",
        method=method,
        url=url,
        status_code=status_code,
        duration=duration,
        headers=headers,
        body=body,
        type="response"
    )


# Performance profiling utilities
class PerformanceTimer:
    """Context manager for performance timing"""
    
    def __init__(self, logger: ContextLogger, operation: str):
        self.logger = logger
        self.operation = operation
        self.start_time = None
    
    def __enter__(self):
        """Start timer"""
        self.start_time = time.time()
        self.logger.info(f"Starting {self.operation}", type="timer_start")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Stop timer and log"""
        duration = time.time() - self.start_time
        if exc_type is None:
            self.logger.info(
                f"Completed {self.operation}",
                duration=duration * 1000,  # Convert to milliseconds
                type="timer_end"
            )
        else:
            self.logger.error(
                f"Failed {self.operation}",
                duration=duration * 1000,
                error=str(exc_val),
                type="timer_error"
            )


def time_operation(logger: ContextLogger, operation: str):
    """Decorator to time operations"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with PerformanceTimer(logger, operation):
                return func(*args, **kwargs)
        return wrapper
    return decorator

