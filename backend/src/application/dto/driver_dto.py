"""
Data Transfer Objects for Driver and Provider management.
"""

import uuid
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator, root_validator


class DriverBaseDTO(BaseModel):
    """Base DTO for Driver."""
    name: str = Field(..., min_length=1, max_length=100, description="Driver name")
    type: str = Field(..., min_length=1, max_length=50, description="Driver type")
    description: Optional[str] = Field(None, max_length=500, description="Driver description")
    default_config: Optional[Dict[str, Any]] = Field(None, description="Default configuration")
    active: bool = Field(True, description="Whether driver is active")
    
    @validator('type')
    def validate_type(cls, v):
        """Validate driver type."""
        valid_types = ['litellm', 'openai', 'openrouter', 'vertex', 'bedrock', 'azure', 'anthropic', 'groq', 'huggingface']
        if v not in valid_types:
            raise ValueError(f"Invalid driver type. Must be one of: {', '.join(valid_types)}")
        return v


class DriverCreateDTO(DriverBaseDTO):
    """DTO for creating a driver."""
    pass


class DriverUpdateDTO(BaseModel):
    """DTO for updating a driver."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    type: Optional[str] = Field(None, min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=500)
    default_config: Optional[Dict[str, Any]] = None
    active: Optional[bool] = None
    
    @validator('type')
    def validate_type(cls, v):
        """Validate driver type."""
        if v is not None:
            valid_types = ['litellm', 'openai', 'openrouter', 'vertex', 'bedrock', 'azure', 'anthropic', 'groq', 'huggingface']
            if v not in valid_types:
                raise ValueError(f"Invalid driver type. Must be one of: {', '.join(valid_types)}")
        return v


class DriverResponseDTO(DriverBaseDTO):
    """DTO for driver response."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    version: int
    provider_count: int = 0
    
    class Config:
        from_attributes = True


class ProviderBaseDTO(BaseModel):
    """Base DTO for Provider."""
    name: str = Field(..., min_length=1, max_length=100, description="Provider name")
    url: Optional[str] = Field(None, max_length=500, description="Provider URL")
    api_key: Optional[str] = Field(None, max_length=1000, description="Provider API key")
    max_concurrent_requests: int = Field(10, gt=0, description="Maximum concurrent requests")
    driver_id: uuid.UUID = Field(..., description="Driver ID")


class ProviderCreateDTO(ProviderBaseDTO):
    """DTO for creating a provider."""
    pass


class ProviderUpdateDTO(BaseModel):
    """DTO for updating a provider."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    url: Optional[str] = Field(None, max_length=500)
    api_key: Optional[str] = Field(None, max_length=1000)
    max_concurrent_requests: Optional[int] = Field(None, gt=0)
    driver_id: Optional[uuid.UUID] = None


class ProviderResponseDTO(ProviderBaseDTO):
    """DTO for provider response."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    version: int
    api_key: Optional[str] = Field(None, description="Masked API key")  # Always masked
    driver_name: Optional[str] = None
    model_count: int = 0
    
    class Config:
        from_attributes = True


class LLMModelBaseDTO(BaseModel):
    """Base DTO for LLM Model."""
    name: str = Field(..., min_length=1, max_length=100, description="Model name")
    version: Optional[str] = Field(None, max_length=50, description="Model version")
    description: Optional[str] = Field(None, max_length=500, description="Model description")
    requests_per_minute: Optional[int] = Field(None, gt=0, description="Requests per minute limit")
    tokens_per_minute: Optional[int] = Field(None, gt=0, description="Tokens per minute limit")
    max_tokens: Optional[int] = Field(None, gt=0, description="Maximum tokens")
    supports_functions: bool = Field(False, description="Supports function calling")
    supports_vision: bool = Field(False, description="Supports vision/images")
    provider_id: uuid.UUID = Field(..., description="Provider ID")
    active: bool = Field(True, description="Whether model is active")


class LLMModelCreateDTO(LLMModelBaseDTO):
    """DTO for creating an LLM model."""
    pass


class LLMModelUpdateDTO(BaseModel):
    """DTO for updating an LLM model."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    version: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = Field(None, max_length=500)
    requests_per_minute: Optional[int] = Field(None, gt=0)
    tokens_per_minute: Optional[int] = Field(None, gt=0)
    max_tokens: Optional[int] = Field(None, gt=0)
    supports_functions: Optional[bool] = None
    supports_vision: Optional[bool] = None
    provider_id: Optional[uuid.UUID] = None
    active: Optional[bool] = None


class LLMModelResponseDTO(LLMModelBaseDTO):
    """DTO for LLM model response."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    full_name: str
    provider_name: Optional[str] = None
    default_params: Dict[str, str] = Field(default_factory=dict)
    
    class Config:
        from_attributes = True


class ModelDefaultParamBaseDTO(BaseModel):
    """Base DTO for Model Default Parameter."""
    key: str = Field(..., min_length=1, max_length=100, description="Parameter key")
    value: str = Field(..., min_length=1, max_length=1000, description="Parameter value")
    param_type: str = Field('string', description="Parameter type")
    description: Optional[str] = Field(None, max_length=500, description="Parameter description")
    model_id: uuid.UUID = Field(..., description="Model ID")
    
    @validator('param_type')
    def validate_param_type(cls, v):
        """Validate parameter type."""
        valid_types = ['string', 'integer', 'float', 'boolean', 'json']
        if v not in valid_types:
            raise ValueError(f"Invalid parameter type. Must be one of: {', '.join(valid_types)}")
        return v


class ModelDefaultParamCreateDTO(ModelDefaultParamBaseDTO):
    """DTO for creating a model default parameter."""
    pass


class ModelDefaultParamUpdateDTO(BaseModel):
    """DTO for updating a model default parameter."""
    key: Optional[str] = Field(None, min_length=1, max_length=100)
    value: Optional[str] = Field(None, min_length=1, max_length=1000)
    param_type: Optional[str] = None
    description: Optional[str] = Field(None, max_length=500)
    model_id: Optional[uuid.UUID] = None
    
    @validator('param_type')
    def validate_param_type(cls, v):
        """Validate parameter type."""
        if v is not None:
            valid_types = ['string', 'integer', 'float', 'boolean', 'json']
            if v not in valid_types:
                raise ValueError(f"Invalid parameter type. Must be one of: {', '.join(valid_types)}")
        return v


class ModelDefaultParamResponseDTO(ModelDefaultParamBaseDTO):
    """DTO for model default parameter response."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    version: int
    typed_value: Any = Field(..., description="Value with correct type conversion")
    model_name: Optional[str] = None
    
    class Config:
        from_attributes = True


class TenantProviderConfigBaseDTO(BaseModel):
    """Base DTO for Tenant Provider Configuration."""
    tenant_id: uuid.UUID = Field(..., description="Tenant ID")
    provider_id: uuid.UUID = Field(..., description="Provider ID")
    api_key: str = Field(..., min_length=1, max_length=1000, description="Tenant-specific API key")
    max_concurrent_requests: Optional[int] = Field(None, gt=0, description="Override max concurrent requests")
    enabled: bool = Field(True, description="Whether configuration is enabled")
    config: Optional[Dict[str, Any]] = Field(None, description="Additional configuration")


class TenantProviderConfigCreateDTO(TenantProviderConfigBaseDTO):
    """DTO for creating tenant provider configuration."""
    pass


class TenantProviderConfigUpdateDTO(BaseModel):
    """DTO for updating tenant provider configuration."""
    api_key: Optional[str] = Field(None, min_length=1, max_length=1000)
    max_concurrent_requests: Optional[int] = Field(None, gt=0)
    enabled: Optional[bool] = None
    config: Optional[Dict[str, Any]] = None


class TenantProviderConfigResponseDTO(TenantProviderConfigBaseDTO):
    """DTO for tenant provider configuration response."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    version: int
    api_key: str = Field("***", description="Masked API key")  # Always masked
    provider_name: Optional[str] = None
    
    class Config:
        from_attributes = True


# List DTOs for pagination
class DriverListDTO(BaseModel):
    """DTO for driver list response."""
    items: List[DriverResponseDTO]
    total: int
    page: int
    per_page: int
    pages: int


class ProviderListDTO(BaseModel):
    """DTO for provider list response."""
    items: List[ProviderResponseDTO]
    total: int
    page: int
    per_page: int
    pages: int


class LLMModelListDTO(BaseModel):
    """DTO for LLM model list response."""
    items: List[LLMModelResponseDTO]
    total: int
    page: int
    per_page: int
    pages: int


class ModelDefaultParamListDTO(BaseModel):
    """DTO for model default parameter list response."""
    items: List[ModelDefaultParamResponseDTO]
    total: int
    page: int
    per_page: int
    pages: int


class TenantProviderConfigListDTO(BaseModel):
    """DTO for tenant provider configuration list response."""
    items: List[TenantProviderConfigResponseDTO]
    total: int
    page: int
    per_page: int
    pages: int