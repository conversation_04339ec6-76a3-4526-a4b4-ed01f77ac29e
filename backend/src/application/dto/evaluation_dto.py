"""
Data Transfer Objects for Evaluation framework entities.
"""

import uuid
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator


class ProbeBaseDTO(BaseModel):
    """Base DTO for Probe."""
    code: str = Field(..., min_length=1, max_length=100, description="Unique probe code")
    name: str = Field(..., min_length=1, max_length=200, description="Probe name")
    description: Optional[str] = Field(None, description="Probe description")
    params: Optional[Dict[str, Any]] = Field(None, description="Probe parameters")
    version: str = Field("1.0.0", min_length=1, max_length=50, description="Probe version")
    category: Optional[str] = Field(None, max_length=100, description="Probe category")
    tags: Optional[List[str]] = Field(None, description="Probe tags")
    active: bool = Field(True, description="Whether probe is active")


class ProbeCreateDTO(ProbeBaseDTO):
    """DTO for creating a probe."""
    pass


class ProbeUpdateDTO(BaseModel):
    """DTO for updating a probe."""
    code: Optional[str] = Field(None, min_length=1, max_length=100)
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    params: Optional[Dict[str, Any]] = None
    version: Optional[str] = Field(None, min_length=1, max_length=50)
    category: Optional[str] = Field(None, max_length=100)
    tags: Optional[List[str]] = None
    active: Optional[bool] = None


class ProbeResponseDTO(ProbeBaseDTO):
    """DTO for probe response."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    probeset_count: int = 0
    
    class Config:
        from_attributes = True
        populate_by_name = True


class ProbesetBaseDTO(BaseModel):
    """Base DTO for Probeset."""
    code: str = Field(..., min_length=1, max_length=100, description="Unique probeset code")
    name: str = Field(..., min_length=1, max_length=200, description="Probeset name")
    description: Optional[str] = Field(None, description="Probeset description")
    version: str = Field("1.0.0", min_length=1, max_length=50, description="Probeset version")
    scoring_method: str = Field("avg", description="Scoring method")
    scoring_config: Optional[Dict[str, Any]] = Field(None, description="Scoring configuration")
    category: Optional[str] = Field(None, max_length=100, description="Probeset category")
    tags: Optional[List[str]] = Field(None, description="Probeset tags")
    active: bool = Field(True, description="Whether probeset is active")
    
    @validator('scoring_method')
    def validate_scoring_method(cls, v):
        """Validate scoring method."""
        valid_methods = ['binary', 'avg', 'weighted', 'rule', 'custom']
        if v not in valid_methods:
            raise ValueError(f"Invalid scoring method. Must be one of: {', '.join(valid_methods)}")
        return v


class ProbesetCreateDTO(ProbesetBaseDTO):
    """DTO for creating a probeset."""
    pass


class ProbesetUpdateDTO(BaseModel):
    """DTO for updating a probeset."""
    code: Optional[str] = Field(None, min_length=1, max_length=100)
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    version: Optional[str] = Field(None, min_length=1, max_length=50)
    scoring_method: Optional[str] = None
    scoring_config: Optional[Dict[str, Any]] = None
    category: Optional[str] = Field(None, max_length=100)
    tags: Optional[List[str]] = None
    active: Optional[bool] = None
    
    @validator('scoring_method')
    def validate_scoring_method(cls, v):
        """Validate scoring method."""
        if v is not None:
            valid_methods = ['binary', 'avg', 'weighted', 'rule', 'custom']
            if v not in valid_methods:
                raise ValueError(f"Invalid scoring method. Must be one of: {', '.join(valid_methods)}")
        return v


class ProbesetResponseDTO(ProbesetBaseDTO):
    """DTO for probeset response."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    probe_count: int = 0
    
    class Config:
        from_attributes = True
        populate_by_name = True


class ProbesetProbeBaseDTO(BaseModel):
    """Base DTO for Probeset-Probe association."""
    probeset_id: uuid.UUID = Field(..., description="Probeset ID")
    probe_id: uuid.UUID = Field(..., description="Probe ID")
    order: int = Field(0, ge=0, description="Probe order in probeset")
    weight: float = Field(1.0, gt=0, description="Probe weight")
    override_params: Optional[Dict[str, Any]] = Field(None, description="Override parameters")
    enabled: bool = Field(True, description="Whether probe is enabled")


class ProbesetProbeCreateDTO(ProbesetProbeBaseDTO):
    """DTO for creating probeset-probe association.
    For creation, probeset_id is taken from the URL path, so make it optional in the payload.
    """
    probeset_id: Optional[uuid.UUID] = Field(None, description="Probeset ID (optional, taken from URL)")


class ProbesetProbeUpdateDTO(BaseModel):
    """DTO for updating probeset-probe association."""
    order: Optional[int] = Field(None, ge=0)
    weight: Optional[float] = Field(None, gt=0)
    override_params: Optional[Dict[str, Any]] = None
    enabled: Optional[bool] = None


class ProbesetProbeResponseDTO(ProbesetProbeBaseDTO):
    """DTO for probeset-probe association response."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    probeset_name: Optional[str] = None
    probe_name: Optional[str] = None
    probe_code: Optional[str] = None
    
    class Config:
        from_attributes = True


class CapabilityBaseDTO(BaseModel):
    """Base DTO for Capability."""
    code: str = Field(..., min_length=1, max_length=100, description="Unique capability code")
    name: str = Field(..., min_length=1, max_length=200, description="Capability name")
    description: Optional[str] = Field(None, description="Capability description")
    category: Optional[str] = Field(None, max_length=100, description="Capability category")
    severity: str = Field("medium", description="Capability severity")
    active: bool = Field(True, description="Whether capability is active")
    
    @validator('severity')
    def validate_severity(cls, v):
        """Validate severity level."""
        valid_severities = ['low', 'medium', 'high', 'critical']
        if v not in valid_severities:
            raise ValueError(f"Invalid severity. Must be one of: {', '.join(valid_severities)}")
        return v


class CapabilityCreateDTO(CapabilityBaseDTO):
    """DTO for creating a capability."""
    pass


class CapabilityUpdateDTO(BaseModel):
    """DTO for updating a capability."""
    code: Optional[str] = Field(None, min_length=1, max_length=100)
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    category: Optional[str] = Field(None, max_length=100)
    severity: Optional[str] = None
    active: Optional[bool] = None
    
    @validator('severity')
    def validate_severity(cls, v):
        """Validate severity level."""
        if v is not None:
            valid_severities = ['low', 'medium', 'high', 'critical']
            if v not in valid_severities:
                raise ValueError(f"Invalid severity. Must be one of: {', '.join(valid_severities)}")
        return v


class CapabilityResponseDTO(CapabilityBaseDTO):
    """DTO for capability response."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    version: int
    probeset_count: int = 0
    
    class Config:
        from_attributes = True


class CapabilitySetBaseDTO(BaseModel):
    """Base DTO for Capability-Probeset association."""
    capability_id: uuid.UUID = Field(..., description="Capability ID")
    probeset_id: uuid.UUID = Field(..., description="Probeset ID")
    weight: float = Field(1.0, ge=0, description="Probeset weight in capability")


class CapabilitySetCreateDTO(CapabilitySetBaseDTO):
    """DTO for creating capability-probeset association."""
    pass


class CapabilitySetUpdateDTO(BaseModel):
    """DTO for updating capability-probeset association."""
    weight: Optional[float] = Field(None, ge=0)


class CapabilitySetResponseDTO(CapabilitySetBaseDTO):
    """DTO for capability-probeset association response."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    capability_name: Optional[str] = None
    capability_code: Optional[str] = None
    probeset_name: Optional[str] = None
    probeset_code: Optional[str] = None
    
    class Config:
        from_attributes = True


class AssessmentBaseDTO(BaseModel):
    """Base DTO for Assessment."""
    code: str = Field(..., min_length=1, max_length=100, description="Unique assessment code")
    name: str = Field(..., min_length=1, max_length=200, description="Assessment name")
    description: Optional[str] = Field(None, description="Assessment description")
    version: str = Field("1.0.0", min_length=1, max_length=50, description="Assessment version")
    category: Optional[str] = Field(None, max_length=100, description="Assessment category")
    active: bool = Field(True, description="Whether assessment is active")


class AssessmentCreateDTO(AssessmentBaseDTO):
    """DTO for creating an assessment."""
    pass


class AssessmentUpdateDTO(BaseModel):
    """DTO for updating an assessment."""
    code: Optional[str] = Field(None, min_length=1, max_length=100)
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    version: Optional[str] = Field(None, min_length=1, max_length=50)
    category: Optional[str] = Field(None, max_length=100)
    active: Optional[bool] = None


class AssessmentResponseDTO(AssessmentBaseDTO):
    """DTO for assessment response."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    capability_count: int = 0
    
    class Config:
        from_attributes = True
        populate_by_name = True


class AssessmentCapabilityBaseDTO(BaseModel):
    """Base DTO for Assessment-Capability association."""
    assessment_id: uuid.UUID = Field(..., description="Assessment ID")
    capability_id: uuid.UUID = Field(..., description="Capability ID")
    weight: float = Field(1.0, ge=0, description="Capability weight in assessment")


class AssessmentCapabilityCreateDTO(AssessmentCapabilityBaseDTO):
    """DTO for creating assessment-capability association."""
    pass


class AssessmentCapabilityUpdateDTO(BaseModel):
    """DTO for updating assessment-capability association."""
    weight: Optional[float] = Field(None, ge=0)


class AssessmentCapabilityResponseDTO(AssessmentCapabilityBaseDTO):
    """DTO for assessment-capability association response."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    assessment_name: Optional[str] = None
    assessment_code: Optional[str] = None
    capability_name: Optional[str] = None
    capability_code: Optional[str] = None
    
    class Config:
        from_attributes = True


# List DTOs for pagination
class ProbeListDTO(BaseModel):
    """DTO for probe list response."""
    items: List[ProbeResponseDTO]
    total: int
    page: int
    per_page: int
    pages: int


class ProbesetListDTO(BaseModel):
    """DTO for probeset list response."""
    items: List[ProbesetResponseDTO]
    total: int
    page: int
    per_page: int
    pages: int


class CapabilityListDTO(BaseModel):
    """DTO for capability list response."""
    items: List[CapabilityResponseDTO]
    total: int
    page: int
    per_page: int
    pages: int


class AssessmentListDTO(BaseModel):
    """DTO for assessment list response."""
    items: List[AssessmentResponseDTO]
    total: int
    page: int
    per_page: int
    pages: int