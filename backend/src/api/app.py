"""
FastAPI application factory for SACRA2 backend.
Configures the application with middleware, routes, and dependencies.
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from fastapi.exception_handlers import request_validation_exception_handler
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
import time
import uuid

# Import from core and infrastructure
from ..core.config import settings
from ..core.logging import get_logger, LogContext, log_request, log_response, configure_logging
from ..core.exceptions import BaseAppException
from ..infrastructure.database import initialize_database, close_database

# Import routers
from .v1.routes import api_v1_router


logger = get_logger(__name__)


# Settings compatibility helpers (support both flat Settings and nested TestSettings)
def _get_attr(obj, path, default=None):
    """Safely get nested attributes by path list."""
    cur = obj
    for name in path:
        cur = getattr(cur, name, None)
        if cur is None:
            return default
    return cur


def _get_app_name(s) -> str:
    return _get_attr(s, ["app_name"]) or _get_attr(s, ["app", "app_name"]) or "SACRA2 Backend"


def _get_app_version(s) -> str:
    return _get_attr(s, ["app_version"]) or _get_attr(s, ["app", "app_version"]) or "0.0.0"


def _is_debug(s) -> bool:
    val = _get_attr(s, ["debug"])
    if val is None:
        val = _get_attr(s, ["app", "debug"])
    return bool(val)


def _is_testing(s) -> bool:
    val = _get_attr(s, ["testing"]) or _get_attr(s, ["app", "testing"])
    return bool(val)


def _get_environment_str(s) -> str:
    env = _get_attr(s, ["environment"]) or _get_attr(s, ["app", "environment"]) or "development"
    # Support Enum values and plain strings
    if hasattr(env, "value"):
        env = env.value
    return str(env)


def _get_log_level(s) -> str:
    return (
        _get_attr(s, ["monitoring", "log_level"]) or
        _get_attr(s, ["app", "log_level"]) or
        _get_attr(s, ["log_level"]) or
        "INFO"
    )


class CorrelationIdMiddleware(BaseHTTPMiddleware):
    """Middleware to add correlation ID to requests"""
    
    async def dispatch(self, request: Request, call_next):
        """Add correlation ID to request"""
        correlation_id = request.headers.get('X-Correlation-ID') or str(uuid.uuid4())
        
        with LogContext(correlation_id):
            response = await call_next(request)
            response.headers['X-Correlation-ID'] = correlation_id
            return response


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for request/response logging"""
    
    async def dispatch(self, request: Request, call_next):
        """Log request and response"""
        start_time = time.time()
        
        # Log request
        log_request(
            logger,
            method=request.method,
            url=str(request.url),
            headers=dict(request.headers),
            user_id=request.headers.get('X-User-ID')
        )
        
        # Process request
        response = await call_next(request)
        
        # Calculate duration
        duration = time.time() - start_time
        
        # Log response
        log_response(
            logger,
            method=request.method,
            url=str(request.url),
            status_code=response.status_code,
            duration=duration,
            headers=dict(response.headers)
        )
        
        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers"""
    
    async def dispatch(self, request: Request, call_next):
        """Add security headers to response"""
        response = await call_next(request)
        
        # Security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Simple rate limiting middleware"""
    
    def __init__(self, app, requests_per_minute: int = 60):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.requests = {}
    
    async def dispatch(self, request: Request, call_next):
        """Apply rate limiting"""
        client_ip = request.client.host
        current_time = time.time()
        
        # Clean old requests
        cutoff_time = current_time - 60
        self.requests = {
            ip: [req_time for req_time in times if req_time > cutoff_time]
            for ip, times in self.requests.items()
        }
        
        # Check rate limit
        if client_ip not in self.requests:
            self.requests[client_ip] = []
        
        if len(self.requests[client_ip]) >= self.requests_per_minute:
            return JSONResponse(
                status_code=429,
                content={"error": "Rate limit exceeded"}
            )
        
        # Record request
        self.requests[client_ip].append(current_time)
        
        return await call_next(request)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Manage application lifespan events"""
    # Configure logging with the correct settings for the environment
    app_settings = app.state.settings
    configure_logging(
        level=_get_log_level(app_settings),
        service_name=_get_app_name(app_settings),
        environment=_get_environment_str(app_settings),
        log_file=None,
        enable_file=not _is_testing(app_settings)  # Disable file logging in tests
    )
    
    logger.info("Starting SACRA2 backend application")
    logger.info("Initializing services...")
    # Skip heavy DB initialization in tests
    if not _is_testing(app_settings):
        await initialize_database()
    
    yield
    
    # Shutdown
    logger.info("Shutting down SACRA2 backend application")
    if not _is_testing(app_settings):
        await close_database()
    logger.info("Services shut down")


def create_app(custom_settings=None) -> FastAPI:
    """Create FastAPI application
    
    Args:
        custom_settings: Optional custom Settings instance
    """
    app_settings = custom_settings or settings
    
    # Create FastAPI instance
    app = FastAPI(
        title=_get_app_name(app_settings),
        description="SACRA2 Backend",
        version=_get_app_version(app_settings),
        docs_url="/docs" if _is_debug(app_settings) else None,
        redoc_url="/redoc" if _is_debug(app_settings) else None,
        openapi_url="/openapi.json" if _is_debug(app_settings) else None,
        lifespan=lifespan
    )
    
    # Store settings in app state
    app.state.settings = app_settings
    
    # Configure middleware
    _configure_middleware(app)
    
    # Configure exception handlers
    _configure_exception_handlers(app)
    
    # Configure routes
    _configure_routes(app)
    
    return app


def _configure_middleware(app: FastAPI) -> None:
    """Configure application middleware"""
    
    # CORS middleware
    sec = app.state.settings.security
    # Compute CORS lists with fallbacks for test settings
    origins_list = getattr(sec, "cors_origins_list", None)
    if origins_list is None:
        origins = getattr(sec, "cors_origins", "*")
        if origins == "*" or origins is None:
            origins_list = ["*"]
        else:
            origins_list = [o.strip() for o in str(origins).split(",") if o.strip()]
    methods_list = getattr(sec, "cors_allow_methods_list", None)
    if methods_list is None:
        methods = getattr(sec, "cors_allow_methods", "*")
        if methods == "*" or methods is None:
            methods_list = ["*"]
        else:
            methods_list = [m.strip() for m in str(methods).split(",") if m.strip()]
    headers_list = getattr(sec, "cors_allow_headers_list", None)
    if headers_list is None:
        headers = getattr(sec, "cors_allow_headers", "*")
        if headers == "*" or headers is None:
            headers_list = ["*"]
        else:
            headers_list = [h.strip() for h in str(headers).split(",") if h.strip()]
    allow_credentials = getattr(sec, "cors_allow_credentials", True)

    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins_list,
        allow_credentials=allow_credentials,
        allow_methods=methods_list,
        allow_headers=headers_list,
    )
    
    # Gzip middleware
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Trusted host middleware
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]
    )
    
    # Custom middleware
    app.add_middleware(CorrelationIdMiddleware)
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(SecurityHeadersMiddleware)
    
    # Rate limiting in production
    if not _is_debug(app.state.settings):
        app.add_middleware(RateLimitMiddleware, requests_per_minute=100)


def _configure_exception_handlers(app: FastAPI) -> None:
    """Configure exception handlers"""
    
    @app.exception_handler(BaseAppException)
    async def handle_base_exception(request: Request, exc: BaseAppException):
        """Handle custom application exceptions"""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "type": exc.__class__.__name__,
                    "message": str(exc),
                    "details": exc.details if hasattr(exc, 'details') else None
                }
            }
        )
    
    @app.exception_handler(RequestValidationError)
    async def handle_validation_error(request: Request, exc: RequestValidationError):
        """Handle request validation errors using FastAPI's default handler for safe JSON."""
        return await request_validation_exception_handler(request, exc)
    
    @app.exception_handler(Exception)
    async def handle_unexpected_error(request: Request, exc: Exception):
        """Handle unexpected errors"""
        logger.error(
            f"Unexpected error occurred",
            error=str(exc),
            path=str(request.url),
            method=request.method
        )
        
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "type": "InternalServerError",
                    "message": "An unexpected error occurred",
                    "details": None
                }
            }
        )


def _configure_routes(app: FastAPI) -> None:
    """Configure application routes"""
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        from ..infrastructure.database import check_database_health
        
        db_health = await check_database_health()
        
        return {
            "status": "ok",
            "timestamp": time.time(),
            "database": db_health
        }
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint"""
        return {
            "name": _get_app_name(app.state.settings),
            "version": _get_app_version(app.state.settings),
            "description": "SACRA2 Backend",
            "environment": _get_environment_str(app.state.settings)
        }
    
    # API routes
    app.include_router(
        api_v1_router,
        prefix="/api/v1",
        tags=["v1"]
    )
    
    # Metrics endpoint (if enabled)
    metrics_enabled = getattr(getattr(app.state.settings, "monitoring", None), "metrics_enabled", False)
    if metrics_enabled:
        @app.get("/metrics")
        async def metrics():
            """Prometheus metrics endpoint"""
            return {"status": "metrics_endpoint"}


# CLI commands for development
# Lazily create the app instance for uvicorn
_app_instance = None

def get_app():
    global _app_instance
    if _app_instance is None:
        _app_instance = create_app()
    return _app_instance


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.api.app:get_app",
        factory=True,
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )