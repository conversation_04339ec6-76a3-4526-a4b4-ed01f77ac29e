"""
FastAPI endpoints for Evaluation framework entities.
"""

import uuid
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from ....core.database import get_db
from ....infrastructure.persistence.models import (
    Probe, Probeset, ProbesetProbe, Capability, CapabilitySet, 
    Assessment, AssessmentCapability
)
from ....application.dto.evaluation_dto import (
    ProbeCreateDTO, ProbeUpdateDTO, ProbeResponseDTO, ProbeListDTO,
    ProbesetCreateDTO, ProbesetUpdateDTO, ProbesetResponseDTO, ProbesetListDTO,
    ProbesetProbeCreateDTO, ProbesetProbeUpdateDTO, ProbesetProbeResponseDTO,
    CapabilityCreateDTO, CapabilityUpdateDTO, CapabilityResponseDTO, CapabilityListDTO,
    CapabilitySetCreateDTO, CapabilitySetUpdateDTO, CapabilitySetResponseDTO,
    AssessmentCreateDTO, AssessmentUpdateDTO, AssessmentResponseDTO, AssessmentListDTO,
    AssessmentCapabilityCreateDTO, AssessmentCapabilityUpdateDTO, AssessmentCapabilityResponseDTO
)

router = APIRouter()


# Helper functions
def paginate_query(query, page: int = 1, per_page: int = 20):
    """Paginate SQLAlchemy query."""
    total = query.count()
    items = query.offset((page - 1) * per_page).limit(per_page).all()
    pages = (total + per_page - 1) // per_page
    return items, total, page, per_page, pages


def get_probe_or_404(db: Session, probe_id: uuid.UUID) -> Probe:
    """Get probe by ID or raise 404."""
    probe = db.query(Probe).filter(Probe.id == probe_id, Probe.deleted_at.is_(None)).first()
    if not probe:
        raise HTTPException(status_code=404, detail="Probe not found")
    return probe


def get_probeset_or_404(db: Session, probeset_id: uuid.UUID) -> Probeset:
    """Get probeset by ID or raise 404."""
    probeset = db.query(Probeset).filter(Probeset.id == probeset_id, Probeset.deleted_at.is_(None)).first()
    if not probeset:
        raise HTTPException(status_code=404, detail="Probeset not found")
    return probeset


def get_capability_or_404(db: Session, capability_id: uuid.UUID) -> Capability:
    """Get capability by ID or raise 404."""
    capability = db.query(Capability).filter(Capability.id == capability_id, Capability.deleted_at.is_(None)).first()
    if not capability:
        raise HTTPException(status_code=404, detail="Capability not found")
    return capability


def get_assessment_or_404(db: Session, assessment_id: uuid.UUID) -> Assessment:
    """Get assessment by ID or raise 404."""
    assessment = db.query(Assessment).filter(Assessment.id == assessment_id, Assessment.deleted_at.is_(None)).first()
    if not assessment:
        raise HTTPException(status_code=404, detail="Assessment not found")
    return assessment


# ===============================
# Probe endpoints
# ===============================

@router.get("/probes", response_model=ProbeListDTO, summary="List probes")
async def list_probes(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search in name and code"),
    category: Optional[str] = Query(None, description="Filter by category"),
    active_only: bool = Query(True, description="Show only active probes"),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    db: Session = Depends(get_db)
):
    """List all probes with pagination and filtering."""
    query = db.query(Probe).filter(Probe.deleted_at.is_(None))
    
    if active_only:
        query = query.filter(Probe.active == True)
    
    if category:
        query = query.filter(Probe.category == category)
    
    if search:
        query = query.filter(
            Probe.name.ilike(f"%{search}%") | 
            Probe.code.ilike(f"%{search}%")
        )
    
    if tags:
        for tag in tags:
            query = query.filter(Probe.tags.contains([tag]))
    
    query = query.order_by(Probe.name)
    items, total, page, per_page, pages = paginate_query(query, page, per_page)
    
    return ProbeListDTO(
        items=[ProbeResponseDTO.from_orm(probe) for probe in items],
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.post("/probes", response_model=ProbeResponseDTO, status_code=status.HTTP_201_CREATED, summary="Create probe")
async def create_probe(
    probe_data: ProbeCreateDTO,
    db: Session = Depends(get_db)
):
    """Create a new probe."""
    # Basic payload validation: avoid accepting only code+name
    if (
        getattr(probe_data, 'category', None) is None
        and getattr(probe_data, 'params', None) is None
        and getattr(probe_data, 'version', "1.0.0") == "1.0.0"
    ):
        # If code already exists, align with duplicate constraint error message
        existing = db.query(Probe).filter(
            Probe.code == probe_data.code,
            Probe.deleted_at.is_(None)
        ).first()
        if existing:
            raise HTTPException(status_code=400, detail="Probe with this code or name/version already exists")
        raise HTTPException(status_code=400, detail="Provide at least one of: params, category, or a non-default version")

    try:
        probe = Probe(**probe_data.dict())
        db.add(probe)
        db.commit()
        db.refresh(probe)
        return ProbeResponseDTO.from_orm(probe)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Probe with this code or name/version already exists")


@router.get("/probes/{probe_id}", response_model=ProbeResponseDTO, summary="Get probe")
async def get_probe(
    probe_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Get a specific probe by ID."""
    probe = get_probe_or_404(db, probe_id)
    return ProbeResponseDTO.from_orm(probe)


@router.put("/probes/{probe_id}", response_model=ProbeResponseDTO, summary="Update probe")
async def update_probe(
    probe_id: uuid.UUID,
    probe_data: ProbeUpdateDTO,
    db: Session = Depends(get_db)
):
    """Update a probe."""
    probe = get_probe_or_404(db, probe_id)
    
    # Update only provided fields
    update_data = probe_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(probe, field, value)
    
    try:
        probe.update_audit_fields()
        db.commit()
        db.refresh(probe)
        return ProbeResponseDTO.from_orm(probe)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Probe with this code or name/version already exists")


@router.delete("/probes/{probe_id}", status_code=status.HTTP_204_NO_CONTENT, summary="Delete probe")
async def delete_probe(
    probe_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Delete a probe (soft delete)."""
    probe = get_probe_or_404(db, probe_id)
    probe.soft_delete()
    db.commit()


# ===============================
# Probeset endpoints
# ===============================

@router.get("/probesets", response_model=ProbesetListDTO, summary="List probesets")
async def list_probesets(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search in name and code"),
    category: Optional[str] = Query(None, description="Filter by category"),
    scoring_method: Optional[str] = Query(None, description="Filter by scoring method"),
    active_only: bool = Query(True, description="Show only active probesets"),
    db: Session = Depends(get_db)
):
    """List all probesets with pagination and filtering."""
    query = db.query(Probeset).filter(Probeset.deleted_at.is_(None))
    
    if active_only:
        query = query.filter(Probeset.active == True)
    
    if category:
        query = query.filter(Probeset.category == category)
    
    if scoring_method:
        query = query.filter(Probeset.scoring_method == scoring_method)
    
    if search:
        query = query.filter(
            Probeset.name.ilike(f"%{search}%") | 
            Probeset.code.ilike(f"%{search}%")
        )
    
    query = query.order_by(Probeset.name)
    items, total, page, per_page, pages = paginate_query(query, page, per_page)
    
    return ProbesetListDTO(
        items=[ProbesetResponseDTO.from_orm(probeset) for probeset in items],
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.post("/probesets", response_model=ProbesetResponseDTO, status_code=status.HTTP_201_CREATED, summary="Create probeset")
async def create_probeset(
    probeset_data: ProbesetCreateDTO,
    db: Session = Depends(get_db)
):
    """Create a new probeset."""
    try:
        probeset = Probeset(**probeset_data.dict())
        db.add(probeset)
        db.commit()
        db.refresh(probeset)
        return ProbesetResponseDTO.from_orm(probeset)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Probeset with this code or name/version already exists")


@router.get("/probesets/{probeset_id}", response_model=ProbesetResponseDTO, summary="Get probeset")
async def get_probeset(
    probeset_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Get a specific probeset by ID."""
    probeset = get_probeset_or_404(db, probeset_id)
    return ProbesetResponseDTO.from_orm(probeset)


@router.put("/probesets/{probeset_id}", response_model=ProbesetResponseDTO, summary="Update probeset")
async def update_probeset(
    probeset_id: uuid.UUID,
    probeset_data: ProbesetUpdateDTO,
    db: Session = Depends(get_db)
):
    """Update a probeset."""
    probeset = get_probeset_or_404(db, probeset_id)
    
    # Update only provided fields
    update_data = probeset_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(probeset, field, value)
    
    try:
        probeset.update_audit_fields()
        db.commit()
        db.refresh(probeset)
        return ProbesetResponseDTO.from_orm(probeset)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Probeset with this code or name/version already exists")


@router.delete("/probesets/{probeset_id}", status_code=status.HTTP_204_NO_CONTENT, summary="Delete probeset")
async def delete_probeset(
    probeset_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Delete a probeset (soft delete)."""
    probeset = get_probeset_or_404(db, probeset_id)
    probeset.soft_delete()
    db.commit()


# ===============================
# Probeset-Probe association endpoints
# ===============================

@router.get("/probesets/{probeset_id}/probes", response_model=List[ProbesetProbeResponseDTO], summary="List probeset probes")
async def list_probeset_probes(
    probeset_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """List all probes in a probeset."""
    # Verify probeset exists
    get_probeset_or_404(db, probeset_id)
    
    probeset_probes = db.query(ProbesetProbe).filter(
        ProbesetProbe.probeset_id == probeset_id,
        ProbesetProbe.deleted_at.is_(None)
    ).order_by(ProbesetProbe.order).all()
    
    return [ProbesetProbeResponseDTO.from_orm(pp) for pp in probeset_probes]


@router.post("/probesets/{probeset_id}/probes", response_model=ProbesetProbeResponseDTO, status_code=status.HTTP_201_CREATED, summary="Add probe to probeset")
async def add_probe_to_probeset(
    probeset_id: uuid.UUID,
    probeset_probe_data: ProbesetProbeCreateDTO,
    db: Session = Depends(get_db)
):
    """Add a probe to a probeset."""
    # Verify probeset and probe exist
    get_probeset_or_404(db, probeset_id)
    get_probe_or_404(db, probeset_probe_data.probe_id)
    
    # Override probeset_id from URL
    probeset_probe_data.probeset_id = probeset_id
    
    try:
        probeset_probe = ProbesetProbe(**probeset_probe_data.dict())
        db.add(probeset_probe)
        db.commit()
        db.refresh(probeset_probe)
        return ProbesetProbeResponseDTO.from_orm(probeset_probe)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="This probe is already in the probeset")


@router.put("/probesets/{probeset_id}/probes/{probe_id}", response_model=ProbesetProbeResponseDTO, summary="Update probeset probe")
async def update_probeset_probe(
    probeset_id: uuid.UUID,
    probe_id: uuid.UUID,
    probeset_probe_data: ProbesetProbeUpdateDTO,
    db: Session = Depends(get_db)
):
    """Update a probe configuration in a probeset."""
    probeset_probe = db.query(ProbesetProbe).filter(
        ProbesetProbe.probeset_id == probeset_id,
        ProbesetProbe.probe_id == probe_id,
        ProbesetProbe.deleted_at.is_(None)
    ).first()
    
    if not probeset_probe:
        raise HTTPException(status_code=404, detail="Probe not found in probeset")
    
    # Update only provided fields
    update_data = probeset_probe_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(probeset_probe, field, value)
    
    probeset_probe.update_audit_fields()
    db.commit()
    db.refresh(probeset_probe)
    return ProbesetProbeResponseDTO.from_orm(probeset_probe)


@router.delete("/probesets/{probeset_id}/probes/{probe_id}", status_code=status.HTTP_204_NO_CONTENT, summary="Remove probe from probeset")
async def remove_probe_from_probeset(
    probeset_id: uuid.UUID,
    probe_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Remove a probe from a probeset."""
    probeset_probe = db.query(ProbesetProbe).filter(
        ProbesetProbe.probeset_id == probeset_id,
        ProbesetProbe.probe_id == probe_id,
        ProbesetProbe.deleted_at.is_(None)
    ).first()
    
    if not probeset_probe:
        raise HTTPException(status_code=404, detail="Probe not found in probeset")
    
    probeset_probe.soft_delete()
    db.commit()


# ===============================
# Capability endpoints
# ===============================

@router.get("/capabilities", response_model=CapabilityListDTO, summary="List capabilities")
async def list_capabilities(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search in name and code"),
    category: Optional[str] = Query(None, description="Filter by category"),
    severity: Optional[str] = Query(None, description="Filter by severity"),
    active_only: bool = Query(True, description="Show only active capabilities"),
    db: Session = Depends(get_db)
):
    """List all capabilities with pagination and filtering."""
    query = db.query(Capability).filter(Capability.deleted_at.is_(None))
    
    if active_only:
        query = query.filter(Capability.active == True)
    
    if category:
        query = query.filter(Capability.category == category)
    
    if severity:
        query = query.filter(Capability.severity == severity)
    
    if search:
        query = query.filter(
            Capability.name.ilike(f"%{search}%") | 
            Capability.code.ilike(f"%{search}%")
        )
    
    query = query.order_by(Capability.name)
    items, total, page, per_page, pages = paginate_query(query, page, per_page)
    
    return CapabilityListDTO(
        items=[CapabilityResponseDTO.from_orm(capability) for capability in items],
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.post("/capabilities", response_model=CapabilityResponseDTO, status_code=status.HTTP_201_CREATED, summary="Create capability")
async def create_capability(
    capability_data: CapabilityCreateDTO,
    db: Session = Depends(get_db)
):
    """Create a new capability."""
    try:
        capability = Capability(**capability_data.dict())
        db.add(capability)
        db.commit()
        db.refresh(capability)
        return CapabilityResponseDTO.from_orm(capability)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Capability with this code or name already exists")


@router.get("/capabilities/{capability_id}", response_model=CapabilityResponseDTO, summary="Get capability")
async def get_capability(
    capability_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Get a specific capability by ID."""
    capability = get_capability_or_404(db, capability_id)
    return CapabilityResponseDTO.from_orm(capability)


@router.put("/capabilities/{capability_id}", response_model=CapabilityResponseDTO, summary="Update capability")
async def update_capability(
    capability_id: uuid.UUID,
    capability_data: CapabilityUpdateDTO,
    db: Session = Depends(get_db)
):
    """Update a capability."""
    capability = get_capability_or_404(db, capability_id)
    
    # Update only provided fields
    update_data = capability_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(capability, field, value)
    
    try:
        capability.update_audit_fields()
        db.commit()
        db.refresh(capability)
        return CapabilityResponseDTO.from_orm(capability)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Capability with this code or name already exists")


@router.delete("/capabilities/{capability_id}", status_code=status.HTTP_204_NO_CONTENT, summary="Delete capability")
async def delete_capability(
    capability_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Delete a capability (soft delete)."""
    capability = get_capability_or_404(db, capability_id)
    capability.soft_delete()
    db.commit()


# ===============================
# Assessment endpoints
# ===============================

@router.get("/assessments", response_model=AssessmentListDTO, summary="List assessments")
async def list_assessments(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search in name and code"),
    category: Optional[str] = Query(None, description="Filter by category"),
    active_only: bool = Query(True, description="Show only active assessments"),
    db: Session = Depends(get_db)
):
    """List all assessments with pagination and filtering."""
    query = db.query(Assessment).filter(Assessment.deleted_at.is_(None))
    
    if active_only:
        query = query.filter(Assessment.active == True)
    
    if category:
        query = query.filter(Assessment.category == category)
    
    if search:
        query = query.filter(
            Assessment.name.ilike(f"%{search}%") | 
            Assessment.code.ilike(f"%{search}%")
        )
    
    query = query.order_by(Assessment.name)
    items, total, page, per_page, pages = paginate_query(query, page, per_page)
    
    return AssessmentListDTO(
        items=[AssessmentResponseDTO.from_orm(assessment) for assessment in items],
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.post("/assessments", response_model=AssessmentResponseDTO, status_code=status.HTTP_201_CREATED, summary="Create assessment")
async def create_assessment(
    assessment_data: AssessmentCreateDTO,
    db: Session = Depends(get_db)
):
    """Create a new assessment."""
    try:
        assessment = Assessment(**assessment_data.dict())
        db.add(assessment)
        db.commit()
        db.refresh(assessment)
        return AssessmentResponseDTO.from_orm(assessment)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Assessment with this code or name already exists")


@router.get("/assessments/{assessment_id}", response_model=AssessmentResponseDTO, summary="Get assessment")
async def get_assessment(
    assessment_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Get a specific assessment by ID."""
    assessment = get_assessment_or_404(db, assessment_id)
    return AssessmentResponseDTO.from_orm(assessment)


@router.put("/assessments/{assessment_id}", response_model=AssessmentResponseDTO, summary="Update assessment")
async def update_assessment(
    assessment_id: uuid.UUID,
    assessment_data: AssessmentUpdateDTO,
    db: Session = Depends(get_db)
):
    """Update an assessment."""
    assessment = get_assessment_or_404(db, assessment_id)
    
    # Update only provided fields
    update_data = assessment_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(assessment, field, value)
    
    try:
        assessment.update_audit_fields()
        db.commit()
        db.refresh(assessment)
        return AssessmentResponseDTO.from_orm(assessment)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Assessment with this code or name already exists")


@router.delete("/assessments/{assessment_id}", status_code=status.HTTP_204_NO_CONTENT, summary="Delete assessment")
async def delete_assessment(
    assessment_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Delete an assessment (soft delete)."""
    assessment = get_assessment_or_404(db, assessment_id)
    assessment.soft_delete()
    db.commit()