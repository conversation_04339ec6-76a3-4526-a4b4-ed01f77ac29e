"""
FastAPI endpoints for Driver and Provider management.
"""

import uuid
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from ....core.database import get_db
from ....infrastructure.persistence.models import Driver, Provider, LLMModel, ModelDefaultParam, TenantProviderConfig
from ....application.dto.driver_dto import (
    DriverCreateDTO, DriverUpdateDTO, DriverResponseDTO, DriverListDTO,
    ProviderCreateDTO, ProviderUpdateDTO, ProviderResponseDTO, ProviderListDTO,
    LLMModelCreateDTO, LLMModelUpdateDTO, LLMModelResponseDTO, LLMModelListDTO,
    ModelDefaultParamCreateDTO, ModelDefaultParamUpdateDTO, ModelDefaultParamResponseDTO, ModelDefaultParamListDTO,
    TenantProviderConfigCreateDTO, TenantProviderConfigUpdateDTO, TenantProviderConfigResponseDTO, TenantProviderConfigListDTO
)

router = APIRouter()


# Helper functions
def get_driver_or_404(db: Session, driver_id: uuid.UUID) -> Driver:
    """Get driver by ID or raise 404."""
    driver = db.query(Driver).filter(
        Driver.id == driver_id,
        Driver.deleted_at.is_(None)
    ).first()
    if not driver:
        raise HTTPException(status_code=404, detail="Driver not found")
    return driver


def get_provider_or_404(db: Session, provider_id: uuid.UUID) -> Provider:
    """Get provider by ID or raise 404."""
    provider = db.query(Provider).filter(
        Provider.id == provider_id,
        Provider.deleted_at.is_(None)
    ).first()
    if not provider:
        raise HTTPException(status_code=404, detail="Provider not found")
    return provider


def get_model_or_404(db: Session, model_id: uuid.UUID) -> LLMModel:
    """Get LLM model by ID or raise 404."""
    model = db.query(LLMModel).filter(
        LLMModel.id == model_id,
        LLMModel.deleted_at.is_(None)
    ).first()
    if not model:
        raise HTTPException(status_code=404, detail="Model not found")
    return model


def paginate_query(query, page: int = 1, per_page: int = 20):
    """Paginate SQLAlchemy query."""
    total = query.count()
    items = query.offset((page - 1) * per_page).limit(per_page).all()
    pages = (total + per_page - 1) // per_page
    return items, total, page, per_page, pages


# ===============================
# Driver endpoints
# ===============================

@router.get("/drivers", response_model=DriverListDTO, summary="List drivers")
async def list_drivers(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search in name and type"),
    type_filter: Optional[str] = Query(None, description="Filter by driver type"),
    active_only: bool = Query(True, description="Show only active drivers"),
    db: Session = Depends(get_db)
):
    """List all drivers with pagination and filtering."""
    query = db.query(Driver).filter(Driver.deleted_at.is_(None))
    
    if active_only:
        query = query.filter(Driver.active == True)
    
    if type_filter:
        query = query.filter(Driver.type == type_filter)
    
    if search:
        query = query.filter(
            Driver.name.ilike(f"%{search}%") | 
            Driver.type.ilike(f"%{search}%")
        )
    
    query = query.order_by(Driver.name)
    items, total, page, per_page, pages = paginate_query(query, page, per_page)
    
    return DriverListDTO(
        items=[DriverResponseDTO.from_orm(driver) for driver in items],
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.post("/drivers", response_model=DriverResponseDTO, status_code=status.HTTP_201_CREATED, summary="Create driver")
async def create_driver(
    driver_data: DriverCreateDTO,
    db: Session = Depends(get_db)
):
    """Create a new driver."""
    try:
        driver = Driver(**driver_data.dict())
        db.add(driver)
        db.commit()
        db.refresh(driver)
        return DriverResponseDTO.from_orm(driver)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Driver with this name already exists")


@router.get("/drivers/{driver_id}", response_model=DriverResponseDTO, summary="Get driver")
async def get_driver(
    driver_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Get a specific driver by ID."""
    driver = get_driver_or_404(db, driver_id)
    return DriverResponseDTO.from_orm(driver)


@router.put("/drivers/{driver_id}", response_model=DriverResponseDTO, summary="Update driver")
async def update_driver(
    driver_id: uuid.UUID,
    driver_data: DriverUpdateDTO,
    db: Session = Depends(get_db)
):
    """Update a driver."""
    driver = get_driver_or_404(db, driver_id)
    
    # Update only provided fields
    update_data = driver_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(driver, field, value)
    
    try:
        driver.update_audit_fields()
        db.commit()
        db.refresh(driver)
        return DriverResponseDTO.from_orm(driver)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Driver with this name already exists")


@router.delete("/drivers/{driver_id}", status_code=status.HTTP_204_NO_CONTENT, summary="Delete driver")
async def delete_driver(
    driver_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Delete a driver (soft delete)."""
    driver = get_driver_or_404(db, driver_id)
    driver.soft_delete()
    db.commit()


# ===============================
# Provider endpoints
# ===============================

@router.get("/providers", response_model=ProviderListDTO, summary="List providers")
async def list_providers(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search in name"),
    driver_id: Optional[uuid.UUID] = Query(None, description="Filter by driver ID"),
    db: Session = Depends(get_db)
):
    """List all providers with pagination and filtering."""
    query = db.query(Provider).filter(Provider.deleted_at.is_(None))
    
    if driver_id:
        query = query.filter(Provider.driver_id == driver_id)
    
    if search:
        query = query.filter(Provider.name.ilike(f"%{search}%"))
    
    query = query.order_by(Provider.name)
    items, total, page, per_page, pages = paginate_query(query, page, per_page)
    
    return ProviderListDTO(
        items=[ProviderResponseDTO.model_validate(provider.to_dict()) for provider in items],
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.post("/providers", response_model=ProviderResponseDTO, status_code=status.HTTP_201_CREATED, summary="Create provider")
async def create_provider(
    provider_data: ProviderCreateDTO,
    db: Session = Depends(get_db)
):
    """Create a new provider."""
    # Verify driver exists
    get_driver_or_404(db, provider_data.driver_id)
    
    try:
        provider = Provider(**provider_data.dict())
        db.add(provider)
        db.commit()
        db.refresh(provider)
        return ProviderResponseDTO.model_validate(provider.to_dict())
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Provider with this name and driver already exists")


@router.get("/providers/{provider_id}", response_model=ProviderResponseDTO, summary="Get provider")
async def get_provider(
    provider_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Get a specific provider by ID."""
    provider = get_provider_or_404(db, provider_id)
    return ProviderResponseDTO.model_validate(provider.to_dict())


@router.put("/providers/{provider_id}", response_model=ProviderResponseDTO, summary="Update provider")
async def update_provider(
    provider_id: uuid.UUID,
    provider_data: ProviderUpdateDTO,
    db: Session = Depends(get_db)
):
    """Update a provider."""
    provider = get_provider_or_404(db, provider_id)
    
    # Verify driver exists if driver_id is being updated
    if provider_data.driver_id:
        get_driver_or_404(db, provider_data.driver_id)
    
    # Update only provided fields
    update_data = provider_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(provider, field, value)
    
    try:
        provider.update_audit_fields()
        db.commit()
        db.refresh(provider)
        return ProviderResponseDTO.model_validate(provider.to_dict())
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Provider with this name and driver already exists")


@router.delete("/providers/{provider_id}", status_code=status.HTTP_204_NO_CONTENT, summary="Delete provider")
async def delete_provider(
    provider_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Delete a provider (soft delete)."""
    provider = get_provider_or_404(db, provider_id)
    provider.soft_delete()
    db.commit()


# ===============================
# LLM Model endpoints
# ===============================

@router.get("/models", response_model=LLMModelListDTO, summary="List LLM models")
async def list_models(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search in name"),
    provider_id: Optional[uuid.UUID] = Query(None, description="Filter by provider ID"),
    active_only: bool = Query(True, description="Show only active models"),
    supports_functions: Optional[bool] = Query(None, description="Filter by function support"),
    supports_vision: Optional[bool] = Query(None, description="Filter by vision support"),
    db: Session = Depends(get_db)
):
    """List all LLM models with pagination and filtering."""
    query = db.query(LLMModel).filter(LLMModel.deleted_at.is_(None))
    
    if active_only:
        query = query.filter(LLMModel.active == True)
    
    if provider_id:
        query = query.filter(LLMModel.provider_id == provider_id)
    
    if supports_functions is not None:
        query = query.filter(LLMModel.supports_functions == supports_functions)
    
    if supports_vision is not None:
        query = query.filter(LLMModel.supports_vision == supports_vision)
    
    if search:
        query = query.filter(LLMModel.name.ilike(f"%{search}%"))
    
    query = query.order_by(LLMModel.name)
    items, total, page, per_page, pages = paginate_query(query, page, per_page)
    
    return LLMModelListDTO(
        items=[LLMModelResponseDTO.model_validate(model.to_dict()) for model in items],
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.post("/models", response_model=LLMModelResponseDTO, status_code=status.HTTP_201_CREATED, summary="Create LLM model")
async def create_model(
    model_data: LLMModelCreateDTO,
    db: Session = Depends(get_db)
):
    """Create a new LLM model."""
    # Verify provider exists
    get_provider_or_404(db, model_data.provider_id)
    
    try:
        model = LLMModel(**model_data.dict())
        db.add(model)
        db.commit()
        db.refresh(model)
        return LLMModelResponseDTO.model_validate(model.to_dict())
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Model with this name, version, and provider already exists")


@router.get("/models/{model_id}", response_model=LLMModelResponseDTO, summary="Get LLM model")
async def get_model(
    model_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Get a specific LLM model by ID."""
    model = get_model_or_404(db, model_id)
    return LLMModelResponseDTO.model_validate(model.to_dict())


@router.put("/models/{model_id}", response_model=LLMModelResponseDTO, summary="Update LLM model")
async def update_model(
    model_id: uuid.UUID,
    model_data: LLMModelUpdateDTO,
    db: Session = Depends(get_db)
):
    """Update an LLM model."""
    model = get_model_or_404(db, model_id)
    
    # Verify provider exists if provider_id is being updated
    if model_data.provider_id:
        get_provider_or_404(db, model_data.provider_id)
    
    # Update only provided fields
    update_data = model_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(model, field, value)
    
    try:
        model.update_audit_fields()
        db.commit()
        db.refresh(model)
        return LLMModelResponseDTO.model_validate(model.to_dict())
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Model with this name, version, and provider already exists")


@router.delete("/models/{model_id}", status_code=status.HTTP_204_NO_CONTENT, summary="Delete LLM model")
async def delete_model(
    model_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Delete an LLM model (soft delete)."""
    model = get_model_or_404(db, model_id)
    model.soft_delete()
    db.commit()


# ===============================
# Model Default Parameter endpoints
# ===============================

@router.get("/models/{model_id}/params", response_model=ModelDefaultParamListDTO, summary="List model parameters")
async def list_model_params(
    model_id: uuid.UUID,
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db)
):
    """List all parameters for a specific model."""
    # Verify model exists
    get_model_or_404(db, model_id)
    
    query = db.query(ModelDefaultParam).filter(
        ModelDefaultParam.model_id == model_id,
        ModelDefaultParam.deleted_at.is_(None)
    ).order_by(ModelDefaultParam.key)
    
    items, total, page, per_page, pages = paginate_query(query, page, per_page)
    
    return ModelDefaultParamListDTO(
        items=[ModelDefaultParamResponseDTO.from_orm(param) for param in items],
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.post("/models/{model_id}/params", response_model=ModelDefaultParamResponseDTO, status_code=status.HTTP_201_CREATED, summary="Create model parameter")
async def create_model_param(
    model_id: uuid.UUID,
    param_data: ModelDefaultParamCreateDTO,
    db: Session = Depends(get_db)
):
    """Create a new model parameter."""
    # Verify model exists
    get_model_or_404(db, model_id)
    
    # Override model_id from URL
    param_data.model_id = model_id
    
    try:
        param = ModelDefaultParam(**param_data.dict())
        db.add(param)
        db.commit()
        db.refresh(param)
        return ModelDefaultParamResponseDTO.from_orm(param)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Parameter with this key already exists for this model")


@router.get("/params/{param_id}", response_model=ModelDefaultParamResponseDTO, summary="Get model parameter")
async def get_model_param(
    param_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Get a specific model parameter by ID."""
    param = db.query(ModelDefaultParam).filter(
        ModelDefaultParam.id == param_id,
        ModelDefaultParam.deleted_at.is_(None)
    ).first()
    if not param:
        raise HTTPException(status_code=404, detail="Parameter not found")
    return ModelDefaultParamResponseDTO.from_orm(param)


@router.put("/params/{param_id}", response_model=ModelDefaultParamResponseDTO, summary="Update model parameter")
async def update_model_param(
    param_id: uuid.UUID,
    param_data: ModelDefaultParamUpdateDTO,
    db: Session = Depends(get_db)
):
    """Update a model parameter."""
    param = db.query(ModelDefaultParam).filter(
        ModelDefaultParam.id == param_id,
        ModelDefaultParam.deleted_at.is_(None)
    ).first()
    if not param:
        raise HTTPException(status_code=404, detail="Parameter not found")
    
    # Verify model exists if model_id is being updated
    if param_data.model_id:
        get_model_or_404(db, param_data.model_id)
    
    # Update only provided fields
    update_data = param_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(param, field, value)
    
    try:
        param.update_audit_fields()
        db.commit()
        db.refresh(param)
        return ModelDefaultParamResponseDTO.from_orm(param)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Parameter with this key already exists for this model")


@router.delete("/params/{param_id}", status_code=status.HTTP_204_NO_CONTENT, summary="Delete model parameter")
async def delete_model_param(
    param_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Delete a model parameter (soft delete)."""
    param = db.query(ModelDefaultParam).filter(
        ModelDefaultParam.id == param_id,
        ModelDefaultParam.deleted_at.is_(None)
    ).first()
    if not param:
        raise HTTPException(status_code=404, detail="Parameter not found")
    
    param.soft_delete()
    db.commit()