"""
Celery configuration and initialization for SACRA2 backend.

This module serves as the main entry point for Celery workers,
scheduler, and monitoring tools like Flower.
"""

import os
from celery import Celery
from src.core.config import get_settings

# Get application settings
settings = get_settings()

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('CELERY_CONFIG_MODULE', 'src.workers.scheduler')

# Create Celery application
app = Celery(
    'sacra2',
    broker=settings.celery.broker_url,
    backend=settings.celery.result_backend,
    include=[
        'src.workers.tasks.health',
        'src.workers.tasks.notifications',
        'src.workers.tasks.data_processing',
        'src.workers.tasks.cleanup',
    ]
)

# Configure Celery using the settings from Pydantic
app.conf.update(
    task_serializer=settings.celery.task_serializer,
    accept_content=settings.celery.accept_content,
    result_serializer=settings.celery.result_serializer,
    timezone=settings.celery.timezone,
    enable_utc=settings.celery.enable_utc,
    task_track_started=settings.celery.task_track_started,
    task_time_limit=settings.celery.task_time_limit,
    task_soft_time_limit=settings.celery.task_soft_time_limit,
    worker_prefetch_multiplier=int(os.getenv('CELERY_WORKER_PREFETCH_MULTIPLIER', 1)),
    worker_concurrency=int(os.getenv('CELERY_WORKER_CONCURRENCY', 2)),
    
    # Task routing
    task_routes={
        'src.workers.tasks.health.*': {'queue': 'health'},
        'src.workers.tasks.notifications.*': {'queue': 'notifications'},
        'src.workers.tasks.data_processing.*': {'queue': 'data_processing'},
        'src.workers.tasks.cleanup.*': {'queue': 'cleanup'},
    },
    
    # Beat schedule for periodic tasks
    beat_schedule={
        'health-check-every-5-minutes': {
            'task': 'src.workers.tasks.health.health_check',
            'schedule': 300.0,  # 5 minutes
            'options': {'queue': 'health'}
        },
        'cleanup-old-tasks-every-hour': {
            'task': 'src.workers.tasks.cleanup.cleanup_old_tasks',
            'schedule': 3600.0,  # 1 hour
            'options': {'queue': 'cleanup'}
        },
    },
)

# Autodiscover tasks
app.autodiscover_tasks(['src.workers'])

@app.task(bind=True)
def debug_task(self):
    """Debug task for testing Celery setup."""
    print(f'Request: {self.request!r}')

if __name__ == '__main__':
    app.start()