"""
Health check tasks for Celery workers.
"""

from celery import shared_task
from src.core.logging import get_logger

logger = get_logger(__name__)


@shared_task(bind=True, name='src.workers.tasks.health.health_check')
def health_check(self):
    """
    Basic health check task.
    
    Returns:
        dict: Health check result with timestamp
    """
    import time
    
    try:
        # Perform basic health check
        result = {
            'status': 'healthy',
            'timestamp': time.time(),
            'task_id': self.request.id,
            'worker': self.request.hostname
        }
        
        logger.info(f"Health check completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise


@shared_task(bind=True, name='src.workers.tasks.health.system_info')
def system_info(self):
    """
    Get system information for monitoring.
    
    Returns:
        dict: System information
    """
    import platform
    import psutil
    
    try:
        # Get system information
        info = {
            'platform': platform.platform(),
            'python_version': platform.python_version(),
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent,
            'task_id': self.request.id,
            'worker': self.request.hostname
        }
        
        logger.info(f"System info collected: {info}")
        return info
        
    except Exception as e:
        logger.error(f"Failed to collect system info: {str(e)}")
        raise