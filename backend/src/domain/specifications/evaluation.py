"""
Evaluation-related specifications.
"""
from __future__ import annotations

from dataclasses import dataclass
from typing import Any

from .base import Specification, FieldEquals, FieldIn, DateRange
from sqlalchemy.sql.elements import ColumnElement


@dataclass(frozen=True)
class StatusSpec(Specification):
    """Filter evaluations by status exact match."""

    status: str

    def to_expression(self, model) -> ColumnElement[bool]:
        if not hasattr(model, "status"):
            raise AttributeError(f"Model {model.__name__} has no field 'status'")
        return getattr(model, "status") == self.status


@dataclass(frozen=True)
class StatusInSpec(Specification):
    statuses: list[str]

    def to_expression(self, model) -> ColumnElement[bool]:
        if not hasattr(model, "status"):
            raise AttributeError(f"Model {model.__name__} has no field 'status'")
        return getattr(model, "status").in_(self.statuses)


@dataclass(frozen=True)
class CreatedAtInRange(DateRange):
    """Filter by created_at date range."""

    field: str = "created_at"


@dataclass(frozen=True)
class UpdatedAtInRange(DateRange):
    """Filter by updated_at date range."""

    field: str = "updated_at"
