"""
Specification pattern base with composable boolean logic for query filtering.

A Specification encapsulates a business rule that can be combined with others
and translated into a SQLAlchemy boolean expression for a given model.
"""
from __future__ import annotations

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Optional

from sqlalchemy.sql.elements import ColumnElement


class Specification(ABC):
    """Abstract base for specifications."""

    @abstractmethod
    def to_expression(self, model) -> ColumnElement[bool]:
        """Return a SQLAlchemy boolean expression for the given model class."""
        raise NotImplementedError

    def __and__(self, other: "Specification") -> "Specification":
        return AndSpecification(self, other)

    def __or__(self, other: "Specification") -> "Specification":
        return OrSpecification(self, other)

    def __invert__(self) -> "Specification":
        return NotSpecification(self)


@dataclass(frozen=True)
class AndSpecification(Specification):
    left: Specification
    right: Specification

    def to_expression(self, model) -> ColumnElement[bool]:
        return self.left.to_expression(model) & self.right.to_expression(model)


@dataclass(frozen=True)
class OrSpecification(Specification):
    left: Specification
    right: Specification

    def to_expression(self, model) -> ColumnElement[bool]:
        return self.left.to_expression(model) | self.right.to_expression(model)


@dataclass(frozen=True)
class NotSpecification(Specification):
    spec: Specification

    def to_expression(self, model) -> ColumnElement[bool]:
        return ~self.spec.to_expression(model)


@dataclass(frozen=True)
class FieldEquals(Specification):
    """A simple field equality specification."""

    field: str
    value: Any

    def to_expression(self, model) -> ColumnElement[bool]:
        if not hasattr(model, self.field):
            raise AttributeError(f"Model {model.__name__} has no field '{self.field}'")
        return getattr(model, self.field) == self.value


@dataclass(frozen=True)
class FieldIn(Specification):
    field: str
    values: list[Any]

    def to_expression(self, model) -> ColumnElement[bool]:
        if not hasattr(model, self.field):
            raise AttributeError(f"Model {model.__name__} has no field '{self.field}'")
        return getattr(model, self.field).in_(self.values)


@dataclass(frozen=True)
class DateRange(Specification):
    field: str
    start: Optional[Any] = None
    end: Optional[Any] = None
    inclusive: bool = True

    def to_expression(self, model) -> ColumnElement[bool]:
        if not hasattr(model, self.field):
            raise AttributeError(f"Model {model.__name__} has no field '{self.field}'")
        column = getattr(model, self.field)
        expr = None
        if self.start is not None:
            expr = column >= self.start if self.inclusive else column > self.start
        if self.end is not None:
            end_expr = column <= self.end if self.inclusive else column < self.end
            expr = end_expr if expr is None else (expr & end_expr)
        if expr is None:
            # No bounds specified; return a trivial true expression
            return column == column
        return expr
