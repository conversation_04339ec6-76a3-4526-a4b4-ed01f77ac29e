"""
Tenant-related specifications.
"""
from __future__ import annotations

from dataclasses import dataclass
from .base import Specification
from sqlalchemy.sql.elements import ColumnElement


@dataclass(frozen=True)
class TenantIdSpec(Specification):
    tenant_id: str

    def to_expression(self, model) -> ColumnElement[bool]:
        if not hasattr(model, "tenant_id"):
            raise AttributeError(f"Model {model.__name__} has no field 'tenant_id'")
        return getattr(model, "tenant_id") == self.tenant_id
