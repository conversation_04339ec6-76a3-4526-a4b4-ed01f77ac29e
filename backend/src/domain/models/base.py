"""
Base entity model for SACRA2 domain models.
Provides common fields and functionality for all domain entities.
"""

import uuid
from datetime import datetime
from typing import Optional, Any, Dict
from sqlalchemy import Column, DateTime, String, func, inspect
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import declarative_base, Mapped, mapped_column
from sqlalchemy.sql import expression

# Import from core types
from ...core.types import AuditInfo, EntityMetadata


class BaseEntity:
    """Base class for all domain entities"""
    
    # Primary key using UUID
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True
    )
    
    # Audit fields
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now()
    )
    
    created_by: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True
    )
    
    updated_by: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True
    )
    
    # Soft delete fields
    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True
    )
    
    deleted_by: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True
    )
    
    # Version for optimistic locking
    version: Mapped[int] = mapped_column(
        default=1,
        nullable=False
    )
    
    @declared_attr
    def __tablename__(cls) -> str:
        """Generate table name from class name"""
        return cls.__name__.lower() + 's'
    
    @property
    def is_deleted(self) -> bool:
        """Check if entity is soft deleted"""
        return self.deleted_at is not None
    
    @property
    def audit_info(self) -> AuditInfo:
        """Get audit information"""
        return AuditInfo(
            created_at=self.created_at,
            updated_at=self.updated_at,
            created_by=self.created_by,
            updated_by=self.updated_by,
            deleted_at=self.deleted_at,
            deleted_by=self.deleted_by
        )
    
    @property
    def metadata(self) -> EntityMetadata:
        """Get entity metadata"""
        return EntityMetadata(
            id=self.id,
            version=self.version,
            is_deleted=self.is_deleted
        )
    
    def soft_delete(self, deleted_by: Optional[str] = None) -> None:
        """Soft delete the entity"""
        self.deleted_at = datetime.utcnow()
        self.deleted_by = deleted_by
    
    def restore(self) -> None:
        """Restore soft deleted entity"""
        self.deleted_at = None
        self.deleted_by = None
    
    def update_audit_fields(self, user_id: Optional[str] = None) -> None:
        """Update audit fields"""
        self.updated_at = datetime.utcnow()
        if user_id:
            self.updated_by = user_id
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert entity to dictionary"""
        return {
            'id': str(self.id),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
            'updated_by': self.updated_by,
            'deleted_at': self.deleted_at.isoformat() if self.deleted_at else None,
            'deleted_by': self.deleted_by,
            'version': self.version,
            'is_deleted': self.is_deleted
        }
    
    def __repr__(self) -> str:
        """String representation"""
        return f"<{self.__class__.__name__}(id={self.id})>"
    
    def __eq__(self, other: Any) -> bool:
        """Check equality based on ID"""
        if not isinstance(other, BaseEntity):
            return False
        return self.id == other.id
    
    def __hash__(self) -> int:
        """Hash based on ID"""
        return hash(self.id)


# Create declarative base
Base = declarative_base(cls=BaseEntity)


class SoftDeleteMixin:
    """Mixin for soft delete functionality"""
    
    @classmethod
    def not_deleted(cls):
        """Query filter for non-deleted entities"""
        return cls.deleted_at.is_(None)
    
    @classmethod
    def deleted(cls):
        """Query filter for deleted entities"""
        return cls.deleted_at.is_not(None)


class TimestampMixin:
    """Mixin for timestamp functionality"""
    
    @classmethod
    def created_after(cls, timestamp: datetime):
        """Filter entities created after timestamp"""
        return cls.created_at >= timestamp
    
    @classmethod
    def created_before(cls, timestamp: datetime):
        """Filter entities created before timestamp"""
        return cls.created_at <= timestamp
    
    @classmethod
    def updated_after(cls, timestamp: datetime):
        """Filter entities updated after timestamp"""
        return cls.updated_at >= timestamp
    
    @classmethod
    def updated_before(cls, timestamp: datetime):
        """Filter entities updated before timestamp"""
        return cls.updated_at <= timestamp


class VersionMixin:
    """Mixin for version management"""
    
    def increment_version(self) -> None:
        """Increment version number"""
        self.version += 1
    
    def check_version(self, expected_version: int) -> bool:
        """Check if current version matches expected version"""
        return self.version == expected_version


class EntityStateMixin:
    """Mixin for entity state management"""
    
    @property
    def is_active(self) -> bool:
        """Check if entity is active (not deleted)"""
        return not self.is_deleted
    
    @property
    def is_archived(self) -> bool:
        """Check if entity is archived (deleted)"""
        return self.is_deleted
    
    def activate(self) -> None:
        """Activate entity (restore from soft delete)"""
        self.restore()
    
    def archive(self, archived_by: Optional[str] = None) -> None:
        """Archive entity (soft delete)"""
        self.soft_delete(archived_by)


# Utility functions for entity management
def generate_entity_id() -> uuid.UUID:
    """Generate a new entity ID"""
    return uuid.uuid4()


def get_entity_changes(old_entity: BaseEntity, new_entity: BaseEntity) -> Dict[str, Any]:
    """Get changes between two entity states"""
    changes = {}
    
    # Get all column names
    mapper = inspect(old_entity.__class__)
    for column in mapper.columns:
        column_name = column.name
        old_value = getattr(old_entity, column_name)
        new_value = getattr(new_entity, column_name)
        
        if old_value != new_value:
            changes[column_name] = {
                'old': str(old_value),
                'new': str(new_value)
            }
    
    return changes


def validate_entity_id(entity_id: str) -> uuid.UUID:
    """Validate and convert entity ID to UUID"""
    try:
        return uuid.UUID(entity_id)
    except ValueError as e:
        raise ValueError(f"Invalid entity ID format: {entity_id}") from e


def is_valid_uuid(uuid_to_test: str) -> bool:
    """Check if string is a valid UUID"""
    try:
        uuid.UUID(uuid_to_test)
        return True
    except ValueError:
        return False