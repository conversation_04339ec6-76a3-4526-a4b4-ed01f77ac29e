version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: sacra2-postgres
    environment:
      POSTGRES_DB: sacra2_db
      POSTGRES_USER: sacra2_user
      POSTGRES_PASSWORD: sacra2_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sacra2_user -d sacra2_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - sacra2-network

  # Redis Cache and Message Broker
  redis:
    image: redis:7-alpine
    container_name: sacra2-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - sacra2-network

  # SACRA2 Backend API
  backend:
    build:
      context: .
      target: development
    container_name: sacra2-backend
    env_file:
      - .env
    environment:
      DATABASE_URL: postgresql+asyncpg://sacra2_user:sacra2_password@postgres:5432/sacra2_db
      REDIS_URL: redis://redis:6379/0
      CELERY_BROKER_URL: redis://redis:6379/1
      CELERY_RESULT_BACKEND: redis://redis:6379/2
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - ./logs:/var/log/sacra2
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sacra2-network

  # Celery Worker
  celery-worker:
    build:
      context: .
      target: development
    container_name: sacra2-celery-worker
    command: ["bash", "-c", "cd /app && poetry run celery -A src.workers.scheduler worker --loglevel=info --concurrency=2"]
    env_file:
      - .env
    environment:
      DATABASE_URL: postgresql+asyncpg://sacra2_user:sacra2_password@postgres:5432/sacra2_db
      REDIS_URL: redis://redis:6379/0
      CELERY_BROKER_URL: redis://redis:6379/1
      CELERY_RESULT_BACKEND: redis://redis:6379/2
    volumes:
      - .:/app
      - ./logs:/var/log/sacra2
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sacra2-network

  # Celery Beat Scheduler
  celery-beat:
    build:
      context: .
      target: development
    container_name: sacra2-celery-beat
    command: ["bash", "-c", "cd /app && poetry run celery -A src.workers.scheduler beat --loglevel=info"]
    env_file:
      - .env
    environment:
      DATABASE_URL: postgresql+asyncpg://sacra2_user:sacra2_password@postgres:5432/sacra2_db
      REDIS_URL: redis://redis:6379/0
      CELERY_BROKER_URL: redis://redis:6379/1
      CELERY_RESULT_BACKEND: redis://redis:6379/2
    volumes:
      - .:/app
      - ./logs:/var/log/sacra2
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sacra2-network

  # Flower - Celery Monitoring
  flower:
    build:
      context: .
      target: development
    container_name: sacra2-flower
    command: ["bash", "-c", "cd /app && poetry run celery -A src.workers.scheduler flower --port=5555"]
    ports:
      - "5555:5555"
    env_file:
      - .env
    environment:
      CELERY_BROKER_URL: redis://redis:6379/1
      CELERY_RESULT_BACKEND: redis://redis:6379/2
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - sacra2-network

  # Adminer - Database Admin Tool
  adminer:
    image: adminer:latest
    container_name: sacra2-adminer
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    networks:
      - sacra2-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  sacra2-network:
    driver: bridge