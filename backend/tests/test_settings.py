"""
Test-specific settings for SACRA2 Backend tests.
This module provides a test configuration that doesn't depend on environment variables.
"""
from typing import Dict, Any

# Test database configuration
TEST_DATABASE_URL = "sqlite:///./test_sacra2.db"

# Test security configuration
TEST_SECURITY_CONFIG: Dict[str, Any] = {
    "secret_key": "test-secret-key-not-for-production",
    "algorithm": "HS256",
    "access_token_expire_minutes": 30,
    "cors_origins": ["http://localhost:3000"],
    "cors_allow_credentials": True,
    "cors_allow_methods": ["*"],
    "cors_allow_headers": ["*"]
}

# Test Redis configuration
TEST_REDIS_CONFIG: Dict[str, Any] = {
    "host": "localhost",
    "port": 6379,
    "db": 15,
    "ssl": False
}

# Test monitoring configuration
TEST_MONITORING_CONFIG: Dict[str, Any] = {
    "log_level": "DEBUG"
}

# Test application configuration
TEST_APP_CONFIG: Dict[str, Any] = {
    "name": "SACRA2 Test",
    "description": "SACRA2 Test Application",
    "version": "1.0.0",
    "debug": True,
    "environment": "testing",
    "testing": True,
    "host": "127.0.0.1",
    "port": 8000,
    "api_v1_prefix": "/api/v1",
    "database_url": TEST_DATABASE_URL,
    "security": TEST_SECURITY_CONFIG,
    "redis": TEST_REDIS_CONFIG,
    "monitoring": TEST_MONITORING_CONFIG
}
