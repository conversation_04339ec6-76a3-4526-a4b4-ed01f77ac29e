"""
Test configuration for SACRA2 Backend tests.
This module provides a test configuration that doesn't depend on environment variables.
"""
from typing import Any, Dict, Optional
from pydantic import BaseModel, Field

# Use absolute imports to avoid relative import issues
from src.core.config import Settings, DatabaseConfig, SecurityConfig, RedisConfig, MonitoringConfig

class TestDatabaseConfig(DatabaseConfig):
    """Test database configuration"""
    url: str = "sqlite:///./test_sacra2.db"
    
    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls,
        init_settings,
        env_settings,
        dotenv_settings,
        file_secret_settings,
    ):
        # Only initialization values; ignore environment and dotenv
        return (init_settings,)
    
    class Config:
        extra = "ignore"
        arbitrary_types_allowed = True

class TestSecurityConfig(SecurityConfig):
    """Test security configuration"""
    secret_key: str = "test-secret-key-not-for-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    cors_origins: list[str] = ["http://localhost:3000"]
    cors_allow_credentials: bool = True
    cors_allow_methods: list[str] = ["*"]
    cors_allow_headers: list[str] = ["*"]
    
    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls,
        init_settings,
        env_settings,
        dotenv_settings,
        file_secret_settings,
    ):
        return (init_settings,)
    
    class Config:
        extra = "ignore"
        arbitrary_types_allowed = True

class TestRedisConfig(RedisConfig):
    """Test Redis configuration"""
    host: str = "localhost"
    port: int = 6379
    db: int = 15
    ssl: bool = False
    
    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls,
        init_settings,
        env_settings,
        dotenv_settings,
        file_secret_settings,
    ):
        return (init_settings,)
    
    class Config:
        extra = "ignore"
        arbitrary_types_allowed = True

class TestMonitoringConfig(MonitoringConfig):
    """Test monitoring configuration"""
    log_level: str = "DEBUG"
    
    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls,
        init_settings,
        env_settings,
        dotenv_settings,
        file_secret_settings,
    ):
        return (init_settings,)
    
    class Config:
        extra = "ignore"
        arbitrary_types_allowed = True

class TestSettings(Settings):
    """Test settings that don't load from environment variables"""
    environment: str = "testing"
    testing: bool = True
    debug: bool = True
    
    # Application
    app_name: str = "SACRA2 Test"
    app_version: str = "1.0.0"
    api_v1_prefix: str = "/api/v1"
    
    # Server
    host: str = "127.0.0.1"
    port: int = 8000
    
    # Configurations
    database: TestDatabaseConfig = Field(default_factory=TestDatabaseConfig)
    security: TestSecurityConfig = Field(default_factory=TestSecurityConfig)
    redis: TestRedisConfig = Field(default_factory=TestRedisConfig)
    monitoring: TestMonitoringConfig = Field(default_factory=TestMonitoringConfig)
    
    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls,
        init_settings,
        env_settings,
        dotenv_settings,
        file_secret_settings,
    ):
        return (init_settings,)
    
    class Config:
        extra = "ignore"
        arbitrary_types_allowed = True

# Global test settings instance
test_settings = TestSettings()

def get_test_settings() -> TestSettings:
    """Get test settings instance."""
    return test_settings
