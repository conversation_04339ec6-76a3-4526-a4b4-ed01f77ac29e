"""
Unit tests for SACRA2 domain models.
"""

import pytest
import uuid
from datetime import datetime
from sqlalchemy.exc import IntegrityError

from src.infrastructure.persistence.models import (
    Driver, Provider, LLMModel, ModelDefaultParam, TenantProviderConfig,
    Tenant, Project,
    Probe, Probeset, ProbesetProbe, Capability, CapabilitySet, Assessment, AssessmentCapability,
    EvaluationRun, RunModel, RunProbeResult
)


class TestDriverModel:
    """Test Driver model."""
    
    def test_create_driver(self, db_session):
        """Test creating a driver."""
        driver = Driver(
            name="Test Driver",
            type="litellm",
            description="Test driver for unit testing"
        )
        db_session.add(driver)
        db_session.commit()
        
        assert driver.id is not None
        assert driver.name == "Test Driver"
        assert driver.type == "litellm"
        assert driver.active is True
        assert driver.created_at is not None
    
    def test_driver_unique_name(self, db_session):
        """Test that driver names must be unique."""
        driver1 = Driver(name="Unique Driver", type="litellm")
        driver2 = Driver(name="Unique Driver", type="openai")
        
        db_session.add(driver1)
        db_session.commit()
        
        db_session.add(driver2)
        with pytest.raises(IntegrityError):
            db_session.commit()
    
    def test_driver_to_dict(self, db_session):
        """Test driver to_dict method."""
        driver = Driver(
            name="Test Driver",
            type="litellm",
            description="Test description"
        )
        db_session.add(driver)
        db_session.commit()
        
        data = driver.to_dict()
        assert data['name'] == "Test Driver"
        assert data['type'] == "litellm"
        assert data['description'] == "Test description"
        assert data['active'] is True
        assert 'id' in data
        assert 'created_at' in data


class TestProviderModel:
    """Test Provider model."""
    
    def test_create_provider(self, db_session, sample_driver):
        """Test creating a provider."""
        provider = Provider(
            name="Test Provider",
            url="https://api.test.com",
            api_key="test-key",
            max_concurrent_requests=50,
            driver_id=sample_driver.id
        )
        db_session.add(provider)
        db_session.commit()
        
        assert provider.id is not None
        assert provider.name == "Test Provider"
        assert provider.driver_id == sample_driver.id
        assert provider.max_concurrent_requests == 50
    
    def test_provider_driver_relationship(self, db_session, sample_driver):
        """Test provider-driver relationship."""
        provider = Provider(
            name="Test Provider",
            driver_id=sample_driver.id,
            max_concurrent_requests=10
        )
        db_session.add(provider)
        db_session.commit()
        db_session.refresh(provider)
        
        assert provider.driver.name == sample_driver.name
        assert len(sample_driver.providers) == 1
        assert sample_driver.providers[0].name == "Test Provider"
    
    def test_provider_to_dict_masks_api_key(self, db_session, sample_provider):
        """Test that to_dict masks the API key."""
        data = sample_provider.to_dict()
        assert data['api_key'] == '***'  # Should be masked


class TestLLMModelModel:
    """Test LLMModel model."""
    
    def test_create_model(self, db_session, sample_provider):
        """Test creating an LLM model."""
        model = LLMModel(
            name="test-gpt",
            version="1.0",
            description="Test GPT model",
            requests_per_minute=100,
            tokens_per_minute=50000,
            max_tokens=2048,
            supports_functions=True,
            provider_id=sample_provider.id
        )
        db_session.add(model)
        db_session.commit()
        
        assert model.id is not None
        assert model.name == "test-gpt"
        assert model.full_name == "test-gpt:1.0"
        assert model.supports_functions is True
    
    def test_model_full_name_without_version(self, db_session, sample_provider):
        """Test full_name property without version."""
        model = LLMModel(
            name="test-model",
            provider_id=sample_provider.id
        )
        assert model.full_name == "test-model"
    
    def test_model_get_default_param(self, db_session, sample_model):
        """Test getting default parameters."""
        param = ModelDefaultParam(
            model_id=sample_model.id,
            key="temperature",
            value="0.7",
            param_type="float"
        )
        db_session.add(param)
        db_session.commit()
        db_session.refresh(sample_model)
        
        assert sample_model.get_default_param("temperature") == "0.7"
        assert sample_model.get_default_param("nonexistent") is None


class TestModelDefaultParamModel:
    """Test ModelDefaultParam model."""
    
    def test_create_param(self, db_session, sample_model):
        """Test creating a model parameter."""
        param = ModelDefaultParam(
            model_id=sample_model.id,
            key="temperature",
            value="0.8",
            param_type="float",
            description="Model temperature"
        )
        db_session.add(param)
        db_session.commit()
        
        assert param.id is not None
        assert param.key == "temperature"
        assert param.typed_value == 0.8  # Should be converted to float
    
    def test_param_type_conversion(self, db_session, sample_model):
        """Test parameter type conversions."""
        params = [
            ("int_param", "42", "integer", 42),
            ("float_param", "3.14", "float", 3.14),
            ("bool_param", "true", "boolean", True),
            ("string_param", "test", "string", "test"),
            ("json_param", '{"key": "value"}', "json", {"key": "value"}),
        ]
        
        for key, value, param_type, expected in params:
            param = ModelDefaultParam(
                model_id=sample_model.id,
                key=key,
                value=value,
                param_type=param_type
            )
            db_session.add(param)
            db_session.commit()
            
            assert param.typed_value == expected
            db_session.rollback()  # Reset for next iteration


class TestTenantModel:
    """Test Tenant model."""
    
    def test_create_tenant(self, db_session):
        """Test creating a tenant."""
        tenant = Tenant(
            name="test-tenant",
            display_name="Test Tenant",
            description="Test tenant",
            contact_email="<EMAIL>",
            plan="professional"
        )
        db_session.add(tenant)
        db_session.commit()
        
        assert tenant.id is not None
        assert tenant.name == "test-tenant"
        assert tenant.plan == "professional"
        assert tenant.active is True
        assert tenant.trial is True
    
    def test_tenant_quota_methods(self, db_session):
        """Test tenant quota getter/setter methods."""
        tenant = Tenant(name="quota-tenant")
        
        # Test setting quota
        tenant.set_quota("max_evaluations", 100)
        assert tenant.quota_config == {"max_evaluations": 100}
        
        # Test getting quota
        assert tenant.get_quota("max_evaluations") == 100
        assert tenant.get_quota("nonexistent") is None
    
    def test_tenant_project_count(self, db_session, sample_tenant):
        """Test tenant project count property."""
        project = Project(
            name="test-project",
            tenant_id=sample_tenant.id
        )
        db_session.add(project)
        db_session.commit()
        db_session.refresh(sample_tenant)
        
        assert sample_tenant.project_count == 1


class TestProjectModel:
    """Test Project model."""
    
    def test_create_project(self, db_session, sample_tenant):
        """Test creating a project."""
        project = Project(
            name="test-project",
            display_name="Test Project",
            description="Test project description",
            tenant_id=sample_tenant.id
        )
        db_session.add(project)
        db_session.commit()
        
        assert project.id is not None
        assert project.name == "test-project"
        assert project.tenant_id == sample_tenant.id
        assert project.active is True
        assert project.archived is False
    
    def test_project_archive_methods(self, db_session, sample_project):
        """Test project archive/unarchive methods."""
        assert not sample_project.is_archived
        
        # Archive project
        sample_project.archive("test-user")
        assert sample_project.is_archived
        assert sample_project.archived is True
        assert sample_project.active is False
        
        # Unarchive project
        sample_project.unarchive("test-user")
        assert not sample_project.is_archived
        assert sample_project.archived is False
        assert sample_project.active is True


class TestProbeModel:
    """Test Probe model."""
    
    def test_create_probe(self, db_session):
        """Test creating a probe."""
        probe = Probe(
            code="test-probe",
            name="Test Probe",
            description="Test probe description",
            params={"test": "value"},
            version="1.0.0",
            category="security"
        )
        db_session.add(probe)
        db_session.commit()
        
        assert probe.id is not None
        assert probe.code == "test-probe"
        assert probe.name == "Test Probe"
        assert probe.params == {"test": "value"}
        assert probe.active is True
    
    def test_probe_unique_constraints(self, db_session):
        """Test probe unique constraints."""
        probe1 = Probe(code="unique-code", name="Test Probe", version="1.0.0")
        probe2 = Probe(code="unique-code", name="Different Name", version="2.0.0")  # Same code
        
        db_session.add(probe1)
        db_session.commit()
        
        db_session.add(probe2)
        with pytest.raises(IntegrityError):
            db_session.commit()


class TestProbesetModel:
    """Test Probeset model."""
    
    def test_create_probeset(self, db_session):
        """Test creating a probeset."""
        probeset = Probeset(
            code="test-probeset",
            name="Test Probeset",
            description="Test probeset",
            scoring_method="avg",
            scoring_config={"threshold": 0.8}
        )
        db_session.add(probeset)
        db_session.commit()
        
        assert probeset.id is not None
        assert probeset.code == "test-probeset"
        assert probeset.scoring_method == "avg"
        assert probeset.probe_count == 0
    
    def test_probeset_probe_count(self, db_session, sample_probeset, sample_probe):
        """Test probeset probe count property."""
        probeset_probe = ProbesetProbe(
            probeset_id=sample_probeset.id,
            probe_id=sample_probe.id,
            order=1,
            weight=1.0
        )
        db_session.add(probeset_probe)
        db_session.commit()
        db_session.refresh(sample_probeset)
        
        assert sample_probeset.probe_count == 1


class TestCapabilityModel:
    """Test Capability model."""
    
    def test_create_capability(self, db_session):
        """Test creating a capability."""
        capability = Capability(
            code="test-capability",
            name="Test Capability",
            description="Test capability description",
            category="security",
            severity="high"
        )
        db_session.add(capability)
        db_session.commit()
        
        assert capability.id is not None
        assert capability.code == "test-capability"
        assert capability.severity == "high"
        assert capability.active is True


class TestAssessmentModel:
    """Test Assessment model."""
    
    def test_create_assessment(self, db_session):
        """Test creating an assessment."""
        assessment = Assessment(
            code="test-assessment",
            name="Test Assessment",
            description="Test assessment",
            version="1.0.0",
            category="security"
        )
        db_session.add(assessment)
        db_session.commit()
        
        assert assessment.id is not None
        assert assessment.code == "test-assessment"
        assert assessment.capability_count == 0
    
    def test_assessment_capability_count(self, db_session, sample_assessment, sample_capability):
        """Test assessment capability count property."""
        assessment_capability = AssessmentCapability(
            assessment_id=sample_assessment.id,
            capability_id=sample_capability.id,
            weight=1.0
        )
        db_session.add(assessment_capability)
        db_session.commit()
        db_session.refresh(sample_assessment)
        
        assert sample_assessment.capability_count == 1


class TestEvaluationRunModel:
    """Test EvaluationRun model."""
    
    def test_create_evaluation_run(self, db_session, sample_project, sample_assessment):
        """Test creating an evaluation run."""
        eval_run = EvaluationRun(
            name="Test Evaluation",
            description="Test evaluation run",
            project_id=sample_project.id,
            assessment_id=sample_assessment.id,
            status="queued"
        )
        db_session.add(eval_run)
        db_session.commit()
        
        assert eval_run.id is not None
        assert eval_run.name == "Test Evaluation"
        assert eval_run.status == "queued"
        assert eval_run.progress == 0.0
    
    def test_evaluation_run_progress(self, db_session, sample_evaluation_run):
        """Test evaluation run progress calculation."""
        sample_evaluation_run.total_probes = 100
        sample_evaluation_run.completed_probes = 25
        
        assert sample_evaluation_run.progress == 25.0
    
    def test_evaluation_run_status_methods(self, db_session, sample_evaluation_run):
        """Test evaluation run status methods."""
        assert not sample_evaluation_run.is_running
        assert not sample_evaluation_run.is_completed
        
        # Start evaluation
        sample_evaluation_run.start()
        assert sample_evaluation_run.is_running
        assert sample_evaluation_run.status == "running"
        assert sample_evaluation_run.started_at is not None
        
        # Complete evaluation
        sample_evaluation_run.complete()
        assert sample_evaluation_run.is_completed
        assert sample_evaluation_run.status == "completed"
        assert sample_evaluation_run.completed_at is not None
        
        # Test duration
        assert sample_evaluation_run.duration is not None
        assert sample_evaluation_run.duration > 0


class TestRunProbeResultModel:
    """Test RunProbeResult model."""
    
    def test_create_probe_result(self, db_session, sample_evaluation_run):
        """Test creating a probe result (capability score) for a run model."""
        # Create a RunModel for the evaluation run and model
        # Use the model already created by complete_evaluation_setup (avoid creating another sample_model)
        from src.infrastructure.persistence.models import LLMModel
        existing_model = db_session.query(LLMModel).first()

        run_model = RunModel(
            evaluation_run_id=sample_evaluation_run.id,
            model_id=existing_model.id,
            model_params_snapshot={"temperature": 0.7},
            status="completed",
            completed_probes=0,
            failed_probes=0,
        )
        db_session.add(run_model)
        db_session.commit()
        db_session.refresh(run_model)

        # Fetch existing capability created by complete_evaluation_setup
        from src.infrastructure.persistence.models import Capability
        existing_capability = db_session.query(Capability).first()

        # Create RunProbeResult using correct foreign keys
        result = RunProbeResult(
            run_model_id=run_model.id,
            capability_id=existing_capability.id,
            score=0.85,
            probeset_count=1,
            details={"test": "data"},
        )
        db_session.add(result)
        db_session.commit()
        
        assert result.id is not None
        assert result.score == 0.85
        assert result.probeset_count == 1
    
    def test_probe_result_unique_constraint(self, db_session, sample_evaluation_run):
        """Test probe result unique constraint on (run_model_id, capability_id)."""
        # Create a RunModel for the evaluation run and model
        from src.infrastructure.persistence.models import LLMModel
        existing_model = db_session.query(LLMModel).first()

        run_model = RunModel(
            evaluation_run_id=sample_evaluation_run.id,
            model_id=existing_model.id,
            model_params_snapshot={"temperature": 0.7},
            status="completed",
        )
        db_session.add(run_model)
        db_session.commit()
        db_session.refresh(run_model)

        # Fetch existing capability created by complete_evaluation_setup
        from src.infrastructure.persistence.models import Capability
        existing_capability = db_session.query(Capability).first()

        result1 = RunProbeResult(
            run_model_id=run_model.id,
            capability_id=existing_capability.id,
            score=0.8,
        )
        result2 = RunProbeResult(
            run_model_id=run_model.id,
            capability_id=existing_capability.id,  # Same pair should conflict
            score=0.9,
        )
        
        db_session.add(result1)
        db_session.commit()
        
        db_session.add(result2)
        with pytest.raises(IntegrityError):
            db_session.commit()


class TestBaseEntityFunctionality:
    """Test BaseEntity common functionality."""
    
    def test_soft_delete(self, db_session, sample_driver):
        """Test soft delete functionality."""
        assert not sample_driver.is_deleted
        assert sample_driver.deleted_at is None
        
        # Soft delete
        sample_driver.soft_delete("test-user")
        assert sample_driver.is_deleted
        assert sample_driver.deleted_at is not None
        assert sample_driver.deleted_by == "test-user"
        
        # Restore
        sample_driver.restore()
        assert not sample_driver.is_deleted
        assert sample_driver.deleted_at is None
        assert sample_driver.deleted_by is None
    
    def test_audit_fields_update(self, db_session, sample_driver):
        """Test audit fields update."""
        original_updated_at = sample_driver.updated_at
        
        sample_driver.update_audit_fields("test-user")
        assert sample_driver.updated_at > original_updated_at
        assert sample_driver.updated_by == "test-user"
    
    def test_version_increment(self, db_session, sample_driver):
        """Test version increment."""
        original_version = sample_driver.version
        
        # BaseEntity does not define an increment method; increment directly
        sample_driver.version += 1
        assert sample_driver.version == original_version + 1
    
    def test_entity_equality(self, db_session):
        """Test entity equality based on ID."""
        driver1 = Driver(name="Test1", type="litellm")
        driver2 = Driver(name="Test2", type="openai")
        
        # Same ID should be equal
        test_id = uuid.uuid4()
        driver1.id = test_id
        driver2.id = test_id
        
        assert driver1 == driver2
        
        # Different IDs should not be equal
        driver2.id = uuid.uuid4()
        assert driver1 != driver2