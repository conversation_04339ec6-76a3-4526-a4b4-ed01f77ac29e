import pytest

from types import SimpleNamespace

from src.core import dependencies as deps


@pytest.mark.asyncio
async def test_get_uow_yields_context_managed_uow(monkeypatch):
    calls = SimpleNamespace(entered=False, exited=False)

    class FakeUoW:
        def __init__(self):
            self.repositories = object()

        async def __aenter__(self):
            calls.entered = True
            return self

        async def __aexit__(self, exc_type, exc, tb):
            calls.exited = True

    # Patch the UnitOfWork symbol used inside dependencies.get_uow
    monkeypatch.setattr(deps, "UnitOfWork", FakeUoW)

    # Collect yielded value from dependency generator
    gen = deps.get_uow()
    tx = await gen.__anext__()

    assert isinstance(tx, FakeUoW)
    assert calls.entered is True
    assert calls.exited is False  # not exited until generator is closed

    # Closing the generator should trigger __aexit__
    with pytest.raises(StopAsyncIteration):
        await gen.__anext__()
    assert calls.exited is True


@pytest.mark.asyncio
async def test_get_db_session_yields_and_closes(monkeypatch):
    calls = SimpleNamespace(iterated=0, closed=False)

    class FakeSession:  # simple sentinel
        pass

    async def fake_session_gen():
        try:
            calls.iterated += 1
            yield FakeSession()
        finally:
            calls.closed = True

    # Patch the get_database_session symbol used inside dependencies.get_db_session
    monkeypatch.setattr(deps, "get_database_session", fake_session_gen)

    gen = deps.get_db_session()
    session = await gen.__anext__()
    assert isinstance(session, FakeSession)
    assert calls.iterated == 1
    assert calls.closed is False

    with pytest.raises(StopAsyncIteration):
        await gen.__anext__()
    assert calls.closed is True
