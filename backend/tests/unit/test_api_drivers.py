"""
Unit tests for Driver API endpoints.
"""

import pytest
import uuid
from fastapi.testclient import TestClient

from src.infrastructure.persistence.models import <PERSON>, Provider, LLMModel, ModelDefaultParam


class TestDriverAPI:
    """Test Driver API endpoints."""
    
    def test_list_drivers_empty(self, client):
        """Test listing drivers when none exist."""
        response = client.get("/api/v1/drivers")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 0
        assert data['items'] == []
        assert data['page'] == 1
        assert data['per_page'] == 20
    
    def test_create_driver(self, client):
        """Test creating a driver."""
        driver_data = {
            "name": "Test Driver",
            "type": "litellm",
            "description": "Test driver for API testing",
            "default_config": {"timeout": 30},
            "active": True
        }
        
        response = client.post("/api/v1/drivers", json=driver_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data['name'] == "Test Driver"
        assert data['type'] == "litellm"
        assert data['description'] == "Test driver for API testing"
        assert data['active'] is True
        assert 'id' in data
        assert 'created_at' in data
    
    def test_create_driver_invalid_type(self, client):
        """Test creating driver with invalid type."""
        driver_data = {
            "name": "Invalid Driver",
            "type": "invalid-type",
            "description": "This should fail"
        }
        
        response = client.post("/api/v1/drivers", json=driver_data)
        assert response.status_code == 422  # Validation error
    
    def test_list_drivers_with_data(self, client, sample_driver):
        """Test listing drivers with data."""
        response = client.get("/api/v1/drivers")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert len(data['items']) == 1
        assert data['items'][0]['name'] == sample_driver.name
    
    def test_get_driver(self, client, sample_driver):
        """Test getting a specific driver."""
        response = client.get(f"/api/v1/drivers/{sample_driver.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data['id'] == str(sample_driver.id)
        assert data['name'] == sample_driver.name
        assert data['type'] == sample_driver.type
    
    def test_get_driver_not_found(self, client):
        """Test getting non-existent driver."""
        fake_id = uuid.uuid4()
        response = client.get(f"/api/v1/drivers/{fake_id}")
        assert response.status_code == 404
    
    def test_update_driver(self, client, sample_driver):
        """Test updating a driver."""
        update_data = {
            "name": "Updated Driver",
            "description": "Updated description"
        }
        
        response = client.put(f"/api/v1/drivers/{sample_driver.id}", json=update_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data['name'] == "Updated Driver"
        assert data['description'] == "Updated description"
        assert data['type'] == sample_driver.type  # Should remain unchanged
    
    def test_delete_driver(self, client, sample_driver):
        """Test deleting a driver (soft delete)."""
        response = client.delete(f"/api/v1/drivers/{sample_driver.id}")
        assert response.status_code == 204
        
        # Verify driver is soft deleted
        response = client.get(f"/api/v1/drivers/{sample_driver.id}")
        assert response.status_code == 404  # Should not be found after soft delete
    
    def test_list_drivers_with_search(self, client):
        """Test listing drivers with search."""
        # Create test drivers
        driver1_data = {"name": "OpenAI Driver", "type": "openai"}
        driver2_data = {"name": "Anthropic Driver", "type": "anthropic"}
        
        client.post("/api/v1/drivers", json=driver1_data)
        client.post("/api/v1/drivers", json=driver2_data)
        
        # Search by name
        response = client.get("/api/v1/drivers?search=OpenAI")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert data['items'][0]['name'] == "OpenAI Driver"
    
    def test_list_drivers_with_type_filter(self, client):
        """Test listing drivers with type filter."""
        # Create test drivers
        driver1_data = {"name": "OpenAI Driver", "type": "openai"}
        driver2_data = {"name": "LiteLLM Driver", "type": "litellm"}
        
        client.post("/api/v1/drivers", json=driver1_data)
        client.post("/api/v1/drivers", json=driver2_data)
        
        # Filter by type
        response = client.get("/api/v1/drivers?type_filter=openai")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert data['items'][0]['type'] == "openai"


class TestProviderAPI:
    """Test Provider API endpoints."""
    
    def test_create_provider(self, client, sample_driver):
        """Test creating a provider."""
        provider_data = {
            "name": "Test Provider",
            "url": "https://api.test.com",
            "api_key": "test-api-key",
            "max_concurrent_requests": 50,
            "driver_id": str(sample_driver.id)
        }
        
        response = client.post("/api/v1/providers", json=provider_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data['name'] == "Test Provider"
        assert data['url'] == "https://api.test.com"
        assert data['api_key'] == '***'  # Should be masked
        assert data['max_concurrent_requests'] == 50
        assert data['driver_id'] == str(sample_driver.id)
    
    def test_create_provider_invalid_driver(self, client):
        """Test creating provider with invalid driver ID."""
        provider_data = {
            "name": "Test Provider",
            "driver_id": str(uuid.uuid4()),  # Non-existent driver
            "max_concurrent_requests": 10
        }
        
        response = client.post("/api/v1/providers", json=provider_data)
        assert response.status_code == 404  # Driver not found
    
    def test_list_providers(self, client, sample_provider):
        """Test listing providers."""
        response = client.get("/api/v1/providers")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert len(data['items']) == 1
        assert data['items'][0]['name'] == sample_provider.name
        assert data['items'][0]['api_key'] == '***'  # Should be masked
    
    def test_list_providers_with_driver_filter(self, client, sample_driver, sample_provider):
        """Test listing providers with driver filter."""
        response = client.get(f"/api/v1/providers?driver_id={sample_driver.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert data['items'][0]['driver_id'] == str(sample_driver.id)
    
    def test_get_provider(self, client, sample_provider):
        """Test getting a specific provider."""
        response = client.get(f"/api/v1/providers/{sample_provider.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data['id'] == str(sample_provider.id)
        assert data['name'] == sample_provider.name
        assert data['api_key'] == '***'  # Should be masked
    
    def test_update_provider(self, client, sample_provider):
        """Test updating a provider."""
        update_data = {
            "name": "Updated Provider",
            "max_concurrent_requests": 100
        }
        
        response = client.put(f"/api/v1/providers/{sample_provider.id}", json=update_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data['name'] == "Updated Provider"
        assert data['max_concurrent_requests'] == 100
    
    def test_delete_provider(self, client, sample_provider):
        """Test deleting a provider."""
        response = client.delete(f"/api/v1/providers/{sample_provider.id}")
        assert response.status_code == 204
        
        # Verify provider is soft deleted
        response = client.get(f"/api/v1/providers/{sample_provider.id}")
        assert response.status_code == 404


class TestLLMModelAPI:
    """Test LLM Model API endpoints."""
    
    def test_create_model(self, client, sample_provider):
        """Test creating an LLM model."""
        model_data = {
            "name": "test-gpt",
            "version": "4.0",
            "description": "Test GPT model",
            "requests_per_minute": 500,
            "tokens_per_minute": 150000,
            "max_tokens": 4096,
            "supports_functions": True,
            "supports_vision": True,
            "provider_id": str(sample_provider.id),
            "active": True
        }
        
        response = client.post("/api/v1/models", json=model_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data['name'] == "test-gpt"
        assert data['version'] == "4.0"
        assert data['full_name'] == "test-gpt:4.0"
        assert data['supports_functions'] is True
        assert data['supports_vision'] is True
        assert data['provider_id'] == str(sample_provider.id)
    
    def test_create_model_invalid_provider(self, client):
        """Test creating model with invalid provider ID."""
        model_data = {
            "name": "test-model",
            "provider_id": str(uuid.uuid4())  # Non-existent provider
        }
        
        response = client.post("/api/v1/models", json=model_data)
        assert response.status_code == 404  # Provider not found
    
    def test_list_models(self, client, sample_model):
        """Test listing LLM models."""
        response = client.get("/api/v1/models")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert len(data['items']) == 1
        assert data['items'][0]['name'] == sample_model.name
    
    def test_list_models_with_filters(self, client, sample_model):
        """Test listing models with filters."""
        # Test provider filter
        response = client.get(f"/api/v1/models?provider_id={sample_model.provider_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        
        # Test function support filter
        response = client.get("/api/v1/models?supports_functions=true")
        assert response.status_code == 200
        
        # Test vision support filter  
        response = client.get("/api/v1/models?supports_vision=true")
        assert response.status_code == 200
    
    def test_get_model(self, client, sample_model):
        """Test getting a specific model."""
        response = client.get(f"/api/v1/models/{sample_model.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data['id'] == str(sample_model.id)
        assert data['name'] == sample_model.name
        assert data['full_name'] == sample_model.full_name
    
    def test_update_model(self, client, sample_model):
        """Test updating an LLM model."""
        update_data = {
            "description": "Updated model description",
            "max_tokens": 8192
        }
        
        response = client.put(f"/api/v1/models/{sample_model.id}", json=update_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data['description'] == "Updated model description"
        assert data['max_tokens'] == 8192
    
    def test_delete_model(self, client, sample_model):
        """Test deleting an LLM model."""
        response = client.delete(f"/api/v1/models/{sample_model.id}")
        assert response.status_code == 204
        
        # Verify model is soft deleted
        response = client.get(f"/api/v1/models/{sample_model.id}")
        assert response.status_code == 404


class TestModelDefaultParamAPI:
    """Test Model Default Parameter API endpoints."""
    
    def test_create_model_param(self, client, sample_model):
        """Test creating a model parameter."""
        param_data = {
            "key": "temperature",
            "value": "0.8",
            "param_type": "float",
            "description": "Model temperature parameter",
            "model_id": str(sample_model.id)
        }
        
        response = client.post(f"/api/v1/models/{sample_model.id}/params", json=param_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data['key'] == "temperature"
        assert data['value'] == "0.8"
        assert data['typed_value'] == 0.8
        assert data['param_type'] == "float"
        assert data['model_id'] == str(sample_model.id)
    
    def test_list_model_params(self, client, sample_model, db_session):
        """Test listing model parameters."""
        # Create test parameters
        param1 = ModelDefaultParam(
            model_id=sample_model.id,
            key="temperature",
            value="0.7",
            param_type="float"
        )
        param2 = ModelDefaultParam(
            model_id=sample_model.id,
            key="max_tokens",
            value="2000",
            param_type="integer"
        )
        db_session.add_all([param1, param2])
        db_session.commit()
        
        response = client.get(f"/api/v1/models/{sample_model.id}/params")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 2
        assert len(data['items']) == 2
    
    def test_get_model_param(self, client, sample_model, db_session):
        """Test getting a specific model parameter."""
        param = ModelDefaultParam(
            model_id=sample_model.id,
            key="temperature",
            value="0.7",
            param_type="float"
        )
        db_session.add(param)
        db_session.commit()
        
        response = client.get(f"/api/v1/params/{param.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data['id'] == str(param.id)
        assert data['key'] == "temperature"
        assert data['typed_value'] == 0.7
    
    def test_update_model_param(self, client, sample_model, db_session):
        """Test updating a model parameter."""
        param = ModelDefaultParam(
            model_id=sample_model.id,
            key="temperature",
            value="0.7",
            param_type="float"
        )
        db_session.add(param)
        db_session.commit()
        
        update_data = {
            "value": "0.9",
            "description": "Updated temperature"
        }
        
        response = client.put(f"/api/v1/params/{param.id}", json=update_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data['value'] == "0.9"
        assert data['typed_value'] == 0.9
        assert data['description'] == "Updated temperature"
    
    def test_delete_model_param(self, client, sample_model, db_session):
        """Test deleting a model parameter."""
        param = ModelDefaultParam(
            model_id=sample_model.id,
            key="temperature",
            value="0.7",
            param_type="float"
        )
        db_session.add(param)
        db_session.commit()
        
        response = client.delete(f"/api/v1/params/{param.id}")
        assert response.status_code == 204
        
        # Verify parameter is soft deleted
        response = client.get(f"/api/v1/params/{param.id}")
        assert response.status_code == 404


class TestAPIValidation:
    """Test API input validation."""
    
    def test_driver_validation_errors(self, client):
        """Test driver creation validation errors."""
        # Missing required fields
        response = client.post("/api/v1/drivers", json={})
        assert response.status_code == 422
        
        # Invalid field lengths
        response = client.post("/api/v1/drivers", json={
            "name": "",  # Empty name
            "type": "litellm"
        })
        assert response.status_code == 422
        
        # Invalid type
        response = client.post("/api/v1/drivers", json={
            "name": "Test Driver",
            "type": "invalid-type"
        })
        assert response.status_code == 422
    
    def test_provider_validation_errors(self, client, sample_driver):
        """Test provider creation validation errors."""
        # Negative max_concurrent_requests
        response = client.post("/api/v1/providers", json={
            "name": "Test Provider",
            "max_concurrent_requests": -1,
            "driver_id": str(sample_driver.id)
        })
        assert response.status_code == 422
    
    def test_model_validation_errors(self, client, sample_provider):
        """Test model creation validation errors."""
        # Negative rate limits
        response = client.post("/api/v1/models", json={
            "name": "Test Model",
            "requests_per_minute": -1,
            "provider_id": str(sample_provider.id)
        })
        assert response.status_code == 422
    
    def test_pagination_validation(self, client):
        """Test pagination parameter validation."""
        # Invalid page number
        response = client.get("/api/v1/drivers?page=0")
        assert response.status_code == 422
        
        # Invalid per_page
        response = client.get("/api/v1/drivers?per_page=101")  # Over limit
        assert response.status_code == 422