"""
Unit tests for Evaluation API endpoints.
"""

import pytest
import uuid
from fastapi.testclient import TestClient

from src.infrastructure.persistence.models import (
    Probe, Probeset, ProbesetProbe, Capability, CapabilitySet,
    Assessment, AssessmentCapability
)


class TestProbeAPI:
    """Test Probe API endpoints."""
    
    def test_create_probe(self, client):
        """Test creating a probe."""
        probe_data = {
            "code": "test-probe",
            "name": "Test Probe",
            "description": "Test probe for API testing",
            "params": {"pattern": "test.*pattern"},
            "version": "1.0.0",
            "category": "security",
            "tags": ["security", "test"],
            "active": True
        }
        
        response = client.post("/api/v1/probes", json=probe_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data['code'] == "test-probe"
        assert data['name'] == "Test Probe"
        assert data['params'] == {"pattern": "test.*pattern"}
        assert data['tags'] == ["security", "test"]
        assert data['active'] is True
        assert 'id' in data
    
    def test_create_probe_duplicate_code(self, client, sample_probe):
        """Test creating probe with duplicate code."""
        probe_data = {
            "code": sample_probe.code,  # Same code
            "name": "Different Name",
            "version": "2.0.0"
        }
        
        response = client.post("/api/v1/probes", json=probe_data)
        assert response.status_code == 400  # Should fail due to unique constraint
    
    def test_list_probes(self, client, sample_probe):
        """Test listing probes."""
        response = client.get("/api/v1/probes")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert len(data['items']) == 1
        assert data['items'][0]['code'] == sample_probe.code
    
    def test_list_probes_with_filters(self, client):
        """Test listing probes with filters."""
        # Create test probes
        probe1_data = {
            "code": "security-probe",
            "name": "Security Probe",
            "category": "security",
            "tags": ["security", "prompt-leaking"]
        }
        probe2_data = {
            "code": "safety-probe", 
            "name": "Safety Probe",
            "category": "safety",
            "tags": ["safety", "harmful-content"]
        }
        
        client.post("/api/v1/probes", json=probe1_data)
        client.post("/api/v1/probes", json=probe2_data)
        
        # Filter by category
        response = client.get("/api/v1/probes?category=security")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert data['items'][0]['category'] == "security"
        
        # Search by name
        response = client.get("/api/v1/probes?search=Safety")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert data['items'][0]['name'] == "Safety Probe"
    
    def test_get_probe(self, client, sample_probe):
        """Test getting a specific probe."""
        response = client.get(f"/api/v1/probes/{sample_probe.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data['id'] == str(sample_probe.id)
        assert data['code'] == sample_probe.code
        assert data['name'] == sample_probe.name
    
    def test_update_probe(self, client, sample_probe):
        """Test updating a probe."""
        update_data = {
            "description": "Updated probe description",
            "params": {"new_param": "new_value"}
        }
        
        response = client.put(f"/api/v1/probes/{sample_probe.id}", json=update_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data['description'] == "Updated probe description"
        assert data['params'] == {"new_param": "new_value"}
        assert data['code'] == sample_probe.code  # Should remain unchanged
    
    def test_delete_probe(self, client, sample_probe):
        """Test deleting a probe."""
        response = client.delete(f"/api/v1/probes/{sample_probe.id}")
        assert response.status_code == 204
        
        # Verify probe is soft deleted
        response = client.get(f"/api/v1/probes/{sample_probe.id}")
        assert response.status_code == 404


class TestProbesetAPI:
    """Test Probeset API endpoints."""
    
    def test_create_probeset(self, client):
        """Test creating a probeset."""
        probeset_data = {
            "code": "test-probeset",
            "name": "Test Probeset",
            "description": "Test probeset for API testing",
            "version": "1.0.0",
            "scoring_method": "weighted",
            "scoring_config": {"threshold": 0.8},
            "category": "security",
            "tags": ["security", "comprehensive"],
            "active": True
        }
        
        response = client.post("/api/v1/probesets", json=probeset_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data['code'] == "test-probeset"
        assert data['name'] == "Test Probeset"
        assert data['scoring_method'] == "weighted"
        assert data['scoring_config'] == {"threshold": 0.8}
        assert data['active'] is True
    
    def test_create_probeset_invalid_scoring_method(self, client):
        """Test creating probeset with invalid scoring method."""
        probeset_data = {
            "code": "test-probeset",
            "name": "Test Probeset",
            "scoring_method": "invalid-method"
        }
        
        response = client.post("/api/v1/probesets", json=probeset_data)
        assert response.status_code == 422  # Validation error
    
    def test_list_probesets(self, client, sample_probeset):
        """Test listing probesets."""
        response = client.get("/api/v1/probesets")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert len(data['items']) == 1
        assert data['items'][0]['code'] == sample_probeset.code
    
    def test_list_probesets_with_scoring_filter(self, client):
        """Test listing probesets with scoring method filter."""
        # Create probesets with different scoring methods
        probeset1_data = {
            "code": "avg-probeset",
            "name": "Average Probeset",
            "scoring_method": "avg"
        }
        probeset2_data = {
            "code": "binary-probeset",
            "name": "Binary Probeset", 
            "scoring_method": "binary"
        }
        
        client.post("/api/v1/probesets", json=probeset1_data)
        client.post("/api/v1/probesets", json=probeset2_data)
        
        # Filter by scoring method
        response = client.get("/api/v1/probesets?scoring_method=binary")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert data['items'][0]['scoring_method'] == "binary"
    
    def test_get_probeset(self, client, sample_probeset):
        """Test getting a specific probeset."""
        response = client.get(f"/api/v1/probesets/{sample_probeset.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data['id'] == str(sample_probeset.id)
        assert data['code'] == sample_probeset.code
    
    def test_update_probeset(self, client, sample_probeset):
        """Test updating a probeset."""
        update_data = {
            "scoring_method": "binary",
            "scoring_config": {"pass_threshold": 1.0}
        }
        
        response = client.put(f"/api/v1/probesets/{sample_probeset.id}", json=update_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data['scoring_method'] == "binary"
        assert data['scoring_config'] == {"pass_threshold": 1.0}


class TestProbesetProbeAPI:
    """Test Probeset-Probe association API endpoints."""
    
    def test_add_probe_to_probeset(self, client, sample_probeset, sample_probe):
        """Test adding a probe to a probeset."""
        association_data = {
            "probe_id": str(sample_probe.id),
            "order": 1,
            "weight": 2.0,
            "override_params": {"custom": "value"},
            "enabled": True
        }
        
        response = client.post(
            f"/api/v1/probesets/{sample_probeset.id}/probes",
            json=association_data
        )
        assert response.status_code == 201
        
        data = response.json()
        assert data['probeset_id'] == str(sample_probeset.id)
        assert data['probe_id'] == str(sample_probe.id)
        assert data['order'] == 1
        assert data['weight'] == 2.0
        assert data['override_params'] == {"custom": "value"}
        assert data['enabled'] is True
    
    def test_list_probeset_probes(self, client, sample_probeset, sample_probe, db_session):
        """Test listing probes in a probeset."""
        # Create association
        association = ProbesetProbe(
            probeset_id=sample_probeset.id,
            probe_id=sample_probe.id,
            order=1,
            weight=1.0
        )
        db_session.add(association)
        db_session.commit()
        
        response = client.get(f"/api/v1/probesets/{sample_probeset.id}/probes")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 1
        assert data[0]['probeset_id'] == str(sample_probeset.id)
        assert data[0]['probe_id'] == str(sample_probe.id)
    
    def test_update_probeset_probe(self, client, sample_probeset, sample_probe, db_session):
        """Test updating probe configuration in probeset."""
        # Create association
        association = ProbesetProbe(
            probeset_id=sample_probeset.id,
            probe_id=sample_probe.id,
            order=1,
            weight=1.0
        )
        db_session.add(association)
        db_session.commit()
        
        update_data = {
            "weight": 3.0,
            "order": 2
        }
        
        response = client.put(
            f"/api/v1/probesets/{sample_probeset.id}/probes/{sample_probe.id}",
            json=update_data
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data['weight'] == 3.0
        assert data['order'] == 2
    
    def test_remove_probe_from_probeset(self, client, sample_probeset, sample_probe, db_session):
        """Test removing probe from probeset."""
        # Create association
        association = ProbesetProbe(
            probeset_id=sample_probeset.id,
            probe_id=sample_probe.id,
            order=1,
            weight=1.0
        )
        db_session.add(association)
        db_session.commit()
        
        response = client.delete(
            f"/api/v1/probesets/{sample_probeset.id}/probes/{sample_probe.id}"
        )
        assert response.status_code == 204
        
        # Verify association is removed
        response = client.get(f"/api/v1/probesets/{sample_probeset.id}/probes")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 0


class TestCapabilityAPI:
    """Test Capability API endpoints."""
    
    def test_create_capability(self, client):
        """Test creating a capability."""
        capability_data = {
            "code": "test-capability",
            "name": "Test Capability",
            "description": "Test capability for API testing",
            "category": "security",
            "severity": "high",
            "active": True
        }
        
        response = client.post("/api/v1/capabilities", json=capability_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data['code'] == "test-capability"
        assert data['name'] == "Test Capability"
        assert data['severity'] == "high"
        assert data['active'] is True
    
    def test_create_capability_invalid_severity(self, client):
        """Test creating capability with invalid severity."""
        capability_data = {
            "code": "test-capability",
            "name": "Test Capability",
            "severity": "invalid-severity"
        }
        
        response = client.post("/api/v1/capabilities", json=capability_data)
        assert response.status_code == 422  # Validation error
    
    def test_list_capabilities(self, client, sample_capability):
        """Test listing capabilities."""
        response = client.get("/api/v1/capabilities")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert len(data['items']) == 1
        assert data['items'][0]['code'] == sample_capability.code
    
    def test_list_capabilities_with_severity_filter(self, client):
        """Test listing capabilities with severity filter."""
        # Create capabilities with different severities
        cap1_data = {
            "code": "high-cap",
            "name": "High Severity Cap",
            "severity": "high"
        }
        cap2_data = {
            "code": "critical-cap", 
            "name": "Critical Severity Cap",
            "severity": "critical"
        }
        
        client.post("/api/v1/capabilities", json=cap1_data)
        client.post("/api/v1/capabilities", json=cap2_data)
        
        # Filter by severity
        response = client.get("/api/v1/capabilities?severity=critical")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert data['items'][0]['severity'] == "critical"
    
    def test_get_capability(self, client, sample_capability):
        """Test getting a specific capability."""
        response = client.get(f"/api/v1/capabilities/{sample_capability.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data['id'] == str(sample_capability.id)
        assert data['code'] == sample_capability.code
    
    def test_update_capability(self, client, sample_capability):
        """Test updating a capability."""
        update_data = {
            "description": "Updated capability description",
            "severity": "critical"
        }
        
        response = client.put(f"/api/v1/capabilities/{sample_capability.id}", json=update_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data['description'] == "Updated capability description"
        assert data['severity'] == "critical"


class TestAssessmentAPI:
    """Test Assessment API endpoints."""
    
    def test_create_assessment(self, client):
        """Test creating an assessment."""
        assessment_data = {
            "code": "test-assessment",
            "name": "Test Assessment",
            "description": "Test assessment for API testing",
            "version": "1.0.0",
            "category": "security",
            "active": True
        }
        
        response = client.post("/api/v1/assessments", json=assessment_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data['code'] == "test-assessment"
        assert data['name'] == "Test Assessment"
        assert data['version'] == "1.0.0"
        assert data['active'] is True
    
    def test_create_assessment_duplicate_code(self, client, sample_assessment):
        """Test creating assessment with duplicate code."""
        assessment_data = {
            "code": sample_assessment.code,  # Same code
            "name": "Different Name"
        }
        
        response = client.post("/api/v1/assessments", json=assessment_data)
        assert response.status_code == 400  # Should fail due to unique constraint
    
    def test_list_assessments(self, client, sample_assessment):
        """Test listing assessments."""
        response = client.get("/api/v1/assessments")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert len(data['items']) == 1
        assert data['items'][0]['code'] == sample_assessment.code
    
    def test_list_assessments_with_category_filter(self, client):
        """Test listing assessments with category filter."""
        # Create assessments with different categories
        assessment1_data = {
            "code": "security-assessment",
            "name": "Security Assessment",
            "category": "security"
        }
        assessment2_data = {
            "code": "safety-assessment",
            "name": "Safety Assessment", 
            "category": "safety"
        }
        
        client.post("/api/v1/assessments", json=assessment1_data)
        client.post("/api/v1/assessments", json=assessment2_data)
        
        # Filter by category
        response = client.get("/api/v1/assessments?category=safety")
        assert response.status_code == 200
        
        data = response.json()
        assert data['total'] == 1
        assert data['items'][0]['category'] == "safety"
    
    def test_get_assessment(self, client, sample_assessment):
        """Test getting a specific assessment."""
        response = client.get(f"/api/v1/assessments/{sample_assessment.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data['id'] == str(sample_assessment.id)
        assert data['code'] == sample_assessment.code
    
    def test_update_assessment(self, client, sample_assessment):
        """Test updating an assessment."""
        update_data = {
            "description": "Updated assessment description",
            "category": "comprehensive"
        }
        
        response = client.put(f"/api/v1/assessments/{sample_assessment.id}", json=update_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data['description'] == "Updated assessment description"
        assert data['category'] == "comprehensive"
    
    def test_delete_assessment(self, client, sample_assessment):
        """Test deleting an assessment."""
        response = client.delete(f"/api/v1/assessments/{sample_assessment.id}")
        assert response.status_code == 204
        
        # Verify assessment is soft deleted
        response = client.get(f"/api/v1/assessments/{sample_assessment.id}")
        assert response.status_code == 404


class TestAPIErrorHandling:
    """Test API error handling."""
    
    def test_not_found_errors(self, client):
        """Test 404 responses for non-existent resources."""
        fake_id = uuid.uuid4()
        
        endpoints = [
            f"/api/v1/probes/{fake_id}",
            f"/api/v1/probesets/{fake_id}",
            f"/api/v1/capabilities/{fake_id}",
            f"/api/v1/assessments/{fake_id}"
        ]
        
        for endpoint in endpoints:
            response = client.get(endpoint)
            assert response.status_code == 404
    
    def test_validation_errors(self, client):
        """Test validation error responses."""
        # Test invalid enum values
        invalid_data = [
            ("/api/v1/probes", {"code": "test", "name": "Test"}),  # Missing required fields
            ("/api/v1/probesets", {"code": "test", "name": "Test", "scoring_method": "invalid"}),
            ("/api/v1/capabilities", {"code": "test", "name": "Test", "severity": "invalid"}),
        ]
        
        for endpoint, data in invalid_data:
            response = client.post(endpoint, json=data)
            assert response.status_code in [400, 422]  # Bad request or validation error
    
    def test_constraint_violation_errors(self, client, sample_probe):
        """Test constraint violation errors."""
        # Try to create probe with same code
        duplicate_data = {
            "code": sample_probe.code,
            "name": "Different Name"
        }
        
        response = client.post("/api/v1/probes", json=duplicate_data)
        assert response.status_code == 400
        
        # Check error message
        data = response.json()
        assert "already exists" in data.get('detail', '').lower()