import pytest
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from src.domain.models.base import Base
from src.infrastructure.uow import UnitOfWork
from src.infrastructure.repositories.base import BaseRepository
from src.domain.specifications.base import (
    FieldEquals,
    FieldIn,
    AndSpecification,
    OrSpecification,
    NotSpecification,
    DateRange,
)
from src.infrastructure.persistence.models.organization import Tenant, Project
from src.core.types import PaginationParams, SortParam, SortDirection


@pytest.mark.asyncio
async def test_specifications_and_or_not_composition():
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    session_factory = async_sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False, autoflush=False
    )

    async with UnitOfWork(session_factory=session_factory) as uow:
        tenant_repo: BaseRepository[Tenant] = uow.repositories.get_repository(Tenant)
        project_repo: BaseRepository[Project] = uow.repositories.get_repository(Project)

        tenant = Tenant(name="t-spec-combo", display_name="Specs")
        await tenant_repo.create(tenant)

        names = ["alpha", "beta", "gamma", "delta"]
        await project_repo.bulk_create([Project(name=n, tenant_id=tenant.id) for n in names])
        await uow.commit()

    async with UnitOfWork(session_factory=session_factory) as uow:
        project_repo = uow.repositories.get_repository(Project)

        # (name in {alpha, beta} AND NOT name == beta) OR name == gamma -> {alpha, gamma}
        spec = OrSpecification(
            AndSpecification(FieldIn("name", ["alpha", "beta"]), NotSpecification(FieldEquals("name", "beta"))),
            FieldEquals("name", "gamma"),
        )

        result = await project_repo.list_by_spec(
            spec,
            pagination=PaginationParams(page=1, per_page=10),
            sort=SortParam(field="name", direction=SortDirection.ASC),
        )
        assert [p.name for p in result.items] == ["alpha", "gamma"]

    await engine.dispose()


@pytest.mark.asyncio
async def test_specifications_invalid_field_raises_attribute_error():
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    session_factory = async_sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False, autoflush=False
    )

    async with UnitOfWork(session_factory=session_factory) as uow:
        tenant_repo: BaseRepository[Tenant] = uow.repositories.get_repository(Tenant)
        project_repo: BaseRepository[Project] = uow.repositories.get_repository(Project)

        tenant = Tenant(name="t-spec-invalid", display_name="SpecsInvalid")
        await tenant_repo.create(tenant)
        await project_repo.create(Project(name="omega", tenant_id=tenant.id))

        # Using an invalid field in spec should propagate AttributeError
        with pytest.raises(AttributeError):
            await project_repo.list_by_spec(FieldEquals("does_not_exist", "x"))

    await engine.dispose()


@pytest.mark.asyncio
async def test_specifications_date_range_inclusive_exclusive():
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    session_factory = async_sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False, autoflush=False
    )

    now = datetime.utcnow()
    t0 = now - timedelta(hours=2)
    t1 = now - timedelta(hours=1)
    t2 = now

    async with UnitOfWork(session_factory=session_factory) as uow:
        tenant_repo: BaseRepository[Tenant] = uow.repositories.get_repository(Tenant)
        project_repo: BaseRepository[Project] = uow.repositories.get_repository(Project)

        tenant = Tenant(name="t-spec-daterange", display_name="SpecsDate")
        await tenant_repo.create(tenant)

        p0 = Project(name="p0", tenant_id=tenant.id)
        p1 = Project(name="p1", tenant_id=tenant.id)
        p2 = Project(name="p2", tenant_id=tenant.id)
        await project_repo.bulk_create([p0, p1, p2])

        # Manually set created_at times for deterministic DateRange tests
        # Note: BaseEntity.created_at has server_default, but we can override then flush
        p0.created_at = t0
        p1.created_at = t1
        p2.created_at = t2
        await uow.session.flush()
        await uow.commit()

    async with UnitOfWork(session_factory=session_factory) as uow:
        project_repo = uow.repositories.get_repository(Project)

        # Inclusive range [t1, t2] should include p1 and p2
        inclusive_spec = DateRange(field="created_at", start=t1, end=t2, inclusive=True)
        inclusive = await project_repo.list_by_spec(
            inclusive_spec,
            pagination=PaginationParams(page=1, per_page=10),
            sort=SortParam(field="name", direction=SortDirection.ASC),
        )
        assert [p.name for p in inclusive.items] == ["p1", "p2"]

        # Exclusive range (t1, t2) should exclude exact endpoints -> none match in current data
        exclusive_spec = DateRange(field="created_at", start=t1, end=t2, inclusive=False)
        exclusive = await project_repo.list_by_spec(
            exclusive_spec,
            pagination=PaginationParams(page=1, per_page=10),
            sort=SortParam(field="name", direction=SortDirection.ASC),
        )
        assert exclusive.total == 0

    await engine.dispose()
