import pytest
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from src.domain.models.base import Base
from src.infrastructure.uow import UnitOfWork
from src.infrastructure.repositories.base import BaseRepository
from src.domain.specifications.base import (
    FieldEquals,
    FieldIn,
)
from src.infrastructure.persistence.models.organization import Tenant, Project
from src.core.types import PaginationParams, SortParam, SortDirection


@pytest.mark.asyncio
async def test_repository_count_and_exists_by_spec():
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    session_factory = async_sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False, autoflush=False
    )

    async with UnitOfWork(session_factory=session_factory) as uow:
        tenant_repo: BaseRepository[Tenant] = uow.repositories.get_repository(Tenant)
        project_repo: BaseRepository[Project] = uow.repositories.get_repository(Project)

        tenant = Tenant(name="t-count-exists", display_name="T")
        await tenant_repo.create(tenant)

        p1 = Project(name="alpha", tenant_id=tenant.id)
        p2 = Project(name="beta", tenant_id=tenant.id)
        p3 = Project(name="gamma", tenant_id=tenant.id)
        await project_repo.bulk_create([p1, p2, p3])
        await uow.commit()

    # Count by spec
    async with UnitOfWork(session_factory=session_factory) as uow:
        project_repo = uow.repositories.get_repository(Project)
        spec = FieldIn(field="name", values=["alpha", "beta"])  # 2 matches
        count = await project_repo.count_by_spec(spec)
        assert count == 2

        # exists_by_spec
        exists_alpha = await project_repo.exists_by_spec(FieldEquals("name", "alpha"))
        exists_delta = await project_repo.exists_by_spec(FieldEquals("name", "delta"))
        assert exists_alpha is True
        assert exists_delta is False

    await engine.dispose()


@pytest.mark.asyncio
async def test_repository_spec_composition_sorting_and_pagination_with_soft_delete():
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    session_factory = async_sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False, autoflush=False
    )

    async with UnitOfWork(session_factory=session_factory) as uow:
        tenant_repo: BaseRepository[Tenant] = uow.repositories.get_repository(Tenant)
        project_repo: BaseRepository[Project] = uow.repositories.get_repository(Project)

        tenant1 = Tenant(name="t-combo-1", display_name="Combo 1")
        tenant2 = Tenant(name="t-combo-2", display_name="Combo 2")
        await tenant_repo.create(tenant1)
        await tenant_repo.create(tenant2)

        # Create projects: alpha, beta, gamma, delta for tenant1; beta for tenant2
        p_alpha = Project(name="alpha", tenant_id=tenant1.id)
        p_beta_t1 = Project(name="beta", tenant_id=tenant1.id)
        p_gamma = Project(name="gamma", tenant_id=tenant1.id)
        p_delta = Project(name="delta", tenant_id=tenant1.id)
        p_beta_t2 = Project(name="beta", tenant_id=tenant2.id)
        await project_repo.bulk_create([p_alpha, p_beta_t1, p_gamma, p_delta, p_beta_t2])

        # soft-delete beta from tenant1
        await project_repo.delete(p_beta_t1.id, soft_delete=True)
        await uow.commit()

    # Compose spec: name in {beta, gamma} AND NOT name == gamma -> filters to remaining beta rows
    async with UnitOfWork(session_factory=session_factory) as uow:
        project_repo = uow.repositories.get_repository(Project)

        # Filter only by name == beta (across tenants). One beta is soft-deleted.
        spec = FieldEquals("name", "beta")

        # Default include_deleted=False should exclude the soft-deleted beta
        result = await project_repo.list_by_spec(
            spec,
            pagination=PaginationParams(page=1, per_page=10),
            sort=SortParam(field="name", direction=SortDirection.ASC),
        )
        # Expect only the non-deleted beta (from tenant2)
        assert result.total == 1
        assert len(result.items) == 1
        assert result.items[0].name == "beta"

        # Now include_deleted=True should bring back the deleted beta
        result_all = await project_repo.list_by_spec(
            spec,
            pagination=PaginationParams(page=1, per_page=10),
            sort=SortParam(field="name", direction=SortDirection.DESC),
            include_deleted=True,
        )
        # Two betas now (one deleted), verify ordering by DESC
        assert result_all.total == 2
        assert [p.name for p in result_all.items] == ["beta", "beta"]

        # Pagination: per_page=1
        paged = await project_repo.list_by_spec(
            spec,
            pagination=PaginationParams(page=1, per_page=1),
            sort=SortParam(field="name", direction=SortDirection.ASC),
            include_deleted=True,
        )
        assert paged.total == 2
        assert len(paged.items) == 1
        assert paged.items[0].name == "beta"

    await engine.dispose()


@pytest.mark.asyncio
async def test_repository_list_filters_sort_pagination_and_count_filters():
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    session_factory = async_sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False, autoflush=False
    )

    async with UnitOfWork(session_factory=session_factory) as uow:
        tenant_repo: BaseRepository[Tenant] = uow.repositories.get_repository(Tenant)
        project_repo: BaseRepository[Project] = uow.repositories.get_repository(Project)

        tenant = Tenant(name="t-filters", display_name="Filters")
        await tenant_repo.create(tenant)

        # Create projects with varying names
        names = ["zeta", "eta", "theta", "iota", "kappa"]
        await project_repo.bulk_create([Project(name=n, tenant_id=tenant.id) for n in names])
        await uow.commit()

    async with UnitOfWork(session_factory=session_factory) as uow:
        project_repo = uow.repositories.get_repository(Project)

        # Filters: like 'ta' should match zeta, eta, theta, iota (substring match)
        filters = {"name": {"like": "ta"}}
        sort = SortParam(field="name", direction=SortDirection.ASC)
        page = PaginationParams(page=1, per_page=2)
        result = await project_repo.list(filters=filters, pagination=page, sort=sort)
        assert result.total == 4
        # Sorted asc: ["eta", "iota", "theta", "zeta"]
        assert [p.name for p in result.items] == ["eta", "iota"]  # first page

        # Next page
        page2 = PaginationParams(page=2, per_page=2)
        result2 = await project_repo.list(filters=filters, pagination=page2, sort=sort)
        assert [p.name for p in result2.items] == ["theta", "zeta"]

        # Count with filters
        cnt = await project_repo.count(filters=filters)
        assert cnt == 4

    await engine.dispose()


@pytest.mark.asyncio
async def test_repository_exists_and_delete_paths_with_include_deleted():
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    session_factory = async_sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False, autoflush=False
    )

    async with UnitOfWork(session_factory=session_factory) as uow:
        tenant_repo: BaseRepository[Tenant] = uow.repositories.get_repository(Tenant)
        project_repo: BaseRepository[Project] = uow.repositories.get_repository(Project)

        tenant = Tenant(name="t-exists-del", display_name="ExistsDel")
        await tenant_repo.create(tenant)

        proj = Project(name="omega", tenant_id=tenant.id)
        await project_repo.create(proj)
        await uow.commit()

    # exists True initially
    async with UnitOfWork(session_factory=session_factory) as uow:
        project_repo = uow.repositories.get_repository(Project)
        assert await project_repo.exists(proj.id) is True

        # soft delete -> exists should be False unless include_deleted=True
        await project_repo.delete(proj.id, soft_delete=True)
        assert await project_repo.exists(proj.id) is False
        assert await project_repo.exists(proj.id, include_deleted=True) is True

        # Attempting hard delete on a soft-deleted entity returns False (not found by get_by_id)
        deleted = await project_repo.delete(proj.id, soft_delete=False)
        assert deleted is False

        # Create a fresh entity and hard delete it
        tenant2 = Tenant(name="t-exists-del-2", display_name="ExistsDel2")
        await uow.repositories.get_repository(Tenant).create(tenant2)
        proj2 = Project(name="omega-2", tenant_id=tenant2.id)
        await project_repo.create(proj2)
        hard_deleted = await project_repo.delete(proj2.id, soft_delete=False)
        assert hard_deleted is True
        assert await project_repo.exists(proj2.id, include_deleted=True) is False

    await engine.dispose()


@pytest.mark.asyncio
async def test_repository_update_valid_and_invalid_field_behaviour():
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    session_factory = async_sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False, autoflush=False
    )

    async with UnitOfWork(session_factory=session_factory) as uow:
        tenant_repo: BaseRepository[Tenant] = uow.repositories.get_repository(Tenant)
        project_repo: BaseRepository[Project] = uow.repositories.get_repository(Project)

        tenant = Tenant(name="t-update", display_name="Upd")
        await tenant_repo.create(tenant)

        proj = Project(name="sigma", tenant_id=tenant.id)
        await project_repo.create(proj)

        # Update currently calls increment_version() which Project doesn't implement.
        # Expect AttributeError; this still exercises update path until the missing method.
        with pytest.raises(AttributeError):
            await project_repo.update(proj.id, {"display_name": "Sigma Display"}, user_id="u1")

        await uow.commit()

    await engine.dispose()
