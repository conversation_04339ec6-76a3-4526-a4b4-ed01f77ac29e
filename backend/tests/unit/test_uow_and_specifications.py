import pytest
import asyncio
from typing import List

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from src.domain.models.base import Base
from src.infrastructure.uow import UnitOfWork
from src.infrastructure.repositories.base import BaseRepository, RepositoryFactory
from src.domain.specifications.base import FieldEquals
from src.infrastructure.persistence.models.organization import Tenant, Project


@pytest.mark.asyncio
async def test_uow_commit_and_rollback():
    # Setup async in-memory SQLite
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    session_factory = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False, autoflush=False)

    # Commit path
    async with UnitOfWork(session_factory=session_factory) as uow:
        tenant_repo: BaseRepository[Tenant] = uow.repositories.get_repository(Tenant)
        tenant = Tenant(name="tenant-uow", display_name="UOW Tenant")
        await tenant_repo.create(tenant)
        # Explicit commit is optional; context exit should commit as well
        await uow.commit()

    # Verify persisted
    async with session_factory() as verify_session:
        result = await verify_session.execute(
            Tenant.__table__.select().where(Tenant.name == "tenant-uow")
        )
        row = result.first()
        assert row is not None

    # Rollback path: raise exception within UoW
    class TestError(Exception):
        pass

    with pytest.raises(TestError):
        async with UnitOfWork(session_factory=session_factory) as uow:
            tenant_repo = uow.repositories.get_repository(Tenant)
            t = Tenant(name="tenant-rollback", display_name="Should Rollback")
            await tenant_repo.create(t)
            raise TestError()

    # Verify not persisted due to rollback
    async with session_factory() as verify_session:
        result = await verify_session.execute(
            Tenant.__table__.select().where(Tenant.name == "tenant-rollback")
        )
        row = result.first()
        assert row is None

    await engine.dispose()


@pytest.mark.asyncio
async def test_repository_list_by_spec_with_field_equals():
    # Setup async in-memory SQLite
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    session_factory = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False, autoflush=False)

    async with UnitOfWork(session_factory=session_factory) as uow:
        tenant_repo: BaseRepository[Tenant] = uow.repositories.get_repository(Tenant)
        project_repo: BaseRepository[Project] = uow.repositories.get_repository(Project)

        tenant = Tenant(name="tenant-spec", display_name="Spec Tenant")
        await tenant_repo.create(tenant)

        p1 = Project(name="alpha", tenant_id=tenant.id)
        p2 = Project(name="beta", tenant_id=tenant.id)
        await project_repo.bulk_create([p1, p2])
        await uow.commit()

    # Filter projects by name using FieldEquals spec
    async with UnitOfWork(session_factory=session_factory) as uow:
        project_repo = uow.repositories.get_repository(Project)
        spec = FieldEquals(field="name", value="alpha")
        result = await project_repo.list_by_spec(spec)
        assert result.total == 1
        assert len(result.items) == 1
        assert result.items[0].name == "alpha"

    await engine.dispose()
