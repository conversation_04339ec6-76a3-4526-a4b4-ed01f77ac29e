# Git
.git
.gitignore
.gitattributes

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.pytest_cache/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
docs/
*.md

# Tests
tests/
test_*.py
*_test.py

# Build artifacts
build/
dist/
*.egg-info/
.eggs/

# Temporary files
*.tmp
*.temp
.tmp/
temp/

# Logs
logs/
*.log

# Local development
.env.local
.env.development
.env.test
.env.production

# Backup files
*.bak
*.backup

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Cache
.cache/
.mypy_cache/
.dmypy.json
dmypy.json

# Jupyter
.ipynb_checkpoints/

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Poetry
poetry.lock