[pytest]
# Pytest configuration for SACRA2 Backend

# Test discovery
minversion = 6.0
addopts = 
    -ra
    --strict-markers
    --strict-config
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=60
    --cov-branch
    --disable-warnings
    --tb=short
    --maxfail=5
    -p no:warnings
    --asyncio-mode=auto
    --import-mode=importlib

# Test paths
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    api: API tests
    database: Database tests
    redis: Redis tests
    celery: Celery tests
    security: Security tests
    performance: Performance tests
    smoke: Smoke tests
    regression: Regression tests
    e2e: End-to-end tests
    async: Async tests
    auth: Authentication tests
    authorization: Authorization tests
    validation: Validation tests
    error_handling: Error handling tests
    health_check: Health check tests
    metrics: Metrics tests
    logging: Logging tests
    caching: Caching tests
    rate_limiting: Rate limiting tests
    circuit_breaker: Circuit breaker tests
    retry: Retry mechanism tests
    background_jobs: Background job tests
    file_upload: File upload tests
    webhooks: Webhook tests
    monitoring: Monitoring tests

# Filter warnings
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# Async configuration
asyncio_mode = auto

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Coverage configuration handled via addopts; detailed omit/exclude can be set in .coveragerc if needed

# Test execution
norecursedirs = .git .tox build dist *.egg venv .venv

# Add src to import path so 'from src...' works
pythonpath = src