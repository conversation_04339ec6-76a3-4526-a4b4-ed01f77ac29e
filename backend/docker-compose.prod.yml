version: '3.8'

services:
  # PostgreSQL Database (Production)
  postgres:
    image: postgres:15-alpine
    container_name: sacra2-postgres-prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-sacra2_db}
      POSTGRES_USER: ${POSTGRES_USER:-sacra2_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-sacra2_password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-sacra2_user} -d ${POSTGRES_DB:-sacra2_db}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - sacra2-prod-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  # Redis Cache (Production)
  redis:
    image: redis:7-alpine
    container_name: sacra2-redis-prod
    ports:
      - "6379:6379"
    volumes:
      - redis_prod_data:/data
    command: >
      redis-server 
      --appendonly yes 
      --maxmemory 512mb 
      --maxmemory-policy allkeys-lru
      --requirepass ${REDIS_PASSWORD:-sacra2_redis_password}
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-sacra2_redis_password}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - sacra2-prod-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # SACRA2 Backend API (Production)
  backend:
    build:
      context: .
      target: production
    container_name: sacra2-backend-prod
    environment:
      APP_ENVIRONMENT: production
      APP_DEBUG: false
      APP_RELOAD: false
      DATABASE_URL: postgresql+asyncpg://${POSTGRES_USER:-sacra2_user}:${POSTGRES_PASSWORD:-sacra2_password}@postgres:5432/${POSTGRES_DB:-sacra2_db}
      REDIS_URL: redis://:${REDIS_PASSWORD:-sacra2_redis_password}@redis:6379/0
      CELERY_BROKER_URL: redis://:${REDIS_PASSWORD:-sacra2_redis_password}@redis:6379/1
      CELERY_RESULT_BACKEND: redis://:${REDIS_PASSWORD:-sacra2_redis_password}@redis:6379/2
      SECRET_KEY: ${SECRET_KEY}
      JWT_SECRET_KEY: ${JWT_SECRET_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      ENABLE_METRICS: true
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/var/log/sacra2
      - ./uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sacra2-prod-network
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  # Celery Worker (Production)
  celery-worker:
    build:
      context: .
      target: production
    container_name: sacra2-celery-worker-prod
    command: ["bash", "-c", "cd /app && poetry run celery -A src.workers.scheduler worker --loglevel=info --concurrency=4"]
    environment:
      APP_ENVIRONMENT: production
      DATABASE_URL: postgresql+asyncpg://${POSTGRES_USER:-sacra2_user}:${POSTGRES_PASSWORD:-sacra2_password}@postgres:5432/${POSTGRES_DB:-sacra2_db}
      REDIS_URL: redis://:${REDIS_PASSWORD:-sacra2_redis_password}@redis:6379/0
      CELERY_BROKER_URL: redis://:${REDIS_PASSWORD:-sacra2_redis_password}@redis:6379/1
      CELERY_RESULT_BACKEND: redis://:${REDIS_PASSWORD:-sacra2_redis_password}@redis:6379/2
      SECRET_KEY: ${SECRET_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
    volumes:
      - ./logs:/var/log/sacra2
      - ./uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sacra2-prod-network
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  # Celery Beat Scheduler (Production)
  celery-beat:
    build:
      context: .
      target: production
    container_name: sacra2-celery-beat-prod
    command: ["bash", "-c", "cd /app && poetry run celery -A src.workers.scheduler beat --loglevel=info"]
    environment:
      APP_ENVIRONMENT: production
      DATABASE_URL: postgresql+asyncpg://${POSTGRES_USER:-sacra2_user}:${POSTGRES_PASSWORD:-sacra2_password}@postgres:5432/${POSTGRES_DB:-sacra2_db}
      REDIS_URL: redis://:${REDIS_PASSWORD:-sacra2_redis_password}@redis:6379/0
      CELERY_BROKER_URL: redis://:${REDIS_PASSWORD:-sacra2_redis_password}@redis:6379/1
      CELERY_RESULT_BACKEND: redis://:${REDIS_PASSWORD:-sacra2_redis_password}@redis:6379/2
      SECRET_KEY: ${SECRET_KEY}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
    volumes:
      - ./logs:/var/log/sacra2
      - ./celerybeat-schedule:/app/celerybeat-schedule
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sacra2-prod-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: sacra2-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - backend
    networks:
      - sacra2-prod-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: sacra2-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - sacra2-prod-network
    restart: unless-stopped

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: sacra2-grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - sacra2-prod-network
    restart: unless-stopped

volumes:
  postgres_prod_data:
    driver: local
  redis_prod_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  sacra2-prod-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16