# Pre-commit configuration for SACRA2 Backend
# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks

repos:
  # Meta hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
        args: [--unsafe]
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: check-case-conflict
      - id: check-docstring-first
      - id: check-json
      - id: check-merge-conflict
      - id: check-toml
      - id: check-xml
      - id: debug-statements
      - id: detect-private-key
      - id: fix-byte-order-marker
      - id: mixed-line-ending
        args: ['--fix=lf']
      - id: no-commit-to-branch
        args: [--branch, main, --branch, master, --branch, develop]

  # Python code formatting
  - repo: https://github.com/psf/black
    rev: 24.1.1
    hooks:
      - id: black
        language_version: python3.11
        args: [--line-length=88, --target-version=py311]

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]

  # Code linting
  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.2.1
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]

  # Type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies:
          - types-requests
          - types-redis
          - types-pytz
          - types-python-dateutil
          - types-PyYAML
          - sqlalchemy-stubs
          - types-setuptools
        args: [--ignore-missing-imports, --no-strict-optional]

  # Documentation
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        additional_dependencies: [toml]
        args: [--convention=google]

  # Security scanning
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.7
    hooks:
      - id: bandit
        args: [--recursive, --skip, "B101,B601"]
        exclude: ^tests/

  # Dependency security check
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check

  # SQL formatting
  - repo: https://github.com/sqlfluff/sqlfluff
    rev: 3.0.0
    hooks:
      - id: sqlfluff-lint
        additional_dependencies: [sqlfluff-templater-dbt]
        exclude: ^alembic/

  # JSON formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types_or: [json, yaml, markdown]
        exclude: ^alembic/

  # Dockerfile linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: [--ignore, DL3008, --ignore, DL3013, --ignore, DL3018]

  # Git hooks
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.15.0
    hooks:
      - id: commitizen
        stages: [commit-msg]

  # Python requirements
  - repo: https://github.com/pycqa/pip-audit
    rev: v2.7.0
    hooks:
      - id: pip-audit
        args: [--desc, --require-hashes]

  # License checking
  - repo: https://github.com/fsfe/reuse-tool
    rev: v2.1.0
    hooks:
      - id: reuse
        args: [--suppress-deprecation]

  # Shell script linting
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.6
    hooks:
      - id: shellcheck
        args: [--severity=warning]

  # YAML linting
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.35.1
    hooks:
      - id: yamllint
        args: [--config-file=.yamllint.yaml]

  # TOML linting
  - repo: https://github.com/toml-sort/toml-sort
    rev: v0.23.1
    hooks:
      - id: toml-sort
        args: [--in-place]

  # Python docstring coverage
  - repo: https://github.com/econchick/interrogate
    rev: 1.5.0
    hooks:
      - id: interrogate
        args: [--verbose, --fail-under=80]

  # Configuration files
  - repo: https://github.com/macisamuele/language-formatters-pre-commit-hooks
    rev: v2.12.0
    hooks:
      - id: pretty-format-yaml
        args: [--autofix, --indent, '2']
      - id: pretty-format-toml
        args: [--autofix]

  # Environment variables
  - repo: https://github.com/jorisroovers/gitlint
    rev: v0.19.1
    hooks:
      - id: gitlint
        stages: [commit-msg]

# Default stages
default_stages: [pre-commit, pre-push]

# Global exclude patterns
exclude: |
  (?x)^(
    \.git/|
    \.venv/|
    venv/|
    __pycache__/|
    \.pytest_cache/|
    \.mypy_cache/|
    \.coverage|
    htmlcov/|
    dist/|
    build/|
    \.egg-info/|
    alembic/versions/|
    migrations/|
    node_modules/|
    \.terraform/|
    \.terragrunt-cache/
  )