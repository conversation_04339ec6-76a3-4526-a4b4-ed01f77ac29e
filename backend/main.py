"""
Main entry point for SACRA2 backend application.
Provides CLI interface and development server.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.api.app import app
from src.core.config import settings
from src.core.logging import get_logger, configure_logging
from src.infrastructure.database import initialize_database, close_database


logger = get_logger(__name__)


def print_banner():
    """Print application banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║   SACRA2 - Scalable AI Compliance and Risk Assessment        ║
    ║                    Backend Application                       ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


async def init_app():
    """Initialize application components"""
    print_banner()
    
    # Configure logging
    configure_logging(
        level=settings.app.log_level,
        service_name=settings.app.name,
        environment=settings.app.environment,
        log_file=settings.app.log_file,
        enable_console=True,
        enable_file=bool(settings.app.log_file)
    )
    
    logger.info("Initializing SACRA2 backend...")
    
    # Initialize database
    await initialize_database()
    
    # Create tables if in development
    if settings.app.environment == "development":
        from src.infrastructure.database import database_manager
        from src.domain.models.base import Base
        
        async with database_manager.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("Database tables created")
    
    logger.info("SACRA2 backend initialized successfully")


async def shutdown_app():
    """Shutdown application components"""
    logger.info("Shutting down SACRA2 backend...")
    
    # Close database connection
    await close_database()
    
    logger.info("SACRA2 backend shut down successfully")


def run_dev_server():
    """Run development server"""
    import uvicorn
    
    uvicorn.run(
        "src.api.app:app",
        host=settings.app.host,
        port=settings.app.port,
        reload=settings.app.debug,
        log_level=settings.app.log_level.lower(),
        access_log=True
    )


def run_prod_server():
    """Run production server"""
    import uvicorn
    
    uvicorn.run(
        "src.api.app:app",
        host=settings.app.host,
        port=settings.app.port,
        workers=settings.app.workers,
        log_level=settings.app.log_level.lower(),
        access_log=False
    )


async def run_migrations():
    """Run database migrations"""
    from src.infrastructure.database import MigrationManager
    
    migration_manager = MigrationManager()
    await migration_manager.apply_migrations()
    logger.info("Database migrations applied")


async def create_superuser():
    """Create superuser (placeholder)"""
    logger.info("Superuser creation not implemented yet")
    # TODO: Implement user creation with admin role


def main():
    """Main CLI entry point"""
    if len(sys.argv) < 2:
        print("Usage: python main.py [dev|prod|migrate|init|create-superuser]")
        return
    
    command = sys.argv[1]
    
    if command == "dev":
        # Initialize app
        asyncio.run(init_app())
        run_dev_server()
    
    elif command == "prod":
        # Initialize app
        asyncio.run(init_app())
        run_prod_server()
    
    elif command == "migrate":
        asyncio.run(run_migrations())
    
    elif command == "init":
        asyncio.run(init_app())
    
    elif command == "create-superuser":
        asyncio.run(create_superuser())
    
    else:
        print(f"Unknown command: {command}")
        print("Available commands: dev, prod, migrate, init, create-superuser")


if __name__ == "__main__":
    main()