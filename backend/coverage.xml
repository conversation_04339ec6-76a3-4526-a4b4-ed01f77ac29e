<?xml version="1.0" ?>
<coverage version="7.10.3" timestamp="1755328489083" lines-valid="3156" lines-covered="2321" line-rate="0.7354" branches-valid="488" branches-covered="226" branch-rate="0.4631" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.10.3 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>/Users/<USER>/GFT/Projects/GFT/SACRA2-Workspace/sacra2/backend/src</source>
	</sources>
	<packages>
		<package name="api" line-rate="0.7941" branch-rate="0.4722" complexity="0">
			<classes>
				<class name="app.py" filename="api/app.py" complexity="0" line-rate="0.7941" branch-rate="0.4722">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="29" hits="1"/>
						<line number="32" hits="1"/>
						<line number="36" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="58"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="69" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="70"/>
						<line number="70" hits="0"/>
						<line number="71" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="83" hits="1"/>
						<line number="86" hits="1"/>
						<line number="88" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="96" hits="1"/>
						<line number="99" hits="1"/>
						<line number="101" hits="1"/>
						<line number="104" hits="1"/>
						<line number="113" hits="1"/>
						<line number="116" hits="1"/>
						<line number="119" hits="1"/>
						<line number="128" hits="1"/>
						<line number="131" hits="1"/>
						<line number="134" hits="1"/>
						<line number="136" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="1"/>
						<line number="145" hits="1"/>
						<line number="148" hits="1"/>
						<line number="151" hits="1"/>
						<line number="152" hits="0"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
						<line number="156" hits="1"/>
						<line number="158" hits="0"/>
						<line number="159" hits="0"/>
						<line number="162" hits="0"/>
						<line number="163" hits="0"/>
						<line number="169" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="170,172"/>
						<line number="170" hits="0"/>
						<line number="172" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="173,179"/>
						<line number="173" hits="0"/>
						<line number="179" hits="0"/>
						<line number="181" hits="0"/>
						<line number="184" hits="1"/>
						<line number="185" hits="1"/>
						<line number="188" hits="1"/>
						<line number="189" hits="1"/>
						<line number="197" hits="1"/>
						<line number="198" hits="1"/>
						<line number="200" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="201"/>
						<line number="201" hits="0"/>
						<line number="203" hits="1"/>
						<line number="206" hits="1"/>
						<line number="207" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="208"/>
						<line number="208" hits="0"/>
						<line number="209" hits="1"/>
						<line number="212" hits="1"/>
						<line number="218" hits="1"/>
						<line number="221" hits="1"/>
						<line number="232" hits="1"/>
						<line number="235" hits="1"/>
						<line number="238" hits="1"/>
						<line number="241" hits="1"/>
						<line number="243" hits="1"/>
						<line number="246" hits="1"/>
						<line number="250" hits="1"/>
						<line number="252" hits="1"/>
						<line number="253" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="259"/>
						<line number="254" hits="1"/>
						<line number="255" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="256"/>
						<line number="256" hits="0"/>
						<line number="258" hits="1"/>
						<line number="259" hits="1"/>
						<line number="260" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="266"/>
						<line number="261" hits="1"/>
						<line number="262" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="265"/>
						<line number="263" hits="1"/>
						<line number="265" hits="0"/>
						<line number="266" hits="1"/>
						<line number="267" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="273"/>
						<line number="268" hits="1"/>
						<line number="269" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="272"/>
						<line number="270" hits="1"/>
						<line number="272" hits="0"/>
						<line number="273" hits="1"/>
						<line number="275" hits="1"/>
						<line number="284" hits="1"/>
						<line number="287" hits="1"/>
						<line number="293" hits="1"/>
						<line number="294" hits="1"/>
						<line number="295" hits="1"/>
						<line number="298" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="299"/>
						<line number="299" hits="0"/>
						<line number="302" hits="1"/>
						<line number="305" hits="1"/>
						<line number="306" hits="1"/>
						<line number="308" hits="0"/>
						<line number="319" hits="1"/>
						<line number="320" hits="1"/>
						<line number="322" hits="1"/>
						<line number="324" hits="1"/>
						<line number="325" hits="1"/>
						<line number="327" hits="0"/>
						<line number="334" hits="0"/>
						<line number="346" hits="1"/>
						<line number="350" hits="1"/>
						<line number="351" hits="1"/>
						<line number="353" hits="0"/>
						<line number="355" hits="0"/>
						<line number="357" hits="0"/>
						<line number="364" hits="1"/>
						<line number="365" hits="1"/>
						<line number="367" hits="0"/>
						<line number="375" hits="1"/>
						<line number="382" hits="1"/>
						<line number="383" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="384"/>
						<line number="384" hits="0"/>
						<line number="385" hits="0"/>
						<line number="387" hits="0"/>
						<line number="392" hits="1"/>
						<line number="394" hits="1"/>
						<line number="396" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="397,398"/>
						<line number="397" hits="0"/>
						<line number="398" hits="0"/>
						<line number="401" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="402"/>
						<line number="402" hits="0"/>
						<line number="403" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.v1" line-rate="1" branch-rate="1" complexity="0">
			<classes>
				<class name="routes.py" filename="api/v1/routes.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.v1.endpoints" line-rate="0.8641" branch-rate="0.7788" complexity="0">
			<classes>
				<class name="drivers.py" filename="api/v1/endpoints/drivers.py" complexity="0" line-rate="0.8622" branch-rate="0.8043">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="21" hits="1"/>
						<line number="25" hits="1"/>
						<line number="27" hits="1"/>
						<line number="31" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="36" hits="1"/>
						<line number="38" hits="1"/>
						<line number="42" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="1"/>
						<line number="53" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="58" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="80" hits="1"/>
						<line number="82" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="85"/>
						<line number="83" hits="1"/>
						<line number="85" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="86" hits="1"/>
						<line number="88" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="89" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="97" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="114" hits="1"/>
						<line number="115" hits="1"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="120" hits="0"/>
						<line number="123" hits="1"/>
						<line number="124" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="140" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="145" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="150" hits="1"/>
						<line number="151" hits="1"/>
						<line number="152" hits="0"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="163" hits="1"/>
						<line number="164" hits="1"/>
						<line number="165" hits="1"/>
						<line number="172" hits="1"/>
						<line number="173" hits="1"/>
						<line number="181" hits="1"/>
						<line number="183" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="184" hits="1"/>
						<line number="186" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="187"/>
						<line number="187" hits="0"/>
						<line number="189" hits="1"/>
						<line number="190" hits="1"/>
						<line number="192" hits="1"/>
						<line number="201" hits="1"/>
						<line number="202" hits="1"/>
						<line number="208" hits="1"/>
						<line number="210" hits="1"/>
						<line number="211" hits="1"/>
						<line number="212" hits="1"/>
						<line number="213" hits="1"/>
						<line number="214" hits="1"/>
						<line number="215" hits="1"/>
						<line number="216" hits="0"/>
						<line number="217" hits="0"/>
						<line number="218" hits="0"/>
						<line number="221" hits="1"/>
						<line number="222" hits="1"/>
						<line number="227" hits="1"/>
						<line number="228" hits="1"/>
						<line number="231" hits="1"/>
						<line number="232" hits="1"/>
						<line number="238" hits="1"/>
						<line number="241" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="242"/>
						<line number="242" hits="0"/>
						<line number="245" hits="1"/>
						<line number="246" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="247" hits="1"/>
						<line number="249" hits="1"/>
						<line number="250" hits="1"/>
						<line number="251" hits="1"/>
						<line number="252" hits="1"/>
						<line number="253" hits="1"/>
						<line number="254" hits="0"/>
						<line number="255" hits="0"/>
						<line number="256" hits="0"/>
						<line number="259" hits="1"/>
						<line number="260" hits="1"/>
						<line number="265" hits="1"/>
						<line number="266" hits="1"/>
						<line number="267" hits="1"/>
						<line number="274" hits="1"/>
						<line number="275" hits="1"/>
						<line number="286" hits="1"/>
						<line number="288" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="291"/>
						<line number="289" hits="1"/>
						<line number="291" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="292" hits="1"/>
						<line number="294" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="295" hits="1"/>
						<line number="297" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="298" hits="1"/>
						<line number="300" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="301"/>
						<line number="301" hits="0"/>
						<line number="303" hits="1"/>
						<line number="304" hits="1"/>
						<line number="306" hits="1"/>
						<line number="315" hits="1"/>
						<line number="316" hits="1"/>
						<line number="322" hits="1"/>
						<line number="324" hits="1"/>
						<line number="325" hits="1"/>
						<line number="326" hits="1"/>
						<line number="327" hits="1"/>
						<line number="328" hits="1"/>
						<line number="329" hits="1"/>
						<line number="330" hits="0"/>
						<line number="331" hits="0"/>
						<line number="332" hits="0"/>
						<line number="335" hits="1"/>
						<line number="336" hits="1"/>
						<line number="341" hits="1"/>
						<line number="342" hits="1"/>
						<line number="345" hits="1"/>
						<line number="346" hits="1"/>
						<line number="352" hits="1"/>
						<line number="355" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="356"/>
						<line number="356" hits="0"/>
						<line number="359" hits="1"/>
						<line number="360" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="361" hits="1"/>
						<line number="363" hits="1"/>
						<line number="364" hits="1"/>
						<line number="365" hits="1"/>
						<line number="366" hits="1"/>
						<line number="367" hits="1"/>
						<line number="368" hits="0"/>
						<line number="369" hits="0"/>
						<line number="370" hits="0"/>
						<line number="373" hits="1"/>
						<line number="374" hits="1"/>
						<line number="379" hits="1"/>
						<line number="380" hits="1"/>
						<line number="381" hits="1"/>
						<line number="388" hits="1"/>
						<line number="389" hits="1"/>
						<line number="397" hits="1"/>
						<line number="399" hits="1"/>
						<line number="404" hits="1"/>
						<line number="406" hits="1"/>
						<line number="415" hits="1"/>
						<line number="416" hits="1"/>
						<line number="423" hits="1"/>
						<line number="426" hits="1"/>
						<line number="428" hits="1"/>
						<line number="429" hits="1"/>
						<line number="430" hits="1"/>
						<line number="431" hits="1"/>
						<line number="432" hits="1"/>
						<line number="433" hits="1"/>
						<line number="434" hits="0"/>
						<line number="435" hits="0"/>
						<line number="436" hits="0"/>
						<line number="439" hits="1"/>
						<line number="440" hits="1"/>
						<line number="445" hits="1"/>
						<line number="449" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="450" hits="1"/>
						<line number="451" hits="1"/>
						<line number="454" hits="1"/>
						<line number="455" hits="1"/>
						<line number="461" hits="1"/>
						<line number="465" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="466"/>
						<line number="466" hits="0"/>
						<line number="469" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="470"/>
						<line number="470" hits="0"/>
						<line number="473" hits="1"/>
						<line number="474" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="475" hits="1"/>
						<line number="477" hits="1"/>
						<line number="478" hits="1"/>
						<line number="479" hits="1"/>
						<line number="480" hits="1"/>
						<line number="481" hits="1"/>
						<line number="482" hits="0"/>
						<line number="483" hits="0"/>
						<line number="484" hits="0"/>
						<line number="487" hits="1"/>
						<line number="488" hits="1"/>
						<line number="493" hits="1"/>
						<line number="497" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="498"/>
						<line number="498" hits="0"/>
						<line number="500" hits="1"/>
						<line number="501" hits="1"/>
					</lines>
				</class>
				<class name="evaluation.py" filename="api/v1/endpoints/evaluation.py" complexity="0" line-rate="0.8657" branch-rate="0.7586">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="16" hits="1"/>
						<line number="26" hits="1"/>
						<line number="30" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="46" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="54" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="62" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="85" hits="1"/>
						<line number="87" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="90"/>
						<line number="88" hits="1"/>
						<line number="90" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="91" hits="1"/>
						<line number="93" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="94" hits="1"/>
						<line number="99" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="100"/>
						<line number="100" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="101,103"/>
						<line number="101" hits="0"/>
						<line number="103" hits="1"/>
						<line number="104" hits="1"/>
						<line number="106" hits="1"/>
						<line number="115" hits="1"/>
						<line number="116" hits="1"/>
						<line number="122" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="128" hits="1"/>
						<line number="132" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="136" hits="1"/>
						<line number="137" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="153" hits="1"/>
						<line number="154" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="164" hits="1"/>
						<line number="167" hits="1"/>
						<line number="168" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="169" hits="1"/>
						<line number="171" hits="1"/>
						<line number="172" hits="1"/>
						<line number="173" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="176" hits="0"/>
						<line number="177" hits="0"/>
						<line number="178" hits="0"/>
						<line number="181" hits="1"/>
						<line number="182" hits="1"/>
						<line number="187" hits="1"/>
						<line number="188" hits="1"/>
						<line number="189" hits="1"/>
						<line number="196" hits="1"/>
						<line number="197" hits="1"/>
						<line number="207" hits="1"/>
						<line number="209" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="212"/>
						<line number="210" hits="1"/>
						<line number="212" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="213"/>
						<line number="213" hits="0"/>
						<line number="215" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="216" hits="1"/>
						<line number="218" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="219"/>
						<line number="219" hits="0"/>
						<line number="224" hits="1"/>
						<line number="225" hits="1"/>
						<line number="227" hits="1"/>
						<line number="236" hits="1"/>
						<line number="237" hits="1"/>
						<line number="242" hits="1"/>
						<line number="243" hits="1"/>
						<line number="244" hits="1"/>
						<line number="245" hits="1"/>
						<line number="246" hits="1"/>
						<line number="247" hits="1"/>
						<line number="248" hits="0"/>
						<line number="249" hits="0"/>
						<line number="250" hits="0"/>
						<line number="253" hits="1"/>
						<line number="254" hits="1"/>
						<line number="259" hits="1"/>
						<line number="260" hits="1"/>
						<line number="263" hits="1"/>
						<line number="264" hits="1"/>
						<line number="270" hits="1"/>
						<line number="273" hits="1"/>
						<line number="274" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="275" hits="1"/>
						<line number="277" hits="1"/>
						<line number="278" hits="1"/>
						<line number="279" hits="1"/>
						<line number="280" hits="1"/>
						<line number="281" hits="1"/>
						<line number="282" hits="0"/>
						<line number="283" hits="0"/>
						<line number="284" hits="0"/>
						<line number="287" hits="1"/>
						<line number="288" hits="1"/>
						<line number="293" hits="0"/>
						<line number="294" hits="0"/>
						<line number="295" hits="0"/>
						<line number="302" hits="1"/>
						<line number="303" hits="1"/>
						<line number="309" hits="1"/>
						<line number="311" hits="1"/>
						<line number="316" hits="1"/>
						<line number="319" hits="1"/>
						<line number="320" hits="1"/>
						<line number="327" hits="1"/>
						<line number="328" hits="1"/>
						<line number="331" hits="1"/>
						<line number="333" hits="1"/>
						<line number="334" hits="1"/>
						<line number="335" hits="1"/>
						<line number="336" hits="1"/>
						<line number="337" hits="1"/>
						<line number="338" hits="1"/>
						<line number="339" hits="0"/>
						<line number="340" hits="0"/>
						<line number="341" hits="0"/>
						<line number="344" hits="1"/>
						<line number="345" hits="1"/>
						<line number="352" hits="1"/>
						<line number="358" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="359"/>
						<line number="359" hits="0"/>
						<line number="362" hits="1"/>
						<line number="363" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="364" hits="1"/>
						<line number="366" hits="1"/>
						<line number="367" hits="1"/>
						<line number="368" hits="1"/>
						<line number="369" hits="1"/>
						<line number="372" hits="1"/>
						<line number="373" hits="1"/>
						<line number="379" hits="1"/>
						<line number="385" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="386"/>
						<line number="386" hits="0"/>
						<line number="388" hits="1"/>
						<line number="389" hits="1"/>
						<line number="396" hits="1"/>
						<line number="397" hits="1"/>
						<line number="407" hits="1"/>
						<line number="409" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="412"/>
						<line number="410" hits="1"/>
						<line number="412" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="413"/>
						<line number="413" hits="0"/>
						<line number="415" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="416" hits="1"/>
						<line number="418" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="419"/>
						<line number="419" hits="0"/>
						<line number="424" hits="1"/>
						<line number="425" hits="1"/>
						<line number="427" hits="1"/>
						<line number="436" hits="1"/>
						<line number="437" hits="1"/>
						<line number="442" hits="1"/>
						<line number="443" hits="1"/>
						<line number="444" hits="1"/>
						<line number="445" hits="1"/>
						<line number="446" hits="1"/>
						<line number="447" hits="1"/>
						<line number="448" hits="0"/>
						<line number="449" hits="0"/>
						<line number="450" hits="0"/>
						<line number="453" hits="1"/>
						<line number="454" hits="1"/>
						<line number="459" hits="1"/>
						<line number="460" hits="1"/>
						<line number="463" hits="1"/>
						<line number="464" hits="1"/>
						<line number="470" hits="1"/>
						<line number="473" hits="1"/>
						<line number="474" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="475" hits="1"/>
						<line number="477" hits="1"/>
						<line number="478" hits="1"/>
						<line number="479" hits="1"/>
						<line number="480" hits="1"/>
						<line number="481" hits="1"/>
						<line number="482" hits="0"/>
						<line number="483" hits="0"/>
						<line number="484" hits="0"/>
						<line number="487" hits="1"/>
						<line number="488" hits="1"/>
						<line number="493" hits="0"/>
						<line number="494" hits="0"/>
						<line number="495" hits="0"/>
						<line number="502" hits="1"/>
						<line number="503" hits="1"/>
						<line number="512" hits="1"/>
						<line number="514" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="517"/>
						<line number="515" hits="1"/>
						<line number="517" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="518" hits="1"/>
						<line number="520" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="521"/>
						<line number="521" hits="0"/>
						<line number="526" hits="1"/>
						<line number="527" hits="1"/>
						<line number="529" hits="1"/>
						<line number="538" hits="1"/>
						<line number="539" hits="1"/>
						<line number="544" hits="1"/>
						<line number="545" hits="1"/>
						<line number="546" hits="1"/>
						<line number="547" hits="1"/>
						<line number="548" hits="1"/>
						<line number="549" hits="1"/>
						<line number="550" hits="1"/>
						<line number="551" hits="1"/>
						<line number="552" hits="1"/>
						<line number="555" hits="1"/>
						<line number="556" hits="1"/>
						<line number="561" hits="1"/>
						<line number="562" hits="1"/>
						<line number="565" hits="1"/>
						<line number="566" hits="1"/>
						<line number="572" hits="1"/>
						<line number="575" hits="1"/>
						<line number="576" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="577" hits="1"/>
						<line number="579" hits="1"/>
						<line number="580" hits="1"/>
						<line number="581" hits="1"/>
						<line number="582" hits="1"/>
						<line number="583" hits="1"/>
						<line number="584" hits="0"/>
						<line number="585" hits="0"/>
						<line number="586" hits="0"/>
						<line number="589" hits="1"/>
						<line number="590" hits="1"/>
						<line number="595" hits="1"/>
						<line number="596" hits="1"/>
						<line number="597" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="application.dto" line-rate="0.9692" branch-rate="0.4583" complexity="0">
			<classes>
				<class name="driver_dto.py" filename="application/dto/driver_dto.py" complexity="0" line-rate="0.9436" branch-rate="0.25">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="28" hits="1"/>
						<line number="30" hits="1"/>
						<line number="33" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="44" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="45,48"/>
						<line number="45" hits="0"/>
						<line number="46" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="47,48"/>
						<line number="47" hits="0"/>
						<line number="48" hits="0"/>
						<line number="51" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="65" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="74" hits="1"/>
						<line number="76" hits="1"/>
						<line number="79" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="84" hits="1"/>
						<line number="85" hits="1"/>
						<line number="88" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="104" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="114" hits="1"/>
						<line number="115" hits="1"/>
						<line number="118" hits="1"/>
						<line number="120" hits="1"/>
						<line number="123" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="128" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="1"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="137" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="1"/>
						<line number="146" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="152" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="160" hits="1"/>
						<line number="161" hits="1"/>
						<line number="163" hits="1"/>
						<line number="164" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="165"/>
						<line number="165" hits="0"/>
						<line number="166" hits="1"/>
						<line number="169" hits="1"/>
						<line number="171" hits="1"/>
						<line number="174" hits="1"/>
						<line number="176" hits="1"/>
						<line number="177" hits="1"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1"/>
						<line number="182" hits="1"/>
						<line number="183" hits="1"/>
						<line number="185" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="186,189"/>
						<line number="186" hits="0"/>
						<line number="187" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="188,189"/>
						<line number="188" hits="0"/>
						<line number="189" hits="0"/>
						<line number="192" hits="1"/>
						<line number="194" hits="1"/>
						<line number="195" hits="1"/>
						<line number="196" hits="1"/>
						<line number="197" hits="1"/>
						<line number="198" hits="1"/>
						<line number="199" hits="1"/>
						<line number="200" hits="1"/>
						<line number="201" hits="1"/>
						<line number="203" hits="1"/>
						<line number="204" hits="1"/>
						<line number="207" hits="1"/>
						<line number="209" hits="1"/>
						<line number="210" hits="1"/>
						<line number="211" hits="1"/>
						<line number="212" hits="1"/>
						<line number="213" hits="1"/>
						<line number="214" hits="1"/>
						<line number="217" hits="1"/>
						<line number="219" hits="1"/>
						<line number="222" hits="1"/>
						<line number="224" hits="1"/>
						<line number="225" hits="1"/>
						<line number="226" hits="1"/>
						<line number="227" hits="1"/>
						<line number="230" hits="1"/>
						<line number="232" hits="1"/>
						<line number="233" hits="1"/>
						<line number="234" hits="1"/>
						<line number="235" hits="1"/>
						<line number="236" hits="1"/>
						<line number="237" hits="1"/>
						<line number="238" hits="1"/>
						<line number="239" hits="1"/>
						<line number="241" hits="1"/>
						<line number="242" hits="1"/>
						<line number="246" hits="1"/>
						<line number="248" hits="1"/>
						<line number="249" hits="1"/>
						<line number="250" hits="1"/>
						<line number="251" hits="1"/>
						<line number="252" hits="1"/>
						<line number="255" hits="1"/>
						<line number="257" hits="1"/>
						<line number="258" hits="1"/>
						<line number="259" hits="1"/>
						<line number="260" hits="1"/>
						<line number="261" hits="1"/>
						<line number="264" hits="1"/>
						<line number="266" hits="1"/>
						<line number="267" hits="1"/>
						<line number="268" hits="1"/>
						<line number="269" hits="1"/>
						<line number="270" hits="1"/>
						<line number="273" hits="1"/>
						<line number="275" hits="1"/>
						<line number="276" hits="1"/>
						<line number="277" hits="1"/>
						<line number="278" hits="1"/>
						<line number="279" hits="1"/>
						<line number="282" hits="1"/>
						<line number="284" hits="1"/>
						<line number="285" hits="1"/>
						<line number="286" hits="1"/>
						<line number="287" hits="1"/>
						<line number="288" hits="1"/>
					</lines>
				</class>
				<class name="evaluation_dto.py" filename="application/dto/evaluation_dto.py" complexity="0" line-rate="0.9912" branch-rate="0.6667">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="23" hits="1"/>
						<line number="25" hits="1"/>
						<line number="28" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="54" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="75" hits="1"/>
						<line number="77" hits="1"/>
						<line number="80" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="84" hits="1"/>
						<line number="85" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="90" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="95" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="99"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="98"/>
						<line number="98" hits="0"/>
						<line number="99" hits="1"/>
						<line number="102" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="111" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="116" hits="1"/>
						<line number="118" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="121" hits="1"/>
						<line number="122" hits="1"/>
						<line number="123" hits="1"/>
						<line number="126" hits="1"/>
						<line number="130" hits="1"/>
						<line number="133" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="137" hits="1"/>
						<line number="138" hits="1"/>
						<line number="141" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="1"/>
						<line number="146" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="150" hits="1"/>
						<line number="151" hits="1"/>
						<line number="154" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="159" hits="1"/>
						<line number="160" hits="1"/>
						<line number="161" hits="1"/>
						<line number="163" hits="1"/>
						<line number="164" hits="1"/>
						<line number="166" hits="1"/>
						<line number="167" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="168" hits="1"/>
						<line number="169" hits="1"/>
						<line number="172" hits="1"/>
						<line number="174" hits="1"/>
						<line number="177" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1"/>
						<line number="181" hits="1"/>
						<line number="182" hits="1"/>
						<line number="183" hits="1"/>
						<line number="184" hits="1"/>
						<line number="186" hits="1"/>
						<line number="187" hits="1"/>
						<line number="189" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="193"/>
						<line number="190" hits="1"/>
						<line number="191" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="192"/>
						<line number="192" hits="0"/>
						<line number="193" hits="1"/>
						<line number="196" hits="1"/>
						<line number="198" hits="1"/>
						<line number="199" hits="1"/>
						<line number="200" hits="1"/>
						<line number="201" hits="1"/>
						<line number="202" hits="1"/>
						<line number="203" hits="1"/>
						<line number="204" hits="1"/>
						<line number="206" hits="1"/>
						<line number="207" hits="1"/>
						<line number="210" hits="1"/>
						<line number="212" hits="1"/>
						<line number="213" hits="1"/>
						<line number="214" hits="1"/>
						<line number="217" hits="1"/>
						<line number="219" hits="1"/>
						<line number="222" hits="1"/>
						<line number="224" hits="1"/>
						<line number="227" hits="1"/>
						<line number="229" hits="1"/>
						<line number="230" hits="1"/>
						<line number="231" hits="1"/>
						<line number="232" hits="1"/>
						<line number="233" hits="1"/>
						<line number="234" hits="1"/>
						<line number="235" hits="1"/>
						<line number="237" hits="1"/>
						<line number="238" hits="1"/>
						<line number="241" hits="1"/>
						<line number="243" hits="1"/>
						<line number="244" hits="1"/>
						<line number="245" hits="1"/>
						<line number="246" hits="1"/>
						<line number="247" hits="1"/>
						<line number="248" hits="1"/>
						<line number="251" hits="1"/>
						<line number="253" hits="1"/>
						<line number="256" hits="1"/>
						<line number="258" hits="1"/>
						<line number="259" hits="1"/>
						<line number="260" hits="1"/>
						<line number="261" hits="1"/>
						<line number="262" hits="1"/>
						<line number="263" hits="1"/>
						<line number="266" hits="1"/>
						<line number="268" hits="1"/>
						<line number="269" hits="1"/>
						<line number="270" hits="1"/>
						<line number="271" hits="1"/>
						<line number="272" hits="1"/>
						<line number="273" hits="1"/>
						<line number="275" hits="1"/>
						<line number="276" hits="1"/>
						<line number="277" hits="1"/>
						<line number="280" hits="1"/>
						<line number="282" hits="1"/>
						<line number="283" hits="1"/>
						<line number="284" hits="1"/>
						<line number="287" hits="1"/>
						<line number="289" hits="1"/>
						<line number="292" hits="1"/>
						<line number="294" hits="1"/>
						<line number="297" hits="1"/>
						<line number="299" hits="1"/>
						<line number="300" hits="1"/>
						<line number="301" hits="1"/>
						<line number="302" hits="1"/>
						<line number="303" hits="1"/>
						<line number="304" hits="1"/>
						<line number="305" hits="1"/>
						<line number="307" hits="1"/>
						<line number="308" hits="1"/>
						<line number="312" hits="1"/>
						<line number="314" hits="1"/>
						<line number="315" hits="1"/>
						<line number="316" hits="1"/>
						<line number="317" hits="1"/>
						<line number="318" hits="1"/>
						<line number="321" hits="1"/>
						<line number="323" hits="1"/>
						<line number="324" hits="1"/>
						<line number="325" hits="1"/>
						<line number="326" hits="1"/>
						<line number="327" hits="1"/>
						<line number="330" hits="1"/>
						<line number="332" hits="1"/>
						<line number="333" hits="1"/>
						<line number="334" hits="1"/>
						<line number="335" hits="1"/>
						<line number="336" hits="1"/>
						<line number="339" hits="1"/>
						<line number="341" hits="1"/>
						<line number="342" hits="1"/>
						<line number="343" hits="1"/>
						<line number="344" hits="1"/>
						<line number="345" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="core" line-rate="0.5832" branch-rate="0.2321" complexity="0">
			<classes>
				<class name="__init__.py" filename="core/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="config.py" filename="core/config.py" complexity="0" line-rate="0.8571" branch-rate="0.07143">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="24" hits="1"/>
						<line number="32" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="47" hits="0"/>
						<line number="53" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="70" hits="1"/>
						<line number="72" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="84" hits="1"/>
						<line number="85" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="86"/>
						<line number="86" hits="0"/>
						<line number="87" hits="1"/>
						<line number="89" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="93" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="94,95"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="96,97"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="103" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="104,105"/>
						<line number="104" hits="0"/>
						<line number="105" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="106,107"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="113" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="114,115"/>
						<line number="114" hits="0"/>
						<line number="115" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="116,117"/>
						<line number="116" hits="0"/>
						<line number="117" hits="0"/>
						<line number="120" hits="1"/>
						<line number="122" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="130" hits="1"/>
						<line number="132" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="139" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="1"/>
						<line number="148" hits="1"/>
						<line number="150" hits="1"/>
						<line number="151" hits="1"/>
						<line number="152" hits="1"/>
						<line number="153" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="1"/>
						<line number="158" hits="1"/>
						<line number="160" hits="1"/>
						<line number="161" hits="1"/>
						<line number="162" hits="1"/>
						<line number="163" hits="1"/>
						<line number="164" hits="1"/>
						<line number="165" hits="1"/>
						<line number="166" hits="1"/>
						<line number="167" hits="1"/>
						<line number="168" hits="1"/>
						<line number="169" hits="1"/>
						<line number="172" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="176" hits="1"/>
						<line number="177" hits="1"/>
						<line number="180" hits="1"/>
						<line number="185" hits="1"/>
						<line number="186" hits="1"/>
						<line number="187" hits="1"/>
						<line number="190" hits="1"/>
						<line number="191" hits="1"/>
						<line number="192" hits="1"/>
						<line number="193" hits="1"/>
						<line number="196" hits="1"/>
						<line number="197" hits="1"/>
						<line number="198" hits="1"/>
						<line number="199" hits="1"/>
						<line number="202" hits="1"/>
						<line number="203" hits="1"/>
						<line number="204" hits="1"/>
						<line number="205" hits="1"/>
						<line number="206" hits="1"/>
						<line number="207" hits="1"/>
						<line number="208" hits="1"/>
						<line number="211" hits="1"/>
					</lines>
				</class>
				<class name="database.py" filename="core/database.py" complexity="0" line-rate="0.75" branch-rate="1">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="12" hits="1"/>
						<line number="16" hits="0"/>
					</lines>
				</class>
				<class name="decorators.py" filename="core/decorators.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="6" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="28" hits="0"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
						<line number="38" hits="0"/>
						<line number="39" hits="0"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="48" hits="0"/>
						<line number="49" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0"/>
						<line number="56" hits="0"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="68" hits="0"/>
						<line number="69" hits="0"/>
						<line number="70" hits="0"/>
						<line number="73" hits="0"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="79" hits="0"/>
						<line number="82" hits="0"/>
						<line number="84" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="85,87"/>
						<line number="85" hits="0"/>
						<line number="87" hits="0"/>
						<line number="88" hits="0"/>
						<line number="91" hits="0"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0"/>
						<line number="94" hits="0"/>
						<line number="96" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="97,99"/>
						<line number="97" hits="0"/>
						<line number="99" hits="0"/>
						<line number="101" hits="0"/>
						<line number="103" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="104,109"/>
						<line number="104" hits="0"/>
						<line number="105" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="106,108"/>
						<line number="106" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
						<line number="111" hits="0"/>
						<line number="113" hits="0"/>
						<line number="114" hits="0"/>
						<line number="120" hits="0"/>
						<line number="122" hits="0"/>
						<line number="124" hits="0"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,128"/>
						<line number="128" hits="0"/>
						<line number="132" hits="0"/>
						<line number="135" hits="0"/>
						<line number="152" hits="0"/>
						<line number="160" hits="0"/>
						<line number="161" hits="0"/>
						<line number="162" hits="0"/>
						<line number="163" hits="0"/>
						<line number="166" hits="0"/>
						<line number="167" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="168,172"/>
						<line number="168" hits="0"/>
						<line number="169" hits="0"/>
						<line number="172" hits="0"/>
						<line number="175" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="176,179"/>
						<line number="176" hits="0"/>
						<line number="177" hits="0"/>
						<line number="179" hits="0"/>
						<line number="181" hits="0"/>
						<line number="182" hits="0"/>
						<line number="183" hits="0"/>
						<line number="186" hits="0"/>
						<line number="187" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="188,192"/>
						<line number="188" hits="0"/>
						<line number="189" hits="0"/>
						<line number="192" hits="0"/>
						<line number="195" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="196,199"/>
						<line number="196" hits="0"/>
						<line number="197" hits="0"/>
						<line number="199" hits="0"/>
						<line number="202" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="203,205"/>
						<line number="203" hits="0"/>
						<line number="205" hits="0"/>
						<line number="207" hits="0"/>
						<line number="210" hits="0"/>
						<line number="225" hits="0"/>
						<line number="233" hits="0"/>
						<line number="235" hits="0"/>
						<line number="236" hits="0"/>
						<line number="237" hits="0"/>
						<line number="238" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="239,242"/>
						<line number="239" hits="0"/>
						<line number="242" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="243,245"/>
						<line number="243" hits="0"/>
						<line number="245" hits="0"/>
						<line number="248" hits="0"/>
						<line number="249" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="250,253"/>
						<line number="250" hits="0"/>
						<line number="253" hits="0"/>
						<line number="256" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="257,264"/>
						<line number="257" hits="0"/>
						<line number="264" hits="0"/>
						<line number="266" hits="0"/>
						<line number="268" hits="0"/>
						<line number="269" hits="0"/>
						<line number="270" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="271,274"/>
						<line number="271" hits="0"/>
						<line number="274" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="275,277"/>
						<line number="275" hits="0"/>
						<line number="277" hits="0"/>
						<line number="280" hits="0"/>
						<line number="281" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="282,285"/>
						<line number="282" hits="0"/>
						<line number="285" hits="0"/>
						<line number="288" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="289,296"/>
						<line number="289" hits="0"/>
						<line number="296" hits="0"/>
						<line number="298" hits="0"/>
						<line number="301" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="302,304"/>
						<line number="302" hits="0"/>
						<line number="304" hits="0"/>
						<line number="306" hits="0"/>
						<line number="309" hits="0"/>
						<line number="312" hits="0"/>
						<line number="313" hits="0"/>
						<line number="314" hits="0"/>
						<line number="315" hits="0"/>
						<line number="316" hits="0"/>
						<line number="317" hits="0"/>
						<line number="318" hits="0"/>
						<line number="320" hits="0"/>
						<line number="322" hits="0"/>
						<line number="324" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="325,336"/>
						<line number="325" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="326,329"/>
						<line number="326" hits="0"/>
						<line number="327" hits="0"/>
						<line number="329" hits="0"/>
						<line number="336" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="337,341"/>
						<line number="337" hits="0"/>
						<line number="338" hits="0"/>
						<line number="339" hits="0"/>
						<line number="341" hits="0"/>
						<line number="342" hits="0"/>
						<line number="344" hits="0"/>
						<line number="345" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="346,347"/>
						<line number="346" hits="0"/>
						<line number="347" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="348,350"/>
						<line number="348" hits="0"/>
						<line number="350" hits="0"/>
						<line number="352" hits="0"/>
						<line number="353" hits="0"/>
						<line number="354" hits="0"/>
						<line number="355" hits="0"/>
						<line number="357" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="358,360"/>
						<line number="358" hits="0"/>
						<line number="360" hits="0"/>
						<line number="363" hits="0"/>
						<line number="378" hits="0"/>
						<line number="385" hits="0"/>
						<line number="386" hits="0"/>
						<line number="388" hits="0"/>
						<line number="389" hits="0"/>
						<line number="390" hits="0"/>
						<line number="392" hits="0"/>
						<line number="394" hits="0"/>
						<line number="397" hits="0"/>
						<line number="414" hits="0"/>
						<line number="422" hits="0"/>
						<line number="423" hits="0"/>
						<line number="425" hits="0"/>
						<line number="426" hits="0"/>
						<line number="427" hits="0"/>
						<line number="429" hits="0"/>
						<line number="430" hits="0"/>
						<line number="440" hits="0"/>
						<line number="442" hits="0"/>
						<line number="443" hits="0"/>
						<line number="453" hits="0"/>
						<line number="455" hits="0"/>
						<line number="456" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="457,468"/>
						<line number="457" hits="0"/>
						<line number="458" hits="0"/>
						<line number="468" hits="0"/>
						<line number="470" hits="0"/>
						<line number="471" hits="0"/>
						<line number="472" hits="0"/>
						<line number="474" hits="0"/>
						<line number="475" hits="0"/>
						<line number="485" hits="0"/>
						<line number="487" hits="0"/>
						<line number="488" hits="0"/>
						<line number="498" hits="0"/>
						<line number="500" hits="0"/>
						<line number="501" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="502,513"/>
						<line number="502" hits="0"/>
						<line number="503" hits="0"/>
						<line number="513" hits="0"/>
						<line number="516" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="517,519"/>
						<line number="517" hits="0"/>
						<line number="519" hits="0"/>
						<line number="521" hits="0"/>
						<line number="524" hits="0"/>
						<line number="541" hits="0"/>
						<line number="542" hits="0"/>
						<line number="543" hits="0"/>
						<line number="544" hits="0"/>
						<line number="545" hits="0"/>
						<line number="547" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="548,563"/>
						<line number="548" hits="0"/>
						<line number="549" hits="0"/>
						<line number="550" hits="0"/>
						<line number="551" hits="0"/>
						<line number="552" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="553,555"/>
						<line number="553" hits="0"/>
						<line number="555" hits="0"/>
						<line number="560" hits="0"/>
						<line number="561" hits="0"/>
						<line number="563" hits="0"/>
						<line number="565" hits="0"/>
						<line number="566" hits="0"/>
						<line number="567" hits="0"/>
						<line number="568" hits="0"/>
						<line number="570" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="571,586"/>
						<line number="571" hits="0"/>
						<line number="572" hits="0"/>
						<line number="573" hits="0"/>
						<line number="574" hits="0"/>
						<line number="575" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="576,578"/>
						<line number="576" hits="0"/>
						<line number="578" hits="0"/>
						<line number="583" hits="0"/>
						<line number="584" hits="0"/>
						<line number="586" hits="0"/>
						<line number="589" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="590,592"/>
						<line number="590" hits="0"/>
						<line number="592" hits="0"/>
						<line number="594" hits="0"/>
						<line number="597" hits="0"/>
						<line number="605" hits="0"/>
						<line number="606" hits="0"/>
						<line number="607" hits="0"/>
						<line number="609" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="610,614"/>
						<line number="610" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="609,611"/>
						<line number="611" hits="0"/>
						<line number="614" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="615,618"/>
						<line number="615" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="614,616"/>
						<line number="616" hits="0"/>
						<line number="618" hits="0"/>
						<line number="620" hits="0"/>
						<line number="621" hits="0"/>
						<line number="623" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="624,628"/>
						<line number="624" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="623,625"/>
						<line number="625" hits="0"/>
						<line number="628" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="629,632"/>
						<line number="629" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="628,630"/>
						<line number="630" hits="0"/>
						<line number="632" hits="0"/>
						<line number="635" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="636,638"/>
						<line number="636" hits="0"/>
						<line number="638" hits="0"/>
						<line number="640" hits="0"/>
						<line number="644" hits="0"/>
						<line number="646" hits="0"/>
						<line number="649" hits="0"/>
						<line number="651" hits="0"/>
						<line number="655" hits="0"/>
						<line number="669" hits="0"/>
						<line number="670" hits="0"/>
						<line number="682" hits="0"/>
					</lines>
				</class>
				<class name="dependencies.py" filename="core/dependencies.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="35" hits="1"/>
						<line number="37" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="38" hits="1"/>
					</lines>
				</class>
				<class name="exceptions.py" filename="core/exceptions.py" complexity="0" line-rate="0.7103" branch-rate="1">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
						<line number="38" hits="0"/>
						<line number="39" hits="0"/>
						<line number="40" hits="0"/>
						<line number="44" hits="1"/>
						<line number="46" hits="1"/>
						<line number="49" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="0"/>
						<line number="66" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="0"/>
						<line number="83" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="0"/>
						<line number="99" hits="1"/>
						<line number="102" hits="1"/>
						<line number="103" hits="0"/>
						<line number="116" hits="1"/>
						<line number="118" hits="1"/>
						<line number="121" hits="1"/>
						<line number="124" hits="1"/>
						<line number="125" hits="0"/>
						<line number="138" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="0"/>
						<line number="153" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="0"/>
						<line number="170" hits="1"/>
						<line number="172" hits="1"/>
						<line number="175" hits="1"/>
						<line number="178" hits="1"/>
						<line number="179" hits="0"/>
						<line number="187" hits="1"/>
						<line number="190" hits="1"/>
						<line number="191" hits="0"/>
						<line number="199" hits="1"/>
						<line number="202" hits="1"/>
						<line number="203" hits="0"/>
						<line number="213" hits="1"/>
						<line number="216" hits="1"/>
						<line number="217" hits="0"/>
						<line number="226" hits="1"/>
						<line number="228" hits="1"/>
						<line number="231" hits="1"/>
						<line number="234" hits="1"/>
						<line number="235" hits="0"/>
						<line number="250" hits="1"/>
						<line number="253" hits="1"/>
						<line number="254" hits="0"/>
						<line number="268" hits="1"/>
						<line number="271" hits="1"/>
						<line number="278" hits="0"/>
						<line number="292" hits="1"/>
						<line number="294" hits="1"/>
						<line number="297" hits="1"/>
						<line number="300" hits="1"/>
						<line number="301" hits="0"/>
						<line number="308" hits="1"/>
						<line number="311" hits="1"/>
						<line number="312" hits="0"/>
						<line number="322" hits="1"/>
						<line number="325" hits="1"/>
						<line number="326" hits="0"/>
						<line number="340" hits="1"/>
						<line number="342" hits="1"/>
						<line number="345" hits="1"/>
						<line number="348" hits="1"/>
						<line number="349" hits="0"/>
						<line number="362" hits="1"/>
						<line number="365" hits="1"/>
						<line number="366" hits="0"/>
						<line number="380" hits="1"/>
						<line number="383" hits="1"/>
						<line number="384" hits="0"/>
						<line number="399" hits="1"/>
						<line number="401" hits="1"/>
						<line number="404" hits="1"/>
						<line number="407" hits="1"/>
						<line number="408" hits="0"/>
						<line number="419" hits="1"/>
						<line number="422" hits="1"/>
						<line number="423" hits="0"/>
						<line number="436" hits="1"/>
						<line number="439" hits="1"/>
						<line number="440" hits="0"/>
						<line number="455" hits="1"/>
						<line number="458" hits="1"/>
						<line number="464" hits="0"/>
						<line number="474" hits="1"/>
						<line number="476" hits="0"/>
					</lines>
				</class>
				<class name="logging.py" filename="core/logging.py" complexity="0" line-rate="0.7194" branch-rate="0.5625">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="30" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="38"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="54" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="55"/>
						<line number="55" hits="0"/>
						<line number="58" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="59" hits="1"/>
						<line number="62" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="63"/>
						<line number="63" hits="0"/>
						<line number="66" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="67"/>
						<line number="67" hits="0"/>
						<line number="69" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="70"/>
						<line number="70" hits="0"/>
						<line number="72" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="73"/>
						<line number="73" hits="0"/>
						<line number="75" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="76"/>
						<line number="76" hits="0"/>
						<line number="78" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="79"/>
						<line number="79" hits="0"/>
						<line number="82" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="83" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="84"/>
						<line number="84" hits="0"/>
						<line number="86" hits="1"/>
						<line number="89" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="0"/>
						<line number="99" hits="1"/>
						<line number="102" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="106"/>
						<line number="106" hits="0"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="112" hits="1"/>
						<line number="115" hits="1"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="119" hits="1"/>
						<line number="121" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="122"/>
						<line number="122" hits="0"/>
						<line number="123" hits="1"/>
						<line number="126" hits="1"/>
						<line number="129" hits="1"/>
						<line number="134" hits="1"/>
						<line number="136" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="137" hits="1"/>
						<line number="139" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="142"/>
						<line number="140" hits="1"/>
						<line number="142" hits="1"/>
						<line number="144" hits="1"/>
						<line number="146" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="147" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="148"/>
						<line number="148" hits="0"/>
						<line number="149" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="150" hits="1"/>
						<line number="151" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="152"/>
						<line number="152" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="146,153"/>
						<line number="153" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="152,154"/>
						<line number="154" hits="0"/>
						<line number="156" hits="1"/>
						<line number="158" hits="1"/>
						<line number="159" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="160" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="161"/>
						<line number="161" hits="0"/>
						<line number="162" hits="0"/>
						<line number="164" hits="1"/>
						<line number="165" hits="1"/>
						<line number="168" hits="1"/>
						<line number="171" hits="1"/>
						<line number="172" hits="1"/>
						<line number="174" hits="1"/>
						<line number="176" hits="1"/>
						<line number="177" hits="1"/>
						<line number="179" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="182"/>
						<line number="180" hits="1"/>
						<line number="182" hits="1"/>
						<line number="184" hits="1"/>
						<line number="186" hits="0"/>
						<line number="188" hits="1"/>
						<line number="190" hits="1"/>
						<line number="192" hits="1"/>
						<line number="194" hits="0"/>
						<line number="196" hits="1"/>
						<line number="198" hits="0"/>
						<line number="200" hits="1"/>
						<line number="202" hits="0"/>
						<line number="204" hits="1"/>
						<line number="206" hits="0"/>
						<line number="209" hits="1"/>
						<line number="212" hits="1"/>
						<line number="213" hits="1"/>
						<line number="214" hits="1"/>
						<line number="216" hits="1"/>
						<line number="230" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="231"/>
						<line number="231" hits="0"/>
						<line number="232" hits="0"/>
						<line number="235" hits="1"/>
						<line number="236" hits="1"/>
						<line number="239" hits="1"/>
						<line number="242" hits="1"/>
						<line number="245" hits="1"/>
						<line number="248" hits="1"/>
						<line number="251" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="265"/>
						<line number="252" hits="1"/>
						<line number="254" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="255"/>
						<line number="255" hits="0"/>
						<line number="257" hits="1"/>
						<line number="259" hits="1"/>
						<line number="260" hits="1"/>
						<line number="261" hits="1"/>
						<line number="262" hits="1"/>
						<line number="265" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="266"/>
						<line number="266" hits="0"/>
						<line number="268" hits="0"/>
						<line number="275" hits="0"/>
						<line number="276" hits="0"/>
						<line number="277" hits="0"/>
						<line number="278" hits="0"/>
						<line number="279" hits="0"/>
						<line number="282" hits="1"/>
						<line number="284" hits="1"/>
						<line number="286" hits="1"/>
						<line number="288" hits="1"/>
						<line number="302" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="303" hits="1"/>
						<line number="305" hits="1"/>
						<line number="307" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="311"/>
						<line number="308" hits="1"/>
						<line number="309" hits="1"/>
						<line number="311" hits="1"/>
						<line number="315" hits="1"/>
						<line number="318" hits="1"/>
						<line number="320" hits="1"/>
						<line number="323" hits="1"/>
						<line number="325" hits="1"/>
						<line number="329" hits="1"/>
						<line number="332" hits="1"/>
						<line number="333" hits="1"/>
						<line number="334" hits="1"/>
						<line number="336" hits="1"/>
						<line number="338" hits="1"/>
						<line number="339" hits="1"/>
						<line number="341" hits="1"/>
						<line number="343" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="exit"/>
						<line number="344" hits="1"/>
						<line number="347" hits="1"/>
						<line number="349" hits="0"/>
						<line number="350" hits="0"/>
						<line number="351" hits="0"/>
						<line number="352" hits="0"/>
						<line number="353" hits="0"/>
						<line number="354" hits="0"/>
						<line number="355" hits="0"/>
						<line number="359" hits="1"/>
						<line number="368" hits="1"/>
						<line number="379" hits="1"/>
						<line number="389" hits="1"/>
						<line number="402" hits="1"/>
						<line number="405" hits="1"/>
						<line number="406" hits="0"/>
						<line number="407" hits="0"/>
						<line number="408" hits="0"/>
						<line number="410" hits="1"/>
						<line number="412" hits="0"/>
						<line number="413" hits="0"/>
						<line number="414" hits="0"/>
						<line number="416" hits="1"/>
						<line number="418" hits="0"/>
						<line number="419" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="420,426"/>
						<line number="420" hits="0"/>
						<line number="426" hits="0"/>
						<line number="434" hits="1"/>
						<line number="436" hits="0"/>
						<line number="437" hits="0"/>
						<line number="438" hits="0"/>
						<line number="439" hits="0"/>
						<line number="440" hits="0"/>
						<line number="441" hits="0"/>
					</lines>
				</class>
				<class name="test_config.py" filename="core/test_config.py" complexity="0" line-rate="0.8605" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="33" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="45" hits="1"/>
						<line number="47" hits="1"/>
						<line number="51" hits="1"/>
						<line number="54" hits="1"/>
						<line number="56" hits="0"/>
						<line number="59" hits="1"/>
						<line number="61" hits="0"/>
						<line number="62" hits="0"/>
						<line number="65" hits="1"/>
						<line number="67" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="68,70"/>
						<line number="68" hits="0"/>
						<line number="70" hits="0"/>
					</lines>
				</class>
				<class name="types.py" filename="core/types.py" complexity="0" line-rate="0.9545" branch-rate="0">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="36" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,53"/>
						<line number="53" hits="0"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="58" hits="0"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="63" hits="0"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="68" hits="0"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="73" hits="0"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="78" hits="0"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="85" hits="1"/>
						<line number="86" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="91" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="103" hits="1"/>
						<line number="104" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="117" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="140" hits="1"/>
						<line number="141" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="1"/>
						<line number="146" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="153" hits="1"/>
						<line number="154" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="162" hits="1"/>
						<line number="163" hits="1"/>
						<line number="165" hits="1"/>
						<line number="166" hits="1"/>
						<line number="167" hits="1"/>
						<line number="168" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="1"/>
						<line number="171" hits="1"/>
						<line number="172" hits="1"/>
						<line number="173" hits="1"/>
						<line number="177" hits="1"/>
						<line number="180" hits="1"/>
						<line number="185" hits="1"/>
						<line number="188" hits="1"/>
						<line number="192" hits="1"/>
						<line number="196" hits="1"/>
						<line number="202" hits="1"/>
						<line number="203" hits="1"/>
						<line number="205" hits="1"/>
						<line number="206" hits="1"/>
						<line number="207" hits="1"/>
						<line number="211" hits="1"/>
						<line number="212" hits="1"/>
						<line number="214" hits="1"/>
						<line number="215" hits="1"/>
						<line number="216" hits="1"/>
						<line number="217" hits="1"/>
						<line number="221" hits="1"/>
						<line number="223" hits="1"/>
						<line number="224" hits="1"/>
						<line number="225" hits="1"/>
						<line number="228" hits="1"/>
						<line number="229" hits="1"/>
						<line number="231" hits="1"/>
						<line number="232" hits="1"/>
						<line number="233" hits="1"/>
						<line number="234" hits="1"/>
						<line number="238" hits="1"/>
						<line number="239" hits="1"/>
						<line number="241" hits="1"/>
						<line number="242" hits="1"/>
						<line number="243" hits="1"/>
						<line number="244" hits="1"/>
						<line number="248" hits="1"/>
						<line number="249" hits="1"/>
						<line number="251" hits="1"/>
						<line number="252" hits="1"/>
						<line number="253" hits="1"/>
						<line number="254" hits="1"/>
						<line number="255" hits="1"/>
						<line number="256" hits="1"/>
						<line number="260" hits="1"/>
						<line number="261" hits="1"/>
						<line number="263" hits="1"/>
						<line number="264" hits="1"/>
						<line number="265" hits="1"/>
						<line number="266" hits="1"/>
						<line number="270" hits="1"/>
						<line number="271" hits="1"/>
						<line number="272" hits="1"/>
						<line number="273" hits="1"/>
						<line number="274" hits="1"/>
						<line number="275" hits="1"/>
						<line number="279" hits="1"/>
						<line number="280" hits="1"/>
						<line number="281" hits="1"/>
						<line number="282" hits="1"/>
						<line number="283" hits="1"/>
						<line number="284" hits="1"/>
						<line number="285" hits="1"/>
						<line number="289" hits="1"/>
						<line number="290" hits="1"/>
						<line number="292" hits="1"/>
						<line number="293" hits="1"/>
						<line number="294" hits="1"/>
						<line number="295" hits="1"/>
						<line number="297" hits="1"/>
						<line number="298" hits="1"/>
						<line number="300" hits="0"/>
						<line number="302" hits="1"/>
						<line number="303" hits="1"/>
						<line number="305" hits="0"/>
						<line number="309" hits="1"/>
						<line number="310" hits="1"/>
						<line number="312" hits="1"/>
						<line number="313" hits="1"/>
						<line number="314" hits="1"/>
						<line number="315" hits="1"/>
						<line number="316" hits="1"/>
						<line number="317" hits="1"/>
						<line number="318" hits="1"/>
						<line number="319" hits="1"/>
						<line number="323" hits="1"/>
						<line number="324" hits="1"/>
						<line number="326" hits="1"/>
						<line number="327" hits="1"/>
						<line number="328" hits="1"/>
						<line number="329" hits="1"/>
						<line number="330" hits="1"/>
						<line number="334" hits="1"/>
						<line number="335" hits="1"/>
						<line number="337" hits="1"/>
						<line number="338" hits="1"/>
						<line number="339" hits="1"/>
						<line number="340" hits="1"/>
						<line number="341" hits="1"/>
						<line number="342" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="domain.models" line-rate="0.6636" branch-rate="0.375" complexity="0">
			<classes>
				<class name="base.py" filename="domain/models/base.py" complexity="0" line-rate="0.6636" branch-rate="0.375">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="19" hits="1"/>
						<line number="23" hits="1"/>
						<line number="31" hits="1"/>
						<line number="37" hits="1"/>
						<line number="44" hits="1"/>
						<line number="49" hits="1"/>
						<line number="55" hits="1"/>
						<line number="60" hits="1"/>
						<line number="66" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="0"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="79" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="84" hits="0"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="96" hits="0"/>
						<line number="102" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
						<line number="107" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="112" hits="1"/>
						<line number="114" hits="1"/>
						<line number="115" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="116" hits="1"/>
						<line number="118" hits="1"/>
						<line number="120" hits="1"/>
						<line number="132" hits="1"/>
						<line number="134" hits="0"/>
						<line number="136" hits="1"/>
						<line number="138" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="139"/>
						<line number="139" hits="0"/>
						<line number="140" hits="1"/>
						<line number="142" hits="1"/>
						<line number="144" hits="0"/>
						<line number="148" hits="1"/>
						<line number="151" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="1"/>
						<line number="157" hits="0"/>
						<line number="159" hits="1"/>
						<line number="160" hits="1"/>
						<line number="162" hits="0"/>
						<line number="165" hits="1"/>
						<line number="168" hits="1"/>
						<line number="169" hits="1"/>
						<line number="171" hits="0"/>
						<line number="173" hits="1"/>
						<line number="174" hits="1"/>
						<line number="176" hits="0"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="181" hits="0"/>
						<line number="183" hits="1"/>
						<line number="184" hits="1"/>
						<line number="186" hits="0"/>
						<line number="189" hits="1"/>
						<line number="192" hits="1"/>
						<line number="194" hits="0"/>
						<line number="196" hits="1"/>
						<line number="198" hits="0"/>
						<line number="201" hits="1"/>
						<line number="204" hits="1"/>
						<line number="205" hits="1"/>
						<line number="207" hits="0"/>
						<line number="209" hits="1"/>
						<line number="210" hits="1"/>
						<line number="212" hits="0"/>
						<line number="214" hits="1"/>
						<line number="216" hits="0"/>
						<line number="218" hits="1"/>
						<line number="220" hits="0"/>
						<line number="224" hits="1"/>
						<line number="226" hits="0"/>
						<line number="229" hits="1"/>
						<line number="231" hits="0"/>
						<line number="234" hits="0"/>
						<line number="235" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="236,246"/>
						<line number="236" hits="0"/>
						<line number="237" hits="0"/>
						<line number="238" hits="0"/>
						<line number="240" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="235,241"/>
						<line number="241" hits="0"/>
						<line number="246" hits="0"/>
						<line number="249" hits="1"/>
						<line number="251" hits="0"/>
						<line number="252" hits="0"/>
						<line number="253" hits="0"/>
						<line number="254" hits="0"/>
						<line number="257" hits="1"/>
						<line number="259" hits="0"/>
						<line number="260" hits="0"/>
						<line number="261" hits="0"/>
						<line number="262" hits="0"/>
						<line number="263" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="domain.specifications" line-rate="0.8971" branch-rate="0.5833" complexity="0">
			<classes>
				<class name="__init__.py" filename="domain/specifications/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="2" hits="1"/>
					</lines>
				</class>
				<class name="base.py" filename="domain/specifications/base.py" complexity="0" line-rate="0.8955" branch-rate="0.5833">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="22" hits="0"/>
						<line number="24" hits="1"/>
						<line number="25" hits="0"/>
						<line number="27" hits="1"/>
						<line number="28" hits="0"/>
						<line number="30" hits="1"/>
						<line number="31" hits="0"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="80"/>
						<line number="80" hits="0"/>
						<line number="81" hits="1"/>
						<line number="84" hits="1"/>
						<line number="85" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="93"/>
						<line number="93" hits="0"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="96" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="98"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="101"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="103"/>
						<line number="103" hits="0"/>
						<line number="104" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="infrastructure" line-rate="0.4346" branch-rate="0.1053" complexity="0">
			<classes>
				<class name="database.py" filename="infrastructure/database.py" complexity="0" line-rate="0.3289" branch-rate="0">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="29" hits="1"/>
						<line number="31" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="32,34"/>
						<line number="32" hits="0"/>
						<line number="34" hits="0"/>
						<line number="36" hits="0"/>
						<line number="39" hits="0"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="44" hits="0"/>
						<line number="55" hits="0"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="68" hits="0"/>
						<line number="70" hits="1"/>
						<line number="72" hits="0"/>
						<line number="74" hits="0"/>
						<line number="76" hits="1"/>
						<line number="78" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,79"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="83" hits="1"/>
						<line number="85" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="86,88"/>
						<line number="86" hits="0"/>
						<line number="88" hits="0"/>
						<line number="89" hits="0"/>
						<line number="90" hits="0"/>
						<line number="91" hits="0"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0"/>
						<line number="94" hits="0"/>
						<line number="96" hits="0"/>
						<line number="98" hits="1"/>
						<line number="100" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="101,103"/>
						<line number="101" hits="0"/>
						<line number="103" hits="0"/>
						<line number="105" hits="1"/>
						<line number="107" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
						<line number="110" hits="0"/>
						<line number="111" hits="0"/>
						<line number="112" hits="0"/>
						<line number="113" hits="0"/>
						<line number="115" hits="1"/>
						<line number="117" hits="0"/>
						<line number="119" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="120,122"/>
						<line number="120" hits="0"/>
						<line number="122" hits="0"/>
						<line number="123" hits="0"/>
						<line number="125" hits="0"/>
						<line number="127" hits="1"/>
						<line number="129" hits="0"/>
						<line number="131" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="132,134"/>
						<line number="132" hits="0"/>
						<line number="134" hits="0"/>
						<line number="135" hits="0"/>
						<line number="137" hits="0"/>
						<line number="141" hits="1"/>
						<line number="144" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="0"/>
						<line number="149" hits="0"/>
						<line number="151" hits="1"/>
						<line number="153" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="154,155"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="157" hits="1"/>
						<line number="159" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,160"/>
						<line number="160" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="161,163"/>
						<line number="161" hits="0"/>
						<line number="163" hits="0"/>
						<line number="164" hits="0"/>
						<line number="168" hits="1"/>
						<line number="170" hits="0"/>
						<line number="171" hits="0"/>
						<line number="174" hits="1"/>
						<line number="176" hits="0"/>
						<line number="179" hits="1"/>
						<line number="181" hits="0"/>
						<line number="184" hits="1"/>
						<line number="186" hits="0"/>
						<line number="188" hits="0"/>
						<line number="195" hits="1"/>
						<line number="198" hits="1"/>
						<line number="199" hits="0"/>
						<line number="201" hits="1"/>
						<line number="205" hits="0"/>
						<line number="207" hits="1"/>
						<line number="210" hits="0"/>
						<line number="212" hits="1"/>
						<line number="215" hits="0"/>
						<line number="219" hits="1"/>
						<line number="222" hits="1"/>
						<line number="223" hits="0"/>
						<line number="225" hits="1"/>
						<line number="227" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="228,230"/>
						<line number="228" hits="0"/>
						<line number="230" hits="0"/>
						<line number="232" hits="0"/>
						<line number="239" hits="1"/>
						<line number="241" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,242"/>
						<line number="242" hits="0"/>
						<line number="243" hits="0"/>
						<line number="244" hits="0"/>
						<line number="248" hits="1"/>
						<line number="251" hits="1"/>
						<line number="252" hits="0"/>
						<line number="253" hits="0"/>
						<line number="255" hits="1"/>
						<line number="257" hits="0"/>
						<line number="258" hits="0"/>
						<line number="259" hits="0"/>
						<line number="261" hits="1"/>
						<line number="263" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,264"/>
						<line number="264" hits="0"/>
						<line number="267" hits="1"/>
						<line number="270" hits="1"/>
						<line number="271" hits="0"/>
						<line number="273" hits="1"/>
						<line number="275" hits="0"/>
						<line number="276" hits="0"/>
						<line number="277" hits="0"/>
						<line number="278" hits="0"/>
						<line number="280" hits="1"/>
						<line number="282" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,283"/>
						<line number="283" hits="0"/>
						<line number="284" hits="0"/>
						<line number="285" hits="0"/>
						<line number="287" hits="1"/>
						<line number="289" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,290"/>
						<line number="290" hits="0"/>
						<line number="291" hits="0"/>
						<line number="292" hits="0"/>
						<line number="296" hits="1"/>
					</lines>
				</class>
				<class name="uow.py" filename="infrastructure/uow.py" complexity="0" line-rate="0.8462" branch-rate="0.4">
					<methods/>
					<lines>
						<line number="22" hits="1"/>
						<line number="24" hits="1"/>
						<line number="26" hits="1"/>
						<line number="28" hits="1"/>
						<line number="31" hits="1"/>
						<line number="41" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="59" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="60"/>
						<line number="60" hits="0"/>
						<line number="61" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="66"/>
						<line number="66" hits="0"/>
						<line number="68" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="70,71"/>
						<line number="70" hits="0"/>
						<line number="71" hits="0"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="84" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="87"/>
						<line number="85" hits="1"/>
						<line number="87" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="90"/>
						<line number="88" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="94" hits="1"/>
						<line number="96" hits="1"/>
						<line number="98" hits="1"/>
						<line number="100" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="infrastructure.persistence.models" line-rate="0.8072" branch-rate="0.7917" complexity="0">
			<classes>
				<class name="__init__.py" filename="infrastructure/persistence/models/__init__.py" complexity="0" line-rate="0.7895" branch-rate="1">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="18" hits="1"/>
						<line number="23" hits="1"/>
						<line number="33" hits="1"/>
						<line number="41" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="80" hits="1"/>
						<line number="83" hits="1"/>
						<line number="113" hits="1"/>
						<line number="115" hits="0"/>
						<line number="117" hits="1"/>
						<line number="119" hits="0"/>
						<line number="121" hits="1"/>
						<line number="123" hits="0"/>
						<line number="131" hits="0"/>
					</lines>
				</class>
				<class name="drivers.py" filename="infrastructure/persistence/models/drivers.py" complexity="0" line-rate="0.9027" branch-rate="1">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="29" hits="1"/>
						<line number="32" hits="1"/>
						<line number="35" hits="1"/>
						<line number="41" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="52" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="0"/>
						<line number="58" hits="1"/>
						<line number="61" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="69" hits="1"/>
						<line number="72" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="80" hits="1"/>
						<line number="86" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="98" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="0"/>
						<line number="104" hits="1"/>
						<line number="107" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="114" hits="1"/>
						<line number="115" hits="1"/>
						<line number="116" hits="1"/>
						<line number="119" hits="1"/>
						<line number="122" hits="1"/>
						<line number="123" hits="1"/>
						<line number="126" hits="1"/>
						<line number="132" hits="1"/>
						<line number="134" hits="0"/>
						<line number="135" hits="0"/>
						<line number="144" hits="0"/>
						<line number="146" hits="1"/>
						<line number="147" hits="0"/>
						<line number="150" hits="1"/>
						<line number="153" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="161" hits="1"/>
						<line number="162" hits="1"/>
						<line number="165" hits="1"/>
						<line number="166" hits="1"/>
						<line number="167" hits="1"/>
						<line number="170" hits="1"/>
						<line number="173" hits="1"/>
						<line number="176" hits="1"/>
						<line number="177" hits="1"/>
						<line number="180" hits="1"/>
						<line number="188" hits="1"/>
						<line number="189" hits="1"/>
						<line number="191" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="192" hits="1"/>
						<line number="193" hits="1"/>
						<line number="195" hits="1"/>
						<line number="197" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="198" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="199" hits="1"/>
						<line number="200" hits="1"/>
						<line number="202" hits="1"/>
						<line number="204" hits="1"/>
						<line number="205" hits="1"/>
						<line number="220" hits="1"/>
						<line number="222" hits="1"/>
						<line number="223" hits="0"/>
						<line number="226" hits="1"/>
						<line number="229" hits="1"/>
						<line number="232" hits="1"/>
						<line number="233" hits="1"/>
						<line number="234" hits="1"/>
						<line number="235" hits="1"/>
						<line number="238" hits="1"/>
						<line number="241" hits="1"/>
						<line number="244" hits="1"/>
						<line number="251" hits="1"/>
						<line number="252" hits="1"/>
						<line number="254" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="255" hits="1"/>
						<line number="256" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="257" hits="1"/>
						<line number="258" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="259" hits="1"/>
						<line number="260" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="261" hits="1"/>
						<line number="262" hits="1"/>
						<line number="263" hits="1"/>
						<line number="265" hits="1"/>
						<line number="267" hits="0"/>
						<line number="268" hits="0"/>
						<line number="277" hits="0"/>
						<line number="279" hits="1"/>
						<line number="280" hits="0"/>
					</lines>
				</class>
				<class name="evaluation.py" filename="infrastructure/persistence/models/evaluation.py" complexity="0" line-rate="0.7536" branch-rate="1">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="43" hits="1"/>
						<line number="50" hits="1"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="64" hits="0"/>
						<line number="66" hits="1"/>
						<line number="67" hits="0"/>
						<line number="70" hits="1"/>
						<line number="73" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="1"/>
						<line number="90" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="99" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="109" hits="1"/>
						<line number="111" hits="1"/>
						<line number="113" hits="0"/>
						<line number="114" hits="0"/>
						<line number="126" hits="0"/>
						<line number="128" hits="1"/>
						<line number="129" hits="0"/>
						<line number="132" hits="1"/>
						<line number="135" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="147" hits="1"/>
						<line number="150" hits="1"/>
						<line number="151" hits="1"/>
						<line number="154" hits="1"/>
						<line number="160" hits="1"/>
						<line number="162" hits="0"/>
						<line number="163" hits="0"/>
						<line number="174" hits="0"/>
						<line number="176" hits="1"/>
						<line number="177" hits="0"/>
						<line number="178" hits="0"/>
						<line number="179" hits="0"/>
						<line number="182" hits="1"/>
						<line number="185" hits="1"/>
						<line number="188" hits="1"/>
						<line number="189" hits="1"/>
						<line number="190" hits="1"/>
						<line number="193" hits="1"/>
						<line number="194" hits="1"/>
						<line number="197" hits="1"/>
						<line number="200" hits="1"/>
						<line number="201" hits="1"/>
						<line number="203" hits="1"/>
						<line number="206" hits="1"/>
						<line number="212" hits="1"/>
						<line number="214" hits="0"/>
						<line number="215" hits="0"/>
						<line number="224" hits="0"/>
						<line number="226" hits="1"/>
						<line number="227" hits="0"/>
						<line number="230" hits="1"/>
						<line number="233" hits="1"/>
						<line number="236" hits="1"/>
						<line number="237" hits="1"/>
						<line number="240" hits="1"/>
						<line number="243" hits="1"/>
						<line number="244" hits="1"/>
						<line number="247" hits="1"/>
						<line number="252" hits="1"/>
						<line number="254" hits="0"/>
						<line number="255" hits="0"/>
						<line number="264" hits="0"/>
						<line number="266" hits="1"/>
						<line number="267" hits="0"/>
						<line number="268" hits="0"/>
						<line number="269" hits="0"/>
						<line number="272" hits="1"/>
						<line number="275" hits="1"/>
						<line number="278" hits="1"/>
						<line number="279" hits="1"/>
						<line number="280" hits="1"/>
						<line number="281" hits="1"/>
						<line number="284" hits="1"/>
						<line number="287" hits="1"/>
						<line number="290" hits="1"/>
						<line number="291" hits="1"/>
						<line number="292" hits="1"/>
						<line number="295" hits="1"/>
						<line number="301" hits="1"/>
						<line number="302" hits="1"/>
						<line number="304" hits="1"/>
						<line number="306" hits="1"/>
						<line number="308" hits="0"/>
						<line number="309" hits="0"/>
						<line number="318" hits="0"/>
						<line number="320" hits="1"/>
						<line number="321" hits="0"/>
						<line number="324" hits="1"/>
						<line number="327" hits="1"/>
						<line number="330" hits="1"/>
						<line number="331" hits="1"/>
						<line number="334" hits="1"/>
						<line number="337" hits="1"/>
						<line number="338" hits="1"/>
						<line number="341" hits="1"/>
						<line number="346" hits="1"/>
						<line number="348" hits="0"/>
						<line number="349" hits="0"/>
						<line number="358" hits="0"/>
						<line number="360" hits="1"/>
						<line number="361" hits="0"/>
						<line number="362" hits="0"/>
						<line number="363" hits="0"/>
					</lines>
				</class>
				<class name="execution.py" filename="infrastructure/persistence/models/execution.py" complexity="0" line-rate="0.7597" branch-rate="0.5">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="54" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="65" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="67"/>
						<line number="66" hits="1"/>
						<line number="67" hits="0"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="72" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="79" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="84" hits="1"/>
						<line number="86" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="91" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="96" hits="1"/>
						<line number="98" hits="0"/>
						<line number="99" hits="0"/>
						<line number="100" hits="0"/>
						<line number="101" hits="0"/>
						<line number="103" hits="1"/>
						<line number="105" hits="0"/>
						<line number="106" hits="0"/>
						<line number="108" hits="1"/>
						<line number="110" hits="0"/>
						<line number="111" hits="0"/>
						<line number="132" hits="0"/>
						<line number="134" hits="1"/>
						<line number="135" hits="0"/>
						<line number="138" hits="1"/>
						<line number="141" hits="1"/>
						<line number="144" hits="1"/>
						<line number="147" hits="1"/>
						<line number="150" hits="1"/>
						<line number="151" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="1"/>
						<line number="158" hits="1"/>
						<line number="159" hits="1"/>
						<line number="162" hits="1"/>
						<line number="163" hits="1"/>
						<line number="166" hits="1"/>
						<line number="167" hits="1"/>
						<line number="170" hits="1"/>
						<line number="177" hits="1"/>
						<line number="178" hits="1"/>
						<line number="180" hits="0"/>
						<line number="182" hits="1"/>
						<line number="183" hits="1"/>
						<line number="185" hits="0"/>
						<line number="186" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="187,188"/>
						<line number="187" hits="0"/>
						<line number="188" hits="0"/>
						<line number="190" hits="1"/>
						<line number="192" hits="0"/>
						<line number="193" hits="0"/>
						<line number="206" hits="0"/>
						<line number="208" hits="1"/>
						<line number="209" hits="0"/>
						<line number="210" hits="0"/>
						<line number="213" hits="1"/>
						<line number="216" hits="1"/>
						<line number="219" hits="1"/>
						<line number="220" hits="1"/>
						<line number="223" hits="1"/>
						<line number="224" hits="1"/>
						<line number="227" hits="1"/>
						<line number="228" hits="1"/>
						<line number="231" hits="1"/>
						<line number="232" hits="1"/>
						<line number="235" hits="1"/>
						<line number="241" hits="1"/>
						<line number="243" hits="0"/>
						<line number="244" hits="0"/>
						<line number="254" hits="0"/>
						<line number="256" hits="1"/>
						<line number="257" hits="0"/>
						<line number="258" hits="0"/>
						<line number="261" hits="1"/>
						<line number="264" hits="1"/>
						<line number="267" hits="1"/>
						<line number="268" hits="1"/>
						<line number="271" hits="1"/>
						<line number="272" hits="1"/>
						<line number="275" hits="1"/>
						<line number="276" hits="1"/>
						<line number="279" hits="1"/>
						<line number="280" hits="1"/>
						<line number="283" hits="1"/>
						<line number="289" hits="1"/>
						<line number="291" hits="0"/>
						<line number="292" hits="0"/>
						<line number="302" hits="0"/>
						<line number="304" hits="1"/>
						<line number="305" hits="0"/>
						<line number="306" hits="0"/>
					</lines>
				</class>
				<class name="organization.py" filename="infrastructure/persistence/models/organization.py" complexity="0" line-rate="0.8493" branch-rate="0.5">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="50" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="59" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="64" hits="0"/>
						<line number="66" hits="1"/>
						<line number="68" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="69"/>
						<line number="69" hits="0"/>
						<line number="70" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="76"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="78" hits="1"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="95" hits="0"/>
						<line number="97" hits="1"/>
						<line number="98" hits="0"/>
						<line number="101" hits="1"/>
						<line number="104" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="112" hits="1"/>
						<line number="115" hits="1"/>
						<line number="116" hits="1"/>
						<line number="119" hits="1"/>
						<line number="122" hits="1"/>
						<line number="123" hits="1"/>
						<line number="126" hits="1"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="134" hits="0"/>
						<line number="136" hits="1"/>
						<line number="137" hits="1"/>
						<line number="139" hits="1"/>
						<line number="141" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="1"/>
						<line number="147" hits="1"/>
						<line number="149" hits="1"/>
						<line number="150" hits="1"/>
						<line number="151" hits="1"/>
						<line number="153" hits="1"/>
						<line number="155" hits="0"/>
						<line number="156" hits="0"/>
						<line number="168" hits="0"/>
						<line number="170" hits="1"/>
						<line number="171" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="infrastructure.repositories" line-rate="0.7265" branch-rate="0.6081" complexity="0">
			<classes>
				<class name="base.py" filename="infrastructure/repositories/base.py" complexity="0" line-rate="0.7265" branch-rate="0.6081">
					<methods/>
					<lines>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="35" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="46" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="80" hits="1"/>
						<line number="98" hits="1"/>
						<line number="99" hits="1"/>
						<line number="101" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="104"/>
						<line number="102" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="113" hits="1"/>
						<line number="132" hits="1"/>
						<line number="133" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="134"/>
						<line number="134" hits="0"/>
						<line number="138" hits="1"/>
						<line number="140" hits="1"/>
						<line number="162" hits="1"/>
						<line number="163" hits="1"/>
						<line number="166" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="170"/>
						<line number="167" hits="1"/>
						<line number="170" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="174"/>
						<line number="171" hits="1"/>
						<line number="174" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="178"/>
						<line number="175" hits="1"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1"/>
						<line number="183" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="187"/>
						<line number="184" hits="1"/>
						<line number="187" hits="1"/>
						<line number="188" hits="1"/>
						<line number="191" hits="1"/>
						<line number="192" hits="1"/>
						<line number="193" hits="0"/>
						<line number="194" hits="0"/>
						<line number="200" hits="1"/>
						<line number="208" hits="1"/>
						<line number="209" hits="1"/>
						<line number="211" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="212" hits="1"/>
						<line number="214" hits="1"/>
						<line number="217" hits="1"/>
						<line number="218" hits="1"/>
						<line number="219" hits="1"/>
						<line number="222" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="223" hits="1"/>
						<line number="226" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="227" hits="1"/>
						<line number="229" hits="1"/>
						<line number="230" hits="1"/>
						<line number="232" hits="1"/>
						<line number="233" hits="1"/>
						<line number="234" hits="1"/>
						<line number="235" hits="0"/>
						<line number="241" hits="1"/>
						<line number="263" hits="1"/>
						<line number="265" hits="1"/>
						<line number="268" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="269"/>
						<line number="269" hits="0"/>
						<line number="275" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="276" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="279"/>
						<line number="277" hits="1"/>
						<line number="279" hits="0"/>
						<line number="286" hits="1"/>
						<line number="287" hits="1"/>
						<line number="290" hits="0"/>
						<line number="291" hits="0"/>
						<line number="293" hits="0"/>
						<line number="294" hits="1"/>
						<line number="295" hits="0"/>
						<line number="296" hits="0"/>
						<line number="301" hits="1"/>
						<line number="302" hits="0"/>
						<line number="303" hits="0"/>
						<line number="309" hits="1"/>
						<line number="329" hits="1"/>
						<line number="330" hits="1"/>
						<line number="331" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="332" hits="1"/>
						<line number="334" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="335" hits="1"/>
						<line number="336" hits="1"/>
						<line number="338" hits="1"/>
						<line number="339" hits="1"/>
						<line number="340" hits="1"/>
						<line number="342" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="343"/>
						<line number="343" hits="0"/>
						<line number="345" hits="1"/>
						<line number="346" hits="0"/>
						<line number="347" hits="0"/>
						<line number="348" hits="0"/>
						<line number="354" hits="1"/>
						<line number="368" hits="1"/>
						<line number="369" hits="1"/>
						<line number="370" hits="1"/>
						<line number="372" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="373" hits="1"/>
						<line number="375" hits="1"/>
						<line number="376" hits="0"/>
						<line number="377" hits="0"/>
						<line number="378" hits="0"/>
						<line number="383" hits="0"/>
						<line number="384" hits="0"/>
						<line number="385" hits="0"/>
						<line number="391" hits="1"/>
						<line number="410" hits="0"/>
						<line number="411" hits="0"/>
						<line number="413" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="414,418"/>
						<line number="414" hits="0"/>
						<line number="415" hits="0"/>
						<line number="416" hits="0"/>
						<line number="418" hits="0"/>
						<line number="419" hits="0"/>
						<line number="420" hits="0"/>
						<line number="421" hits="0"/>
						<line number="427" hits="1"/>
						<line number="445" hits="1"/>
						<line number="446" hits="1"/>
						<line number="448" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="451"/>
						<line number="449" hits="1"/>
						<line number="451" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="454"/>
						<line number="452" hits="1"/>
						<line number="454" hits="1"/>
						<line number="455" hits="1"/>
						<line number="456" hits="0"/>
						<line number="457" hits="0"/>
						<line number="463" hits="1"/>
						<line number="469" hits="1"/>
						<line number="470" hits="1"/>
						<line number="472" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="475"/>
						<line number="473" hits="1"/>
						<line number="475" hits="1"/>
						<line number="477" hits="1"/>
						<line number="478" hits="1"/>
						<line number="479" hits="0"/>
						<line number="480" hits="0"/>
						<line number="486" hits="1"/>
						<line number="504" hits="1"/>
						<line number="505" hits="1"/>
						<line number="507" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="508" hits="1"/>
						<line number="510" hits="1"/>
						<line number="511" hits="1"/>
						<line number="512" hits="0"/>
						<line number="513" hits="0"/>
						<line number="519" hits="1"/>
						<line number="525" hits="1"/>
						<line number="526" hits="1"/>
						<line number="528" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="531"/>
						<line number="529" hits="1"/>
						<line number="531" hits="1"/>
						<line number="533" hits="1"/>
						<line number="534" hits="1"/>
						<line number="535" hits="0"/>
						<line number="536" hits="0"/>
						<line number="542" hits="1"/>
						<line number="544" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="545" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="544"/>
						<line number="546" hits="1"/>
						<line number="548" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="570"/>
						<line number="550" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="551"/>
						<line number="551" hits="0"/>
						<line number="552" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="553"/>
						<line number="553" hits="0"/>
						<line number="554" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="555"/>
						<line number="555" hits="0"/>
						<line number="556" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="557"/>
						<line number="557" hits="0"/>
						<line number="558" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="559"/>
						<line number="559" hits="0"/>
						<line number="560" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="561"/>
						<line number="561" hits="0"/>
						<line number="562" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="563"/>
						<line number="563" hits="0"/>
						<line number="564" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="566"/>
						<line number="565" hits="1"/>
						<line number="566" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="544,567"/>
						<line number="567" hits="0"/>
						<line number="570" hits="0"/>
						<line number="572" hits="1"/>
						<line number="574" hits="1"/>
						<line number="576" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="584"/>
						<line number="577" hits="1"/>
						<line number="579" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="580" hits="1"/>
						<line number="582" hits="1"/>
						<line number="584" hits="1"/>
						<line number="586" hits="1"/>
						<line number="588" hits="1"/>
						<line number="590" hits="1"/>
						<line number="592" hits="0"/>
						<line number="594" hits="1"/>
						<line number="596" hits="0"/>
						<line number="597" hits="0"/>
						<line number="599" hits="1"/>
						<line number="601" hits="0"/>
						<line number="604" hits="1"/>
						<line number="607" hits="1"/>
						<line number="608" hits="1"/>
						<line number="609" hits="1"/>
						<line number="611" hits="1"/>
						<line number="613" hits="1"/>
						<line number="615" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="618"/>
						<line number="616" hits="1"/>
						<line number="618" hits="1"/>
						<line number="620" hits="1"/>
						<line number="622" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
