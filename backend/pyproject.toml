[tool.poetry]
name = "sacra2-backend"
version = "2.0.0"
description = "Enterprise-grade backend for SACRA2 AI evaluation platform"
authors = ["SACRA2 Team <<EMAIL>>"]
readme = "README.md"
license = "MIT"
homepage = "https://sacra2.ai"
repository = "https://github.com/sacra2/sacra2-backend"
documentation = "https://docs.sacra2.ai"
keywords = ["ai", "compliance", "evaluation", "fastapi", "async"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.11"

# Core Framework
fastapi = {extras = ["all"], version = "^0.109.0"}
uvicorn = {extras = ["standard"], version = "^0.27.0"}
pydantic = "^2.5.0"
sqlalchemy = "^2.0.25"
alembic = "^1.13.1"

# Database
asyncpg = "^0.29.0"
redis = "^5.0.3"
psycopg2-binary = "^2.9.9"

# Authentication & Security
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
cryptography = "^42.0.0"

# Background Processing
celery = "^5.3.4"
rq = "^1.15.1"
flower = "^2.0.1"
pydantic-settings = "^2.0.0"

# Event Sourcing
eventsourcing = "^9.2.11"

# Monitoring & Observability
prometheus-client = "^0.19.0"
structlog = "^23.2.0"
sentry-sdk = {extras = ["fastapi"], version = "^1.39.2"}

# API & GraphQL
strawberry-graphql = {extras = ["fastapi"], version = "^0.215.1"}
graphene = "^3.3.0"

# Utilities
python-dotenv = "^1.0.0"
httpx = "^0.25.2"
tenacity = "^8.2.3"
aioredis = "^2.0.1"
aiokafka = "^0.10.0"

# Validation & Serialization
email-validator = "^2.1.0"
phonenumbers = "^8.13.26"

# Testing
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"
factory-boy = "^3.3.0"
faker = "^20.1.0"

[tool.poetry.group.dev.dependencies]
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"
pre-commit = "^3.6.0"
bandit = "^1.7.5"
safety = "^2.3.5"
aiosqlite = "^0.21.0"
greenlet = "^3.2.4"

[tool.poetry.group.performance.dependencies]
orjson = "^3.9.10"
uvloop = "^0.19.0"
cython = "^3.0.6"

[tool.poetry.scripts]
sacra2 = "src.main:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short --strict-markers"
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "e2e: End-to-end tests",
    "performance: Performance tests",
    "security: Security tests",
    "slow: Slow running tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/migrations/*",
    "*/alembic/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
]

[tool.bandit]
exclude_dirs = ["tests"]
skips = ["B101", "B601"]