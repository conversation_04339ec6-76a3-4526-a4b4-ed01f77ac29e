# SACRA2 Backend

A scalable, AI-powered compliance and risk assessment backend built with FastAPI, SQLAlchemy, and modern Python patterns.

## 🚀 Features

- **FastAPI Framework**: Modern, fast web framework for building APIs
- **Async/Await**: Full async support for high performance
- **SQLAlchemy ORM**: Powerful ORM with async support
- **PostgreSQL**: Robust, scalable database
- **Redis**: Caching and session management
- **Celery**: Distributed task queue for background jobs
- **Pydantic**: Data validation and serialization
- **Comprehensive Logging**: Structured logging with correlation IDs
- **Security**: JWT authentication, rate limiting, CORS protection
- **Testing**: Comprehensive test suite with pytest
- **Documentation**: Auto-generated API documentation
- **Monitoring**: Health checks and metrics

## 📋 Architecture Overview

```
src/
├── api/                    # API layer
│   ├── v1/                # API version 1
│   └── app.py             # FastAPI application factory
├── core/                  # Core utilities
│   ├── config.py          # Configuration management
│   ├── decorators.py      # Advanced decorators
│   ├── exceptions.py      # Custom exceptions
│   ├── logging.py         # Logging configuration
│   └── types.py           # Common type definitions
├── domain/                # Domain layer
│   ├── models/            # Domain models
│   └── services/          # Business logic
├── infrastructure/        # Infrastructure layer
│   ├── repositories/      # Data access layer
│   ├── database.py        # Database configuration
│   └── workers/           # Background workers
└── workers/               # Celery workers
    ├── tasks/             # Background tasks
    └── scheduler.py       # Task scheduler
```

## 🛠️ Installation

### Prerequisites

- Python 3.11+
- PostgreSQL 14+
- Redis 6+
- Docker (optional)

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Create virtual environment**
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -e .
   # or for development
   pip install -e ".[dev]"
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Set up database**
   ```bash
   python main.py init
   python main.py migrate
   ```

6. **Run development server**
   ```bash
   python main.py dev
   ```

### Docker Setup

1. **Build and run with Docker Compose**
   ```bash
   docker-compose up --build
   ```

2. **Access the application**
   - API: http://localhost:8000
   - Documentation: http://localhost:8000/docs
   - Health check: http://localhost:8000/health

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with the following variables:

```bash
# Application
APP_NAME=SACRA2 Backend
APP_VERSION=1.0.0
APP_ENVIRONMENT=development
APP_HOST=0.0.0.0
APP_PORT=8000
APP_DEBUG=true

# Database
DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/sacra2
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# AI Providers
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# Monitoring
ENABLE_METRICS=true
LOG_LEVEL=INFO
LOG_FILE=/var/log/sacra2/app.log
```

## 🧪 Testing

### Run tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test file
pytest tests/test_api.py

# Run with verbose output
pytest -v
```

### Test structure
```
tests/
├── unit/           # Unit tests
├── integration/    # Integration tests
├── fixtures/       # Test fixtures
└── conftest.py     # Pytest configuration
```

## 📊 API Documentation

### Access Documentation
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI Schema**: http://localhost:8000/openapi.json

### Authentication

Most endpoints require authentication via JWT tokens:

```bash
curl -H "Authorization: Bearer <token>" http://localhost:8000/api/v1/assessments
```

### Example API Calls

```bash
# Health check
curl http://localhost:8000/health

# Create assessment
curl -X POST http://localhost:8000/api/v1/assessments \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "GDPR Compliance Check",
    "type": "gdpr",
    "description": "GDPR compliance assessment"
  }'

# Get assessments
curl http://localhost:8000/api/v1/assessments \
  -H "Authorization: Bearer <token>"
```

## 🔧 Development

### Code Style

The project uses several tools for code quality:

- **Black**: Code formatting
- **isort**: Import sorting
- **mypy**: Type checking
- **ruff**: Linting
- **pre-commit**: Git hooks

### Setup development tools
```bash
# Install pre-commit hooks
pre-commit install

# Format code
black src/
isort src/

# Type checking
mypy src/

# Linting
ruff check src/
```

### Database Migrations

```bash
# Create migration
alembic revision --autogenerate -m "Add new table"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

### Background Workers

Start Celery worker:
```bash
celery -A src.workers.scheduler worker --loglevel=info
```

Start Celery beat scheduler:
```bash
celery -A src.workers.scheduler beat --loglevel=info
```

## 🚀 Deployment

### Production Deployment

1. **Build production image**
   ```bash
   docker build -t sacra2-backend:latest .
   ```

2. **Run with Docker Compose**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

3. **Environment variables**
   Ensure all production environment variables are set in `.env.prod`

### Health Checks

The application provides several health check endpoints:

- **Application health**: GET /health
- **Database health**: GET /health (includes database status)
- **Redis health**: GET /health (includes Redis status)

## 📈 Monitoring

### Logging

- **Structured JSON logging** in production
- **Correlation IDs** for request tracing
- **Security filtering** to prevent sensitive data leaks

### Metrics

Available at `/metrics` endpoint (when enabled):
- Request count and duration
- Database connection pool stats
- Memory and CPU usage
- Custom business metrics

### Health Monitoring

```bash
# Check application health
curl http://localhost:8000/health

# Check specific service
curl http://localhost:8000/health?service=database
```

## 🔒 Security

### Security Features

- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: Protection against abuse
- **CORS**: Cross-origin resource sharing configuration
- **Security Headers**: OWASP recommended headers
- **Input Validation**: Comprehensive request validation
- **SQL Injection Prevention**: Using SQLAlchemy ORM
- **XSS Protection**: Content security policy headers

### Security Best Practices

- Always use HTTPS in production
- Regular security updates
- Environment variable management
- Input sanitization
- Rate limiting configuration
- Audit logging

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check database connectivity
   python -c "import asyncio; from src.infrastructure.database import check_database_health; asyncio.run(check_database_health())"
   ```

2. **Redis Connection Issues**
   ```bash
   # Test Redis connection
   redis-cli ping
   ```

3. **Migration Issues**
   ```bash
   # Reset database (development only)
   alembic downgrade base
   alembic upgrade head
   ```

### Debug Mode

Enable debug mode in development:
```bash
export APP_DEBUG=true
python main.py dev
```

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
- [Celery Documentation](https://docs.celeryproject.org/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Redis Documentation](https://redis.io/documentation)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Check the documentation
- Review existing issues and discussions