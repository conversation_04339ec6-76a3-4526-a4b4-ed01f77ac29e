"""Initial migration - SACRA2 data model

Revision ID: 001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision: str = '001'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create all SACRA2 tables."""
    # Ensure PostgreSQL UUID extension is available for uuid_generate_v4()
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";')
    
    # ===============================
    # Drivers and Models
    # ===============================
    op.create_table(
        'drivers',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('name', sa.String(100), nullable=False, unique=True, index=True),
        sa.Column('type', sa.String(50), nullable=False, index=True),
        sa.Column('description', sa.String(500), nullable=True),
        sa.Column('default_config', sa.JSON, nullable=True),
        sa.Column('active', sa.Boolean, nullable=False, default=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.CheckConstraint("type IN ('litellm', 'openai', 'openrouter', 'vertex', 'bedrock', 'azure', 'anthropic', 'groq', 'huggingface')", name='driver_type_check'),
        sa.CheckConstraint("length(name) >= 1", name='driver_name_not_empty'),
        sa.CheckConstraint("length(type) >= 1", name='driver_type_not_empty'),
    )
    
    op.create_table(
        'providers',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('name', sa.String(100), nullable=False, index=True),
        sa.Column('url', sa.String(500), nullable=True),
        sa.Column('api_key', sa.String(1000), nullable=True),
        sa.Column('max_concurrent_requests', sa.Integer, nullable=False, default=10),
        sa.Column('driver_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('drivers.id'), nullable=False, index=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('driver_id', 'name', name='provider_driver_name_unique'),
        sa.CheckConstraint("max_concurrent_requests > 0", name='provider_max_concurrent_positive'),
        sa.CheckConstraint("length(name) >= 1", name='provider_name_not_empty'),
    )
    
    op.create_table(
        'llm_models',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('name', sa.String(100), nullable=False, index=True),
        sa.Column('model_version', sa.String(50), nullable=True),
        sa.Column('description', sa.String(500), nullable=True),
        sa.Column('requests_per_minute', sa.Integer, nullable=True),
        sa.Column('tokens_per_minute', sa.Integer, nullable=True),
        sa.Column('max_tokens', sa.Integer, nullable=True),
        sa.Column('supports_functions', sa.Boolean, nullable=False, default=False),
        sa.Column('supports_vision', sa.Boolean, nullable=False, default=False),
        sa.Column('provider_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('providers.id'), nullable=False, index=True),
        sa.Column('active', sa.Boolean, nullable=False, default=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('provider_id', 'name', 'model_version', name='model_provider_name_version_unique'),
        sa.CheckConstraint("requests_per_minute > 0 OR requests_per_minute IS NULL", name='model_rpm_positive'),
        sa.CheckConstraint("tokens_per_minute > 0 OR tokens_per_minute IS NULL", name='model_tpm_positive'),
        sa.CheckConstraint("max_tokens > 0 OR max_tokens IS NULL", name='model_max_tokens_positive'),
        sa.CheckConstraint("length(name) >= 1", name='model_name_not_empty'),
    )
    
    op.create_table(
        'model_default_params',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('key', sa.String(100), nullable=False, index=True),
        sa.Column('value', sa.String(1000), nullable=False),
        sa.Column('param_type', sa.String(20), nullable=False, default='string'),
        sa.Column('description', sa.String(500), nullable=True),
        sa.Column('model_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('llm_models.id'), nullable=False, index=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('model_id', 'key', name='model_param_key_unique'),
        sa.CheckConstraint("param_type IN ('string', 'integer', 'float', 'boolean', 'json')", name='param_type_check'),
        sa.CheckConstraint("length(key) >= 1", name='param_key_not_empty'),
        sa.CheckConstraint("length(value) >= 1", name='param_value_not_empty'),
    )
    
    # ===============================
    # Organization
    # ===============================
    op.create_table(
        'tenants',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('name', sa.String(200), nullable=False, unique=True, index=True),
        sa.Column('display_name', sa.String(255), nullable=True),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('contact_email', sa.String(255), nullable=True),
        sa.Column('contact_phone', sa.String(50), nullable=True),
        sa.Column('settings', sa.JSON, nullable=True),
        sa.Column('quota_config', sa.JSON, nullable=True),
        sa.Column('active', sa.Boolean, nullable=False, default=True),
        sa.Column('trial', sa.Boolean, nullable=False, default=True),
        sa.Column('plan', sa.String(50), nullable=False, default='free'),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.CheckConstraint("length(name) >= 1", name='tenant_name_not_empty'),
        sa.CheckConstraint("plan IN ('free', 'starter', 'professional', 'enterprise')", name='tenant_plan_check'),
        sa.CheckConstraint("contact_email IS NULL OR contact_email LIKE '%@%'", name='tenant_email_format'),
    )
    
    op.create_table(
        'projects',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('name', sa.String(200), nullable=False, index=True),
        sa.Column('display_name', sa.String(255), nullable=True),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('settings', sa.JSON, nullable=True),
        sa.Column('active', sa.Boolean, nullable=False, default=True),
        sa.Column('archived', sa.Boolean, nullable=False, default=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('tenants.id'), nullable=False, index=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('tenant_id', 'name', name='project_tenant_name_unique'),
        sa.CheckConstraint("length(name) >= 1", name='project_name_not_empty'),
    )
    
    op.create_table(
        'tenant_provider_configs',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('tenants.id'), nullable=False, index=True),
        sa.Column('provider_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('providers.id'), nullable=False, index=True),
        sa.Column('api_key', sa.String(1000), nullable=False),
        sa.Column('max_concurrent_requests', sa.Integer, nullable=True),
        sa.Column('enabled', sa.Boolean, nullable=False, default=True),
        sa.Column('config', sa.JSON, nullable=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('tenant_id', 'provider_id', name='tenant_provider_unique'),
        sa.CheckConstraint("max_concurrent_requests > 0 OR max_concurrent_requests IS NULL", name='tenant_max_concurrent_positive'),
        sa.CheckConstraint("length(api_key) >= 1", name='tenant_api_key_not_empty'),
    )
    
    # ===============================
    # Evaluation Framework
    # ===============================
    op.create_table(
        'probes',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('code', sa.String(100), nullable=False, unique=True, index=True),
        sa.Column('name', sa.String(200), nullable=False, index=True),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('params', sa.JSON, nullable=True),
        sa.Column('probe_version', sa.String(50), nullable=False, default='1.0.0'),
        sa.Column('category', sa.String(100), nullable=True, index=True),
        sa.Column('tags', sa.JSON, nullable=True),
        sa.Column('active', sa.Boolean, nullable=False, default=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('name', 'probe_version', name='probe_name_version_unique'),
        sa.CheckConstraint("length(code) >= 1", name='probe_code_not_empty'),
        sa.CheckConstraint("length(name) >= 1", name='probe_name_not_empty'),
        sa.CheckConstraint("length(probe_version) >= 1", name='probe_version_not_empty'),
    )
    
    op.create_table(
        'probesets',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('code', sa.String(100), nullable=False, unique=True, index=True),
        sa.Column('name', sa.String(200), nullable=False, index=True),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('probeset_version', sa.String(50), nullable=False, default='1.0.0'),
        sa.Column('scoring_method', sa.String(20), nullable=False, default='avg'),
        sa.Column('scoring_config', sa.JSON, nullable=True),
        sa.Column('category', sa.String(100), nullable=True, index=True),
        sa.Column('tags', sa.JSON, nullable=True),
        sa.Column('active', sa.Boolean, nullable=False, default=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('name', 'probeset_version', name='probeset_name_version_unique'),
        sa.CheckConstraint("scoring_method IN ('binary', 'avg', 'weighted', 'rule', 'custom')", name='probeset_scoring_method_check'),
        sa.CheckConstraint("length(code) >= 1", name='probeset_code_not_empty'),
        sa.CheckConstraint("length(name) >= 1", name='probeset_name_not_empty'),
    )
    
    op.create_table(
        'probeset_probes',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('probeset_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('probesets.id'), nullable=False, index=True),
        sa.Column('probe_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('probes.id'), nullable=False, index=True),
        sa.Column('order', sa.Integer, nullable=False, default=0),
        sa.Column('weight', sa.Float, nullable=False, default=1.0),
        sa.Column('override_params', sa.JSON, nullable=True),
        sa.Column('enabled', sa.Boolean, nullable=False, default=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('probeset_id', 'probe_id', name='probeset_probe_unique'),
        sa.CheckConstraint("weight > 0", name='probeset_probe_weight_positive'),
        sa.CheckConstraint('"order" >= 0', name='probeset_probe_order_non_negative'),
    )
    
    op.create_table(
        'capabilities',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('code', sa.String(100), nullable=False, unique=True, index=True),
        sa.Column('name', sa.String(200), nullable=False, unique=True, index=True),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('category', sa.String(100), nullable=True, index=True),
        sa.Column('severity', sa.String(20), nullable=False, default='medium'),
        sa.Column('active', sa.Boolean, nullable=False, default=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.CheckConstraint("severity IN ('low', 'medium', 'high', 'critical')", name='capability_severity_check'),
        sa.CheckConstraint("length(code) >= 1", name='capability_code_not_empty'),
        sa.CheckConstraint("length(name) >= 1", name='capability_name_not_empty'),
    )
    
    op.create_table(
        'capability_sets',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('capability_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('capabilities.id'), nullable=False, index=True),
        sa.Column('probeset_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('probesets.id'), nullable=False, index=True),
        sa.Column('weight', sa.Float, nullable=False, default=1.0),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('capability_id', 'probeset_id', name='capability_probeset_unique'),
        sa.CheckConstraint("weight >= 0", name='capability_set_weight_non_negative'),
    )
    
    op.create_table(
        'assessments',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('code', sa.String(100), nullable=False, unique=True, index=True),
        sa.Column('name', sa.String(200), nullable=False, unique=True, index=True),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('assessment_version', sa.String(50), nullable=False, default='1.0.0'),
        sa.Column('category', sa.String(100), nullable=True, index=True),
        sa.Column('active', sa.Boolean, nullable=False, default=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.CheckConstraint("length(code) >= 1", name='assessment_code_not_empty'),
        sa.CheckConstraint("length(name) >= 1", name='assessment_name_not_empty'),
        sa.CheckConstraint("length(assessment_version) >= 1", name='assessment_version_not_empty'),
    )
    
    op.create_table(
        'assessment_capabilities',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('assessment_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('assessments.id'), nullable=False, index=True),
        sa.Column('capability_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('capabilities.id'), nullable=False, index=True),
        sa.Column('weight', sa.Float, nullable=False, default=1.0),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('assessment_id', 'capability_id', name='assessment_capability_unique'),
        sa.CheckConstraint("weight >= 0", name='assessment_capability_weight_non_negative'),
    )
    
    # ===============================
    # Execution and Results
    # ===============================
    op.create_table(
        'evaluation_runs',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('name', sa.String(200), nullable=False, index=True),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('status', sa.String(20), nullable=False, default='queued', index=True),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('error_message', sa.Text, nullable=True),
        sa.Column('retry_count', sa.Integer, nullable=False, default=0),
        sa.Column('config', sa.JSON, nullable=True),
        sa.Column('total_probes', sa.Integer, nullable=True),
        sa.Column('completed_probes', sa.Integer, nullable=True, default=0),
        sa.Column('assessment_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('assessments.id'), nullable=False, index=True),
        sa.Column('project_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('projects.id'), nullable=False, index=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.CheckConstraint("status IN ('queued', 'running', 'completed', 'failed', 'cancelled')", name='evaluation_status_check'),
        sa.CheckConstraint("retry_count >= 0", name='evaluation_retry_count_non_negative'),
        sa.CheckConstraint("total_probes >= 0 OR total_probes IS NULL", name='evaluation_total_probes_non_negative'),
        sa.CheckConstraint("completed_probes >= 0 OR completed_probes IS NULL", name='evaluation_completed_probes_non_negative'),
        sa.CheckConstraint("length(name) >= 1", name='evaluation_name_not_empty'),
    )
    
    op.create_table(
        'run_models',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('model_params_snapshot', sa.JSON, nullable=False),
        sa.Column('status', sa.String(20), nullable=False, default='pending', index=True),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_probes', sa.Integer, nullable=False, default=0),
        sa.Column('failed_probes', sa.Integer, nullable=False, default=0),
        sa.Column('evaluation_run_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('evaluation_runs.id'), nullable=False, index=True),
        sa.Column('model_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('llm_models.id'), nullable=False, index=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('evaluation_run_id', 'model_id', name='run_model_unique'),
        sa.CheckConstraint("status IN ('pending', 'running', 'completed', 'failed')", name='run_model_status_check'),
        sa.CheckConstraint("completed_probes >= 0", name='run_model_completed_probes_non_negative'),
        sa.CheckConstraint("failed_probes >= 0", name='run_model_failed_probes_non_negative'),
    )
    
    op.create_table(
        'run_probe_results',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('score', sa.Float, nullable=False),
        sa.Column('execution_log', sa.JSON, nullable=True),
        sa.Column('model_params_used', sa.JSON, nullable=False),
        sa.Column('executed_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('status', sa.String(20), nullable=False, default='success'),
        sa.Column('version_hash', sa.String(64), nullable=False, index=True),
        sa.Column('execution_time_ms', sa.Integer, nullable=True),
        sa.Column('tokens_used', sa.Integer, nullable=True),
        sa.Column('cost_usd', sa.Float, nullable=True),
        sa.Column('model_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('llm_models.id'), nullable=False, index=True),
        sa.Column('probe_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('probes.id'), nullable=False, index=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('model_id', 'probe_id', 'version_hash', name='probe_result_cache_unique'),
        sa.CheckConstraint("score >= 0 AND score <= 1", name='probe_result_score_range'),
        sa.CheckConstraint("status IN ('success', 'failed', 'timeout', 'error')", name='probe_result_status_check'),
        sa.CheckConstraint("execution_time_ms >= 0 OR execution_time_ms IS NULL", name='probe_result_execution_time_non_negative'),
        sa.CheckConstraint("tokens_used >= 0 OR tokens_used IS NULL", name='probe_result_tokens_non_negative'),
        sa.CheckConstraint("cost_usd >= 0 OR cost_usd IS NULL", name='probe_result_cost_non_negative'),
    )
    
    op.create_table(
        'run_probeset_results',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('score', sa.Float, nullable=False),
        sa.Column('details', sa.JSON, nullable=True),
        sa.Column('calculated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('probe_count', sa.Integer, nullable=False, default=0),
        sa.Column('successful_probes', sa.Integer, nullable=False, default=0),
        sa.Column('run_model_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('run_models.id'), nullable=False, index=True),
        sa.Column('probeset_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('probesets.id'), nullable=False, index=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('run_model_id', 'probeset_id', name='probeset_result_unique'),
        sa.CheckConstraint("score >= 0 AND score <= 1", name='probeset_result_score_range'),
        sa.CheckConstraint("probe_count >= 0", name='probeset_result_probe_count_non_negative'),
        sa.CheckConstraint("successful_probes >= 0", name='probeset_result_successful_probes_non_negative'),
        sa.CheckConstraint("successful_probes <= probe_count", name='probeset_result_successful_probes_valid'),
    )
    
    op.create_table(
        'run_capability_scores',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('score', sa.Float, nullable=False),
        sa.Column('calculated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('probeset_count', sa.Integer, nullable=False, default=0),
        sa.Column('details', sa.JSON, nullable=True),
        sa.Column('run_model_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('run_models.id'), nullable=False, index=True),
        sa.Column('capability_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('capabilities.id'), nullable=False, index=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('run_model_id', 'capability_id', name='capability_score_unique'),
        sa.CheckConstraint("score >= 0 AND score <= 1", name='capability_score_range'),
        sa.CheckConstraint("probeset_count >= 0", name='capability_score_probeset_count_non_negative'),
    )
    
    op.create_table(
        'run_assessment_scores',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.func.uuid_generate_v4()),
        sa.Column('score', sa.Float, nullable=False),
        sa.Column('calculated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('capability_count', sa.Integer, nullable=False, default=0),
        sa.Column('details', sa.JSON, nullable=True),
        sa.Column('run_model_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('run_models.id'), nullable=False, index=True),
        sa.Column('assessment_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('assessments.id'), nullable=False, index=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(255), nullable=True),
        sa.Column('updated_by', sa.String(255), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', sa.String(255), nullable=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        
        sa.UniqueConstraint('run_model_id', 'assessment_id', name='assessment_score_unique'),
        sa.CheckConstraint("score >= 0 AND score <= 1", name='assessment_score_range'),
        sa.CheckConstraint("capability_count >= 0", name='assessment_score_capability_count_non_negative'),
    )
    
    # Create indexes for performance
    op.create_index('idx_drivers_type_active', 'drivers', ['type', 'active'])
    op.create_index('idx_providers_driver', 'providers', ['driver_id'])
    op.create_index('idx_models_provider_active', 'llm_models', ['provider_id', 'active'])
    op.create_index('idx_probes_category_active', 'probes', ['category', 'active'])
    op.create_index('idx_probesets_category_active', 'probesets', ['category', 'active'])
    op.create_index('idx_capabilities_severity_active', 'capabilities', ['severity', 'active'])
    op.create_index('idx_evaluation_runs_status_project', 'evaluation_runs', ['status', 'project_id'])
    op.create_index('idx_run_models_status', 'run_models', ['status'])
    op.create_index('idx_probe_results_model_probe', 'run_probe_results', ['model_id', 'probe_id'])


def downgrade() -> None:
    """Drop all SACRA2 tables."""
    
    # Drop tables in reverse order to avoid foreign key constraints
    op.drop_table('run_assessment_scores')
    op.drop_table('run_capability_scores')
    op.drop_table('run_probeset_results')
    op.drop_table('run_probe_results')
    op.drop_table('run_models')
    op.drop_table('evaluation_runs')
    op.drop_table('assessment_capabilities')
    op.drop_table('assessments')
    op.drop_table('capability_sets')
    op.drop_table('capabilities')
    op.drop_table('probeset_probes')
    op.drop_table('probesets')
    op.drop_table('probes')
    op.drop_table('tenant_provider_configs')
    op.drop_table('projects')
    op.drop_table('tenants')
    op.drop_table('model_default_params')
    op.drop_table('llm_models')
    op.drop_table('providers')
    op.drop_table('drivers')