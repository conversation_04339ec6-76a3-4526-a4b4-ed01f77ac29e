# SACRA2 Execution Results Optimization

## Economic Optimization Strategy

To avoid re-running expensive LLM probe executions for each evaluation run, we implement a caching strategy using individual probe results per model. This allows:

1. **Cost Reduction**: Avoid duplicate API calls for the same probe/model combination
2. **Performance**: Faster evaluation runs using cached results
3. **Flexibility**: Calculate probeset/capability/assessment scores on-demand from cached data

## Optimized Execution & Results Model

```mermaid {align: "center", codeblock: true}
erDiagram

    %% =========================
    %% Execution & Results (Optimized)
    %% =========================
    EVALUATION_RUN {
        string id PK
        string assessment_id FK
        string project_id FK
        string name
        string status "queued|running|done|failed"
        datetime created_at
        %% FK: assessment_id -> ASSESSMENT.id ; project_id -> PROJECTS.id
        %% NOTES: Specific launch of an ASSESSMENT
    }
    RUN_MODEL {
        string id PK
        string run_id FK
        string model_id FK
        json   model_params_snapshot
        %% FK: run_id -> EVALUATION_RUN.id ; model_id -> LLM_MODELS.id
        %% UNIQUE: (run_id, model_id)
        %% NOTES: Allows evaluating multiple LLMs in the same run
    }
    RUN_PROBE_RESULT {
        string id PK
        string model_id FK
        string probe_id FK
        float  score "0..1"
        json   execution_log
        json   model_params_used
        datetime executed_at
        string status "success|failed|timeout"
        string version_hash
        %% FK: model_id -> LLM_MODELS.id ; probe_id -> PROBE.id
        %% UNIQUE: (model_id, probe_id, version_hash)
        %% NOTES: Individual probe execution result per model; cached to avoid re-running
        %% NOTES: version_hash includes probe version + model params for cache invalidation
    }
    RUN_PROBESET_RESULT {
        string id PK
        string run_model_id FK
        string probeset_id FK
        float  score "0..1"
        json   details
        datetime calculated_at
        %% FK: run_model_id -> RUN_MODEL.id ; probeset_id -> PROBESET.id
        %% UNIQUE: (run_model_id, probeset_id)
        %% NOTES: Aggregated from RUN_PROBE_RESULT using probeset scoring method
        %% NOTES: Can be calculated on-demand or cached for performance
    }
    RUN_CAPABILITY_SCORE {
        string id PK
        string run_model_id FK
        string capability_id FK
        float  score "0..1"
        datetime calculated_at
        %% FK: run_model_id -> RUN_MODEL.id ; capability_id -> CAPABILITY.id
        %% UNIQUE: (run_model_id, capability_id)
        %% NOTES: Calculated from RUN_PROBE_RESULT via probeset weights
        %% NOTES: Weighted aggregation of probe results grouped by capability
    }
    RUN_ASSESSMENT_SCORE {
        string id PK
        string run_model_id FK
        string assessment_id FK
        float  score "0..1"
        datetime calculated_at
        %% FK: run_model_id -> RUN_MODEL.id ; assessment_id -> ASSESSMENT.id
        %% UNIQUE: (run_model_id, assessment_id)
        %% NOTES: Calculated from RUN_PROBE_RESULT via capability weights (e.g., SACRA)
        %% NOTES: Top-level aggregation of all probe results for the assessment
    }

    %% =========================
    %% Relations
    %% =========================
    PROJECTS ||--o{ EVALUATION_RUN : "runs"

    ASSESSMENT ||--o{ EVALUATION_RUN : "executed in"
    PROBE ||--o{ RUN_PROBE_RESULT : "executed"

    LLM_MODELS ||--o{ RUN_MODEL : "evaluated"
    LLM_MODELS ||--o{ RUN_PROBE_RESULT : "cached results"
    EVALUATION_RUN ||--o{ RUN_MODEL : "targets"
    RUN_MODEL ||--o{ RUN_PROBESET_RESULT : "scores"
    RUN_MODEL ||--o{ RUN_CAPABILITY_SCORE : "roll-up"
    RUN_MODEL ||--o{ RUN_ASSESSMENT_SCORE : "roll-up"
```

See [sacra2-model.md](sacra2-model.md) for the full model.

## Implementation Logic

### 1. Probe Execution Strategy

```sql
-- Check if probe result exists for model
SELECT score, execution_log, executed_at 
FROM RUN_PROBE_RESULT 
WHERE model_id = ? AND probe_id = ? AND version_hash = ?;

-- If not found, execute probe and cache result
INSERT INTO RUN_PROBE_RESULT (model_id, probe_id, score, execution_log, model_params_used, executed_at, status, version_hash)
VALUES (?, ?, ?, ?, ?, NOW(), 'success', ?);
```

### 2. Probeset Score Calculation

```sql
-- Calculate probeset score from cached probe results
WITH probe_scores AS (
    SELECT rpr.score, pp.order
    FROM RUN_PROBE_RESULT rpr
    JOIN PROBESET_PROBES pp ON rpr.probe_id = pp.probe_id
    WHERE pp.probeset_id = ? AND rpr.model_id = ?
)
SELECT 
    CASE ps.scoring_method
        WHEN 'binary' THEN CASE WHEN MIN(score) = 1 THEN 1 ELSE 0 END
        WHEN 'avg' THEN AVG(score)
        WHEN 'rule' THEN apply_scoring_rules(ps.scoring_config, probe_scores)
        WHEN 'custom' THEN apply_custom_scoring(ps.scoring_config, probe_scores)
    END as probeset_score
FROM PROBESET ps, probe_scores
WHERE ps.id = ?;
```

### 3. Capability Score Calculation

```sql
-- Calculate capability score from probe results via probeset weights
WITH probeset_scores AS (
    SELECT cs.weight, calculate_probeset_score(cs.probeset_id, ?) as score
    FROM CAPABILITY_SETS cs
    WHERE cs.capability_id = ?
)
SELECT SUM(weight * score) / SUM(weight) as capability_score
FROM probeset_scores;
```

### 4. Assessment Score Calculation

```sql
-- Calculate assessment score from probe results via capability weights
WITH capability_scores AS (
    SELECT ac.weight, calculate_capability_score(ac.capability_id, ?) as score
    FROM ASSESSMENT_CAPABILITIES ac
    WHERE ac.assessment_id = ?
)
SELECT SUM(weight * score) / SUM(weight) as assessment_score
FROM capability_scores;
```

## Benefits

### Economic Benefits
- **Reduced API Costs**: Avoid duplicate probe executions for same model/probe combinations
- **Faster Evaluations**: Use cached results when available
- **Incremental Updates**: Only run new or changed probes

### Technical Benefits
- **Granular Caching**: Cache at the most granular level (individual probes)
- **Flexible Aggregation**: Calculate higher-level scores on-demand
- **Version Control**: Track probe and parameter changes for cache invalidation
- **Audit Trail**: Complete execution logs for each probe

### Operational Benefits
- **Scalability**: Reduced load on LLM providers
- **Reliability**: Cached results available even if provider is down
- **Analytics**: Rich data for probe performance analysis

## Cache Invalidation Strategy

The `version_hash` field ensures cache validity by including:
- Probe version/content hash
- Model parameter hash
- Any relevant configuration changes

When any component changes, a new hash is generated, ensuring fresh execution while preserving valid cached results.

## Migration Strategy

1. **Add RUN_PROBE_RESULT table** to existing schema
2. **Populate historical data** from existing RUN_PROBESET_RESULT details
3. **Update evaluation engine** to check cache before execution
4. **Implement aggregation functions** for on-demand score calculation
5. **Add monitoring** for cache hit rates and performance metrics

