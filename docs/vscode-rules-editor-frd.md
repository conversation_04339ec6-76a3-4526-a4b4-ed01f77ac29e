# VSCodeRulesEditor Component - Functional Requirements Document

## 1. Overview

The VSCodeRulesEditor is a React component that provides a VSCode-like file explorer and editor interface for managing rule files. It combines a collapsible file tree sidebar with an integrated Monaco editor for code editing.

## 2. Component Interface

### 2.1 Props
```typescript
interface VSCodeRulesEditorProps {
  initialTreeData?: TreeNode[];
}

interface VSCodeRulesEditorHandle {
  format: () => void;
  save: () => { fileId?: string; fileName?: string; content?: string } | undefined;
  reload: () => void;
  run: () => void;
}
```

### 2.2 Ref Interface
The component uses `forwardRef` to expose imperative methods:
- `format()`: Triggers Monaco editor formatting
- `save()`: Returns current file info and content
- `reload()`: Resets tree to initial state
- `run()`: Placeholder for running tests/validation

## 3. Data Structure

### 3.1 TreeNode Interface
```typescript
interface TreeNode {
  id: string;           // Unique identifier
  name: string;         // Display name
  type: 'folder' | 'file';
  extension?: string;   // File extension (for files only)
  content?: string;     // File content (for files only)
  children?: TreeNode[]; // Child nodes (for folders only)
  isExpanded?: boolean; // Expansion state (for folders only)
}
```

## 4. UI Components

### 4.1 Layout Structure
```
┌───────────────────────────────────────────────────────────┐
│ [Sidebar]               │ [Main Editor Area]              │
│ ┌─────────────────────┐ │ ┌─────────────────────────────┐ │
│ │ PROBE RULES EXPLORER│ │ │ Editor Header               │ │
│ │ [Search Box]        │ │ ├─────────────────────────────┤ │
│ │ [File Tree]         │ │ │ Monaco Editor               │ │
│ │                     │ │ │                             │ │
│ │                     │ │ │                             │ │
│ └─────────────────────┘ │ └─────────────────────────────┘ │
└───────────────────────────────────────────────────────────┘
```

### 4.2 Sidebar (File Explorer)
- **Header**: "PROBE RULES EXPLORER" with collapse button
- **Search**: Real-time file/folder name filtering
- **Tree**: Hierarchical file/folder structure
- **Resizable**: Drag handle between sidebar and editor (200-600px range)
- **Collapsible**: Can be collapsed to 0px width

### 4.3 File Tree Items
Each tree item displays:
- **Fold/Unfold Icon**: Small chevron (folders only, 2x2px)
- **File Type Icon**: VSCode-style colored icons with extension labels
- **Name**: File/folder name with white text
- **Hover Actions**: Create file, create folder, rename (on hover)

### 4.4 File Type Icons
Custom SVG icons (18x16px) with colored backgrounds and white text labels:
- **YAML/YML**: Red background, "YML" text
- **TypeScript**: Blue background, "TS" text
- **TSX**: Cyan background, "TSX" text
- **JSON**: Gold background, "JSON" text
- **JSONL**: Gold background, "JSONL" text
- **JavaScript**: Yellow background, "JS" text
- **CSS/SCSS**: Blue background, "CSS" text
- **HTML**: Orange background, "HTML" text
- **Python**: Blue background, "PY" text
- **Markdown**: Dark blue background, "MD" text
- **Folders**: Blue lucide-react folder icons

### 4.5 Monaco Editor Area
- **Header**: Shows file icon, name, language, and status indicator
- **Editor**: Full Monaco editor with VSCode dark theme
- **Empty State**: Shows file icon and instructions when no file selected

## 5. Functional Requirements

### 5.1 File Tree Operations
- **Navigate**: Click to select files/folders
- **Expand/Collapse**: Click chevron or double-click folders
- **Search**: Filter tree items by name (case-insensitive)
- **Create File**: Right-click folder → "New File" or hover action button
- **Create Folder**: Right-click folder → "New Folder" or hover action button
- **Rename**: Right-click → "Rename", hover action button, or press Enter on selected item
- **Delete**: Right-click → "Delete" or press Delete key on selected item
- **Duplicate**: Right-click → "Duplicate"

### 5.2 Drag & Drop
- **Visual Feedback**: Items show drag indicators (before/after/inside)
- **Drop Zones**: 
  - Files: before/after positions
  - Folders: before/after/inside positions
- **Move Operations**: Drag items to reorder or move into folders
- **Restrictions**: Cannot drop item on itself

### 5.3 Context Menu
Right-click on tree items shows context menu with:
- **New File** (folders only)
- **New Folder** (folders only)
- **Rename**
- **Duplicate**
- **Delete**

### 5.4 Keyboard Shortcuts
- **Enter**: Rename selected item
- **Delete**: Delete selected item
- **Ctrl+S**: Save current file (Monaco editor)
- **Shift+Alt+F**: Format current file (Monaco editor)

### 5.5 Editor Integration
- **File Selection**: Clicking file loads content in Monaco editor
- **Content Sync**: Editor changes update tree node content
- **Language Detection**: Automatic language mode based on file extension
- **Theme**: VSCode dark theme with syntax highlighting

### 5.6 Responsive Behavior
- **Sidebar Resize**: Drag handle to resize (200-600px)
- **Collapse**: Button to hide/show sidebar
- **Transitions**: Smooth animations for expand/collapse operations

## 6. State Management

### 6.1 Component State
- `treeData`: Current tree structure
- `selectedFile`: Currently selected file node
- `contextMenu`: Context menu position and target
- `searchQuery`: Current search filter
- `isSidebarCollapsed`: Sidebar visibility state
- `sidebarWidth`: Current sidebar width in pixels
- `isResizing`: Resize operation state

### 6.2 Monaco Editor State
- `monacoEditorRef`: Reference to Monaco editor instance
- `monacoApiRef`: Reference to Monaco API

## 7. Styling Requirements

### 7.1 Color Scheme
- **Background**: Dark gray (#111827, #1f2937, #374151)
- **Text**: White/light gray (#ffffff, #f3f4f6)
- **Folders**: Blue (#3b82f6)
- **Selection**: Gray highlight (#4b5563)
- **Hover**: Lighter gray (#6b7280)

### 7.2 Typography
- **Font**: System font stack
- **Size**: 14px for tree items, 12px for headers
- **Weight**: Normal, semibold for icons

### 7.3 Spacing
- **Indentation**: 12px per tree level
- **Padding**: 2px vertical, 8px horizontal for tree items
- **Gaps**: 4px between icons and text

## 8. Performance Requirements

### 8.1 Tree Operations
- **Search**: Real-time filtering with debouncing
- **Rendering**: Efficient re-renders using React keys
- **Memory**: Minimal state updates for large trees

### 8.2 Editor Loading
- **Monaco**: Lazy load from CDN
- **Theme**: Apply custom VSCode dark theme
- **Language**: Dynamic language switching

## 9. Error Handling

### 9.1 File Operations
- **Invalid Names**: Prevent empty or duplicate names
- **Delete Confirmation**: Implicit confirmation through context menu
- **Drag Drop**: Prevent invalid operations

### 9.2 Editor Integration
- **Monaco Load**: Graceful fallback if CDN fails
- **Content Sync**: Handle editor/tree state conflicts

## 10. Accessibility

### 10.1 Keyboard Navigation
- **Tab Order**: Logical focus sequence
- **Arrow Keys**: Tree navigation
- **Enter/Space**: Activate items

### 10.2 Screen Readers
- **ARIA Labels**: Descriptive labels for interactive elements
- **Role Attributes**: Proper semantic roles
- **Focus Management**: Clear focus indicators

## 11. Browser Compatibility

- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Monaco Editor**: Follows Monaco's browser requirements
- **CSS Features**: Uses modern CSS (Grid, Flexbox, CSS Variables)

## 12. Implementation Notes

### 12.1 Dependencies
- **React**: 18+ with hooks support
- **Lucide React**: For icons
- **Monaco Editor**: Loaded from CDN
- **Tailwind CSS**: For styling

### 12.2 File Structure
```
VSCodeRulesEditor/
├── index.tsx              # Main component
├── components/
│   ├── TreeItem.tsx       # Individual tree item
│   ├── ContextMenu.tsx    # Right-click menu
│   └── MonacoEditor.tsx   # Editor wrapper
├── types/
│   └── TreeNode.ts        # Type definitions
└── utils/
    ├── fileIcons.ts       # Icon mapping
    └── treeOperations.ts  # Tree manipulation
```

### 12.3 Testing Strategy
- **Unit Tests**: Component logic and state management
- **Integration Tests**: File operations and editor sync
- **E2E Tests**: Complete user workflows
- **Accessibility Tests**: Screen reader and keyboard navigation

## 13. Future Enhancements

### 13.1 Potential Features
- **File Upload**: Drag files from OS
- **Multi-Select**: Select multiple items
- **Tabs**: Multiple open files
- **Split View**: Side-by-side editing
- **Git Integration**: File status indicators
- **Minimap**: Code overview
- **Find/Replace**: Global search across files

### 13.2 Performance Optimizations
- **Virtual Scrolling**: For large trees
- **Code Splitting**: Lazy load Monaco features
- **Memoization**: Optimize re-renders
- **Web Workers**: Background processing
