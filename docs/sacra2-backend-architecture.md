# SACRA2 Backend Architecture: Enterprise-Grade Python Microservices

## Executive Summary

This document defines the comprehensive backend architecture for SACRA2 (Scalable AI Capability & Risk Assessment), an enterprise-grade platform for evaluating AI agents and language models. The backend is built on modern Python patterns using FastAPI, implementing advanced architectural patterns including event sourcing, CQRS, domain-driven design, and zero-trust security.

### Key Architectural Principles

- **Clean Architecture**: Separation of concerns with clear boundaries between layers
- **Domain-Driven Design**: Business logic encapsulated in domain models and services  
- **Event-Driven Architecture**: Asynchronous processing with event sourcing and CQRS
- **Zero-Trust Security**: Multiple layers of authentication, authorization, and encryption
- **Cloud-Native Design**: Containerized, horizontally scalable, and resilient
- **Economic Optimization**: Intelligent caching to reduce AI API costs by up to 80%

---

## Table of Contents

1. [Core Architecture](#core-architecture)
2. [Service Architecture](#service-architecture)
3. [Domain-Driven Design](#domain-driven-design)
4. [Advanced Python Patterns](#advanced-python-patterns)
5. [Modern API Design](#modern-api-design)
6. [Repository Pattern Implementation](#repository-pattern-implementation)
7. [Service Layer Architecture](#service-layer-architecture)
8. [Authentication & Authorization](#authentication--authorization)
9. [Background Processing Architecture](#background-processing-architecture)
10. [Database Design & Optimization](#database-design--optimization)
11. [Caching & Performance](#caching--performance)
12. [Security Architecture](#security-architecture)
13. [Event-Driven Architecture](#event-driven-architecture)
14. [Testing & Quality Assurance](#testing--quality-assurance)
15. [Monitoring & Observability](#monitoring--observability)
16. [DevOps & Deployment](#devops--deployment)
17. [Performance Optimization](#performance-optimization)
18. [Disaster Recovery](#disaster-recovery)

---

## Core Architecture

### System Overview

```mermaid
graph TB
    subgraph "API Gateway Layer"
        AG[API Gateway/Load Balancer]
        OAuth[OAuth2 Proxy]
    end
    
    subgraph "Application Layer"
        API[FastAPI Services]
        GQL[GraphQL Server]
        WS[WebSocket Server]
    end
    
    subgraph "Business Logic Layer"
        DOM[Domain Services]
        EVT[Event Processors]
        WF[Workflow Engine]
    end
    
    subgraph "Data Access Layer"
        REPO[Repository Pattern]
        UOW[Unit of Work]
        CACHE[Cache Manager]
    end
    
    subgraph "Infrastructure Layer"
        DB[(PostgreSQL)]
        REDIS[(Redis)]
        ES[(Event Store)]
        MQ[Message Queue]
    end
    
    AG --> OAuth --> API
    API --> DOM --> REPO --> DB
    DOM --> EVT --> ES
    EVT --> MQ
    API --> CACHE --> REDIS
    API --> WS
    API --> GQL
```

### Architectural Layers

```python
# Core architectural structure
sacra-backend/
├── src/
│   ├── api/                      # Presentation Layer
│   │   ├── v1/                  # API Version 1
│   │   │   ├── endpoints/       # FastAPI route handlers
│   │   │   ├── graphql/         # GraphQL schema and resolvers
│   │   │   └── websockets/      # WebSocket handlers
│   │   ├── v2/                  # API Version 2 (future)
│   │   ├── middleware/          # Custom middleware
│   │   └── dependencies.py      # Dependency injection
│   │
│   ├── application/             # Application Layer
│   │   ├── commands/           # Command handlers (CQRS)
│   │   ├── queries/            # Query handlers (CQRS)
│   │   ├── services/           # Application services
│   │   └── dto/                # Data transfer objects
│   │
│   ├── domain/                  # Domain Layer (DDD)
│   │   ├── aggregates/         # Aggregate roots
│   │   ├── entities/           # Domain entities
│   │   ├── value_objects/      # Value objects
│   │   ├── events/             # Domain events
│   │   ├── services/           # Domain services
│   │   └── specifications/     # Business rules
│   │
│   ├── infrastructure/          # Infrastructure Layer
│   │   ├── persistence/        # Database implementation
│   │   │   ├── repositories/   # Repository implementations
│   │   │   ├── models/         # SQLAlchemy models
│   │   │   └── migrations/     # Alembic migrations
│   │   ├── cache/              # Caching implementation
│   │   ├── messaging/          # Message queue integration
│   │   ├── monitoring/         # Metrics and tracing
│   │   └── security/           # Security implementations
│   │
│   ├── core/                    # Core utilities
│   │   ├── config.py           # Configuration management
│   │   ├── exceptions.py       # Custom exceptions
│   │   ├── decorators.py       # Custom decorators
│   │   └── types.py            # Type definitions
│   │
│   └── workers/                 # Background workers
│       ├── celery/             # Celery tasks
│       ├── consumers/          # Message consumers
│       └── schedulers/         # Cron jobs
│
├── tests/                       # Test suites
│   ├── unit/                   # Unit tests
│   ├── integration/            # Integration tests
│   ├── e2e/                    # End-to-end tests
│   ├── performance/            # Load tests
│   └── security/               # Security tests
│
├── scripts/                     # Utility scripts
├── docker/                      # Docker configurations
└── pyproject.toml              # Project dependencies
```

---

## Service Architecture

### Microservices Design

```python
# Service orchestration with advanced patterns
from typing import Protocol, runtime_checkable
from abc import ABC, abstractmethod
import asyncio
from dataclasses import dataclass
from enum import Enum

class ServiceStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"

@runtime_checkable
class HealthCheckable(Protocol):
    """Protocol for services that support health checks"""
    async def health_check(self) -> ServiceStatus: ...
    async def readiness_check(self) -> bool: ...

@dataclass
class ServiceContext:
    """Shared context for all services"""
    request_id: str
    tenant_id: str
    user_id: str
    correlation_id: str
    span_id: str
    trace_id: str
    
class BaseService(ABC, HealthCheckable):
    """Base class for all microservices"""
    
    def __init__(self, name: str, config: dict):
        self.name = name
        self.config = config
        self._circuit_breaker = CircuitBreaker(
            failure_threshold=5,
            recovery_timeout=60,
            expected_exception=ServiceException
        )
        self._rate_limiter = RateLimiter(
            rate=config.get('rate_limit', 100),
            period=60
        )
        self._metrics = MetricsCollector(service_name=name)
        
    async def execute(self, context: ServiceContext, *args, **kwargs):
        """Execute service with circuit breaker and rate limiting"""
        async with self._metrics.track_execution():
            if not await self._rate_limiter.acquire():
                raise RateLimitExceeded(f"Rate limit exceeded for {self.name}")
                
            return await self._circuit_breaker.call(
                self._execute_impl,
                context,
                *args,
                **kwargs
            )
    
    @abstractmethod
    async def _execute_impl(self, context: ServiceContext, *args, **kwargs):
        """Service implementation to be overridden"""
        pass
    
    async def health_check(self) -> ServiceStatus:
        """Check service health"""
        try:
            # Check dependencies
            deps_healthy = await self._check_dependencies()
            
            # Check circuit breaker state
            if self._circuit_breaker.state == CircuitBreakerState.OPEN:
                return ServiceStatus.UNHEALTHY
            
            # Check rate limiter
            if self._rate_limiter.is_throttled():
                return ServiceStatus.DEGRADED
                
            return ServiceStatus.HEALTHY if deps_healthy else ServiceStatus.DEGRADED
            
        except Exception as e:
            logger.error(f"Health check failed for {self.name}: {e}")
            return ServiceStatus.UNHEALTHY
    
    async def readiness_check(self) -> bool:
        """Check if service is ready to handle requests"""
        return await self.health_check() != ServiceStatus.UNHEALTHY
    
    @abstractmethod
    async def _check_dependencies(self) -> bool:
        """Check if all dependencies are available"""
        pass
```

### Service Registry and Discovery

```python
# Service registry with automatic discovery
from typing import Dict, List, Optional
import consul
import asyncio

class ServiceRegistry:
    """Service registry with Consul integration"""
    
    def __init__(self, consul_host: str = "localhost", consul_port: int = 8500):
        self.consul = consul.aio.Consul(host=consul_host, port=consul_port)
        self._services: Dict[str, ServiceInfo] = {}
        self._health_check_task = None
        
    async def register(self, service: ServiceInfo) -> None:
        """Register a service with health check"""
        await self.consul.agent.service.register(
            name=service.name,
            service_id=service.id,
            address=service.address,
            port=service.port,
            tags=service.tags,
            check=consul.Check.http(
                f"http://{service.address}:{service.port}/health",
                interval="10s",
                timeout="5s"
            )
        )
        self._services[service.id] = service
        
    async def deregister(self, service_id: str) -> None:
        """Deregister a service"""
        await self.consul.agent.service.deregister(service_id)
        self._services.pop(service_id, None)
        
    async def discover(self, service_name: str, tags: List[str] = None) -> List[ServiceInfo]:
        """Discover available service instances"""
        _, services = await self.consul.health.service(
            service_name,
            passing=True,
            tags=tags
        )
        
        return [
            ServiceInfo(
                id=s['Service']['ID'],
                name=s['Service']['Service'],
                address=s['Service']['Address'],
                port=s['Service']['Port'],
                tags=s['Service']['Tags']
            )
            for s in services
        ]
    
    async def get_service(self, service_name: str, strategy: LoadBalancingStrategy = LoadBalancingStrategy.ROUND_ROBIN) -> Optional[ServiceInfo]:
        """Get a service instance with load balancing"""
        services = await self.discover(service_name)
        if not services:
            return None
            
        if strategy == LoadBalancingStrategy.ROUND_ROBIN:
            return self._round_robin_select(services)
        elif strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
            return self._least_connections_select(services)
        elif strategy == LoadBalancingStrategy.RANDOM:
            return random.choice(services)
            
    async def start_health_monitoring(self):
        """Start background health monitoring"""
        self._health_check_task = asyncio.create_task(self._monitor_health())
        
    async def _monitor_health(self):
        """Monitor service health in background"""
        while True:
            try:
                for service_id, service in self._services.items():
                    health = await self._check_service_health(service)
                    if not health:
                        logger.warning(f"Service {service.name} ({service_id}) is unhealthy")
                        # Emit health event
                        await self._emit_health_event(service, healthy=False)
                        
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Health monitoring error: {e}")
                await asyncio.sleep(60)
```

---

## Domain-Driven Design

### Aggregate Root Pattern

```python
# Domain aggregates with event sourcing
from typing import List, Optional, Any
from datetime import datetime
from uuid import UUID, uuid4
from dataclasses import dataclass, field

@dataclass
class DomainEvent:
    """Base class for domain events"""
    aggregate_id: UUID
    event_type: str
    occurred_at: datetime = field(default_factory=datetime.utcnow)
    version: int = 1
    metadata: dict = field(default_factory=dict)

class AggregateRoot(ABC):
    """Base aggregate root with event sourcing"""
    
    def __init__(self, aggregate_id: Optional[UUID] = None):
        self.id = aggregate_id or uuid4()
        self._version = 0
        self._events: List[DomainEvent] = []
        self._uncommitted_events: List[DomainEvent] = []
        
    def apply_event(self, event: DomainEvent, is_new: bool = True) -> None:
        """Apply domain event to aggregate"""
        # Call event handler
        handler = getattr(self, f"_handle_{event.__class__.__name__}", None)
        if handler:
            handler(event)
        
        # Track event
        if is_new:
            self._uncommitted_events.append(event)
        self._events.append(event)
        self._version += 1
        
    def get_uncommitted_events(self) -> List[DomainEvent]:
        """Get events that haven't been persisted"""
        return self._uncommitted_events.copy()
        
    def mark_events_committed(self) -> None:
        """Mark all events as committed"""
        self._uncommitted_events.clear()
        
    def load_from_events(self, events: List[DomainEvent]) -> None:
        """Rebuild aggregate from event history"""
        for event in events:
            self.apply_event(event, is_new=False)

# Example: Evaluation aggregate
class EvaluationAggregate(AggregateRoot):
    """Evaluation aggregate root"""
    
    def __init__(self, aggregate_id: Optional[UUID] = None):
        super().__init__(aggregate_id)
        self.assessment_id: Optional[UUID] = None
        self.project_id: Optional[UUID] = None
        self.tenant_id: Optional[UUID] = None
        self.status: EvaluationStatus = EvaluationStatus.CREATED
        self.models: List[ModelConfiguration] = []
        self.results: Dict[str, ProbeResult] = {}
        self.created_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        
    @classmethod
    def create(cls, 
               assessment_id: UUID,
               project_id: UUID,
               tenant_id: UUID,
               models: List[ModelConfiguration]) -> 'EvaluationAggregate':
        """Factory method to create new evaluation"""
        evaluation = cls()
        
        # Apply creation event
        event = EvaluationCreatedEvent(
            aggregate_id=evaluation.id,
            event_type="EvaluationCreated",
            assessment_id=assessment_id,
            project_id=project_id,
            tenant_id=tenant_id,
            models=models
        )
        evaluation.apply_event(event)
        
        return evaluation
        
    def start_evaluation(self) -> None:
        """Start the evaluation process"""
        if self.status != EvaluationStatus.CREATED:
            raise InvalidStateTransition(
                f"Cannot start evaluation in status {self.status}"
            )
            
        event = EvaluationStartedEvent(
            aggregate_id=self.id,
            event_type="EvaluationStarted"
        )
        self.apply_event(event)
        
    def record_probe_result(self, model_id: UUID, probe_id: UUID, result: ProbeResult) -> None:
        """Record a probe execution result"""
        if self.status != EvaluationStatus.RUNNING:
            raise InvalidStateTransition(
                f"Cannot record results for evaluation in status {self.status}"
            )
            
        event = ProbeResultRecordedEvent(
            aggregate_id=self.id,
            event_type="ProbeResultRecorded",
            model_id=model_id,
            probe_id=probe_id,
            result=result
        )
        self.apply_event(event)
        
    def complete_evaluation(self, final_scores: Dict[str, float]) -> None:
        """Complete the evaluation with final scores"""
        if self.status != EvaluationStatus.RUNNING:
            raise InvalidStateTransition(
                f"Cannot complete evaluation in status {self.status}"
            )
            
        event = EvaluationCompletedEvent(
            aggregate_id=self.id,
            event_type="EvaluationCompleted",
            final_scores=final_scores
        )
        self.apply_event(event)
        
    # Event handlers
    def _handle_EvaluationCreatedEvent(self, event: EvaluationCreatedEvent) -> None:
        """Handle evaluation created event"""
        self.assessment_id = event.assessment_id
        self.project_id = event.project_id
        self.tenant_id = event.tenant_id
        self.models = event.models
        self.created_at = event.occurred_at
        self.status = EvaluationStatus.CREATED
        
    def _handle_EvaluationStartedEvent(self, event: EvaluationStartedEvent) -> None:
        """Handle evaluation started event"""
        self.status = EvaluationStatus.RUNNING
        
    def _handle_ProbeResultRecordedEvent(self, event: ProbeResultRecordedEvent) -> None:
        """Handle probe result recorded event"""
        key = f"{event.model_id}:{event.probe_id}"
        self.results[key] = event.result
        
    def _handle_EvaluationCompletedEvent(self, event: EvaluationCompletedEvent) -> None:
        """Handle evaluation completed event"""
        self.status = EvaluationStatus.COMPLETED
        self.completed_at = event.occurred_at
```

### Value Objects

```python
# Value objects for domain modeling
from dataclasses import dataclass, field
from typing import Optional, List
import re

@dataclass(frozen=True)
class Email:
    """Email value object with validation"""
    value: str
    
    def __post_init__(self):
        if not self._is_valid_email(self.value):
            raise ValueError(f"Invalid email: {self.value}")
    
    @staticmethod
    def _is_valid_email(email: str) -> bool:
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def domain(self) -> str:
        """Get email domain"""
        return self.value.split('@')[1]

@dataclass(frozen=True)
class Score:
    """Score value object with constraints"""
    value: float
    min_value: float = 0.0
    max_value: float = 1.0
    
    def __post_init__(self):
        if not self.min_value <= self.value <= self.max_value:
            raise ValueError(
                f"Score {self.value} out of range [{self.min_value}, {self.max_value}]"
            )
    
    def percentage(self) -> float:
        """Convert to percentage"""
        return (self.value - self.min_value) / (self.max_value - self.min_value) * 100
    
    def grade(self) -> str:
        """Convert to letter grade"""
        pct = self.percentage()
        if pct >= 90: return 'A'
        elif pct >= 80: return 'B'
        elif pct >= 70: return 'C'
        elif pct >= 60: return 'D'
        else: return 'F'

@dataclass(frozen=True)
class ModelConfiguration:
    """Model configuration value object"""
    model_id: UUID
    provider: str
    parameters: dict = field(default_factory=dict)
    rate_limit: Optional[int] = None
    
    def __post_init__(self):
        # Validate required parameters
        required = {'temperature', 'max_tokens'}
        if not required.issubset(self.parameters.keys()):
            raise ValueError(f"Missing required parameters: {required - self.parameters.keys()}")
            
        # Validate parameter ranges
        if not 0 <= self.parameters['temperature'] <= 2:
            raise ValueError("Temperature must be between 0 and 2")
            
        if not 1 <= self.parameters['max_tokens'] <= 100000:
            raise ValueError("Max tokens must be between 1 and 100000")
```

### Domain Services

```python
# Domain services for complex business logic
class ScoringDomainService:
    """Domain service for score calculation"""
    
    def calculate_probeset_score(self,
                                  probe_results: List[ProbeResult],
                                  scoring_method: ScoringMethod) -> Score:
        """Calculate aggregate score for a probeset"""
        if not probe_results:
            raise ValueError("No probe results to score")
            
        if scoring_method == ScoringMethod.AVERAGE:
            return self._calculate_average_score(probe_results)
        elif scoring_method == ScoringMethod.WEIGHTED:
            return self._calculate_weighted_score(probe_results)
        elif scoring_method == ScoringMethod.MIN:
            return Score(min(r.score for r in probe_results))
        elif scoring_method == ScoringMethod.MAX:
            return Score(max(r.score for r in probe_results))
        elif scoring_method == ScoringMethod.CUSTOM:
            return self._calculate_custom_score(probe_results)
        else:
            raise ValueError(f"Unknown scoring method: {scoring_method}")
    
    def _calculate_average_score(self, results: List[ProbeResult]) -> Score:
        """Calculate simple average"""
        total = sum(r.score for r in results)
        return Score(total / len(results))
    
    def _calculate_weighted_score(self, results: List[ProbeResult]) -> Score:
        """Calculate weighted average"""
        total_weight = sum(r.weight for r in results)
        if total_weight == 0:
            return self._calculate_average_score(results)
            
        weighted_sum = sum(r.score * r.weight for r in results)
        return Score(weighted_sum / total_weight)
    
    def _calculate_custom_score(self, results: List[ProbeResult]) -> Score:
        """Apply custom scoring logic"""
        # Example: Penalize any failure heavily
        for result in results:
            if result.score < 0.5:
                return Score(result.score * 0.5)  # Heavy penalty
                
        return self._calculate_average_score(results)

class PermissionDomainService:
    """Domain service for permission evaluation"""
    
    def can_access_evaluation(self,
                               user: User,
                               evaluation: EvaluationAggregate) -> bool:
        """Check if user can access evaluation"""
        # Platform admin can access everything
        if user.has_role(Role.PLATFORM_ADMIN):
            return True
            
        # Check tenant membership
        if evaluation.tenant_id not in user.tenant_ids:
            return False
            
        # Check project access
        if user.has_permission(Permission.PROJECT_READ, evaluation.project_id):
            return True
            
        # Check if user created the evaluation
        if evaluation.created_by == user.id:
            return True
            
        return False
    
    def can_modify_evaluation(self,
                              user: User,
                              evaluation: EvaluationAggregate) -> bool:
        """Check if user can modify evaluation"""
        # Cannot modify completed evaluations
        if evaluation.status == EvaluationStatus.COMPLETED:
            return False
            
        # Platform admin can modify anything
        if user.has_role(Role.PLATFORM_ADMIN):
            return True
            
        # Tenant admin can modify tenant evaluations
        if user.has_role(Role.TENANT_ADMIN, evaluation.tenant_id):
            return True
            
        # Project admin can modify project evaluations
        if user.has_permission(Permission.PROJECT_ADMIN, evaluation.project_id):
            return True
            
        return False
```

---

## Advanced Python Patterns

### Decorators and Metaclasses

```python
# Advanced decorator patterns
from functools import wraps
from typing import Callable, Any, TypeVar, cast
import time
import asyncio
from contextvars import ContextVar

# Context variables for request tracking
request_context: ContextVar[RequestContext] = ContextVar('request_context')

F = TypeVar('F', bound=Callable[..., Any])

def traced(span_name: Optional[str] = None) -> Callable[[F], F]:
    """Decorator for distributed tracing"""
    def decorator(func: F) -> F:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            ctx = request_context.get()
            span = ctx.tracer.start_span(
                span_name or func.__name__,
                child_of=ctx.current_span
            )
            
            try:
                with span:
                    span.set_tag('function', func.__name__)
                    span.set_tag('tenant_id', ctx.tenant_id)
                    result = await func(*args, **kwargs)
                    return result
            except Exception as e:
                span.set_tag('error', True)
                span.log_kv({'exception': str(e)})
                raise
            finally:
                span.finish()
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            ctx = request_context.get()
            span = ctx.tracer.start_span(
                span_name or func.__name__,
                child_of=ctx.current_span
            )
            
            try:
                with span:
                    span.set_tag('function', func.__name__)
                    result = func(*args, **kwargs)
                    return result
            except Exception as e:
                span.set_tag('error', True)
                span.log_kv({'exception': str(e)})
                raise
            finally:
                span.finish()
        
        if asyncio.iscoroutinefunction(func):
            return cast(F, async_wrapper)
        else:
            return cast(F, sync_wrapper)
    
    return decorator

def retry(max_attempts: int = 3,
          backoff_factor: float = 2.0,
          exceptions: tuple = (Exception,)) -> Callable[[F], F]:
    """Decorator for retry logic with exponential backoff"""
    def decorator(func: F) -> F:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            attempt = 0
            delay = 1.0
            
            while attempt < max_attempts:
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    attempt += 1
                    if attempt >= max_attempts:
                        logger.error(f"Max retries ({max_attempts}) exceeded for {func.__name__}")
                        raise
                    
                    logger.warning(f"Attempt {attempt} failed for {func.__name__}: {e}")
                    await asyncio.sleep(delay)
                    delay *= backoff_factor
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            attempt = 0
            delay = 1.0
            
            while attempt < max_attempts:
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    attempt += 1
                    if attempt >= max_attempts:
                        logger.error(f"Max retries ({max_attempts}) exceeded for {func.__name__}")
                        raise
                    
                    logger.warning(f"Attempt {attempt} failed for {func.__name__}: {e}")
                    time.sleep(delay)
                    delay *= backoff_factor
        
        if asyncio.iscoroutinefunction(func):
            return cast(F, async_wrapper)
        else:
            return cast(F, sync_wrapper)
    
    return decorator

def cached(ttl: int = 3600, key_prefix: Optional[str] = None) -> Callable[[F], F]:
    """Decorator for Redis caching with TTL"""
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = generate_cache_key(
                key_prefix or func.__name__,
                *args,
                **kwargs
            )
            
            # Try to get from cache
            cache_manager = get_cache_manager()
            cached_value = await cache_manager.get(cache_key)
            if cached_value is not None:
                return cached_value
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await cache_manager.set(cache_key, result, ttl)
            
            return result
        
        return cast(F, wrapper)
    
    return decorator

# Metaclass for singleton pattern
class SingletonMeta(type):
    """Metaclass for singleton pattern"""
    _instances = {}
    _lock = threading.Lock()
    
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
        return cls._instances[cls]

# Metaclass for automatic property validation
class ValidatedMeta(type):
    """Metaclass that adds validation to properties"""
    def __new__(mcs, name, bases, namespace):
        # Find all properties with validators
        for key, value in namespace.items():
            if isinstance(value, ValidatedProperty):
                # Create property with validation
                namespace[key] = mcs._create_validated_property(key, value)
        
        return super().__new__(mcs, name, bases, namespace)
    
    @staticmethod
    def _create_validated_property(name: str, prop: ValidatedProperty):
        """Create a property with validation"""
        private_name = f'_{name}'
        
        def getter(self):
            return getattr(self, private_name, prop.default)
        
        def setter(self, value):
            if prop.validator and not prop.validator(value):
                raise ValueError(f"Invalid value for {name}: {value}")
            setattr(self, private_name, value)
        
        return property(getter, setter)

# Descriptors for advanced property handling
class LazyProperty:
    """Descriptor for lazy property evaluation"""
    def __init__(self, func: Callable):
        self.func = func
        self.__doc__ = func.__doc__
    
    def __get__(self, obj, type=None):
        if obj is None:
            return self
        
        value = self.func(obj)
        setattr(obj, self.func.__name__, value)
        return value

class CachedProperty:
    """Descriptor for cached property with TTL"""
    def __init__(self, ttl: int = 60):
        self.ttl = ttl
        self._cache = {}
        self._timestamps = {}
    
    def __set_name__(self, owner, name):
        self.name = name
    
    def __get__(self, obj, type=None):
        if obj is None:
            return self
        
        now = time.time()
        cache_key = id(obj)
        
        # Check if cached value exists and is valid
        if cache_key in self._cache:
            if now - self._timestamps[cache_key] < self.ttl:
                return self._cache[cache_key]
        
        # Compute new value
        value = getattr(obj, f'_compute_{self.name}')()
        self._cache[cache_key] = value
        self._timestamps[cache_key] = now
        
        return value
```

### Async/Await Patterns

```python
# Advanced async patterns
import asyncio
from typing import List, Callable, TypeVar, Generic
from asyncio import Queue, Semaphore
import aiohttp
from contextlib import asynccontextmanager

T = TypeVar('T')

class AsyncBatcher(Generic[T]):
    """Batch async operations for efficiency"""
    
    def __init__(self, 
                 batch_size: int = 100,
                 batch_timeout: float = 1.0,
                 process_func: Callable[[List[T]], Awaitable[List[Any]]]):
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.process_func = process_func
        self._queue: Queue[T] = Queue()
        self._processing_task = None
        
    async def start(self):
        """Start the batch processor"""
        self._processing_task = asyncio.create_task(self._process_batches())
        
    async def stop(self):
        """Stop the batch processor"""
        if self._processing_task:
            self._processing_task.cancel()
            await asyncio.gather(self._processing_task, return_exceptions=True)
    
    async def add(self, item: T) -> None:
        """Add item to batch queue"""
        await self._queue.put(item)
    
    async def _process_batches(self):
        """Process batches continuously"""
        while True:
            batch = []
            deadline = asyncio.create_task(asyncio.sleep(self.batch_timeout))
            
            try:
                while len(batch) < self.batch_size:
                    get_task = asyncio.create_task(self._queue.get())
                    done, pending = await asyncio.wait(
                        {get_task, deadline},
                        return_when=asyncio.FIRST_COMPLETED
                    )
                    
                    if get_task in done:
                        batch.append(get_task.result())
                    else:
                        get_task.cancel()
                        break
                
                if batch:
                    await self.process_func(batch)
                    
            except asyncio.CancelledError:
                raise
            except Exception as e:
                logger.error(f"Batch processing error: {e}")

class AsyncConnectionPool:
    """Async connection pool with health checks"""
    
    def __init__(self, 
                 create_connection: Callable,
                 max_connections: int = 10,
                 min_connections: int = 2):
        self._create_connection = create_connection
        self._max_connections = max_connections
        self._min_connections = min_connections
        self._connections: Queue = Queue(maxsize=max_connections)
        self._semaphore = Semaphore(max_connections)
        self._health_check_task = None
        
    async def initialize(self):
        """Initialize the connection pool"""
        # Create minimum connections
        for _ in range(self._min_connections):
            conn = await self._create_connection()
            await self._connections.put(conn)
        
        # Start health monitoring
        self._health_check_task = asyncio.create_task(self._monitor_health())
    
    @asynccontextmanager
    async def acquire(self):
        """Acquire a connection from the pool"""
        async with self._semaphore:
            try:
                # Try to get existing connection
                conn = self._connections.get_nowait()
            except asyncio.QueueEmpty:
                # Create new connection if pool not at max
                conn = await self._create_connection()
            
            try:
                # Test connection health
                if not await self._is_healthy(conn):
                    await self._close_connection(conn)
                    conn = await self._create_connection()
                
                yield conn
                
            finally:
                # Return connection to pool
                if self._connections.full():
                    await self._close_connection(conn)
                else:
                    await self._connections.put(conn)
    
    async def _monitor_health(self):
        """Monitor connection health in background"""
        while True:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                
                # Check all idle connections
                connections_to_check = []
                while not self._connections.empty():
                    try:
                        conn = self._connections.get_nowait()
                        connections_to_check.append(conn)
                    except asyncio.QueueEmpty:
                        break
                
                # Test and return healthy connections
                for conn in connections_to_check:
                    if await self._is_healthy(conn):
                        await self._connections.put(conn)
                    else:
                        await self._close_connection(conn)
                        # Create replacement
                        new_conn = await self._create_connection()
                        await self._connections.put(new_conn)
                        
            except Exception as e:
                logger.error(f"Health check error: {e}")

# Async context manager for distributed locking
class AsyncDistributedLock:
    """Distributed lock using Redis"""
    
    def __init__(self, 
                 redis_client,
                 key: str,
                 timeout: int = 10,
                 retry_interval: float = 0.1):
        self.redis = redis_client
        self.key = f"lock:{key}"
        self.timeout = timeout
        self.retry_interval = retry_interval
        self.token = None
        
    async def __aenter__(self):
        """Acquire the lock"""
        self.token = str(uuid4())
        
        while True:
            # Try to acquire lock
            acquired = await self.redis.set(
                self.key,
                self.token,
                nx=True,
                ex=self.timeout
            )
            
            if acquired:
                return self
                
            await asyncio.sleep(self.retry_interval)
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Release the lock"""
        # Use Lua script for atomic check-and-delete
        lua_script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
        """
        
        await self.redis.eval(lua_script, 1, self.key, self.token)
```

---

## Modern API Design

### Contract-First API Design with OpenAPI 3.1

```python
# OpenAPI schema generation with advanced features
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator
from fastapi import FastAPI, APIRouter, Query, Path, Body, Header
from fastapi.openapi.utils import get_openapi
from enum import Enum

class APIVersion(str, Enum):
    V1 = "v1"
    V2 = "v2"
    
class BaseResponse(BaseModel):
    """Base response model with standard fields"""
    success: bool = Field(..., description="Indicates if the request was successful")
    message: Optional[str] = Field(None, description="Human-readable message")
    request_id: str = Field(..., description="Unique request identifier for tracing")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
class PaginatedResponse(BaseResponse):
    """Paginated response with metadata"""
    data: List[Any] = Field(..., description="List of items")
    pagination: PaginationMeta = Field(..., description="Pagination metadata")
    
class PaginationMeta(BaseModel):
    """Pagination metadata"""
    page: int = Field(..., ge=1, description="Current page number")
    per_page: int = Field(..., ge=1, le=1000, description="Items per page")
    total: int = Field(..., ge=0, description="Total number of items")
    total_pages: int = Field(..., ge=0, description="Total number of pages")
    has_next: bool = Field(..., description="Has next page")
    has_prev: bool = Field(..., description="Has previous page")

# Advanced API router with versioning
class VersionedAPIRouter(APIRouter):
    """API router with version management"""
    
    def __init__(self, version: APIVersion, *args, **kwargs):
        self.version = version
        super().__init__(*args, **kwargs)
        
    def add_api_route(self, path: str, endpoint, **kwargs):
        # Add version to path
        versioned_path = f"/api/{self.version.value}{path}"
        
        # Add common responses
        kwargs.setdefault('responses', {}).update({
            400: {"description": "Bad Request", "model": ErrorResponse},
            401: {"description": "Unauthorized", "model": ErrorResponse},
            403: {"description": "Forbidden", "model": ErrorResponse},
            404: {"description": "Not Found", "model": ErrorResponse},
            429: {"description": "Too Many Requests", "model": ErrorResponse},
            500: {"description": "Internal Server Error", "model": ErrorResponse},
        })
        
        super().add_api_route(versioned_path, endpoint, **kwargs)

# Example API implementation with advanced features
router = VersionedAPIRouter(
    version=APIVersion.V1,
    tags=["evaluations"],
    responses={404: {"description": "Not found"}}
)

@router.post(
    "/evaluations",
    response_model=EvaluationResponse,
    status_code=201,
    summary="Create a new evaluation",
    description="Create a new evaluation run with specified models and assessment",
    response_description="The created evaluation",
    deprecated=False,
    operation_id="createEvaluation",
    tags=["evaluations", "create"]
)
async def create_evaluation(
    evaluation: EvaluationCreateRequest = Body(
        ...,
        example={
            "assessment_id": "550e8400-e29b-41d4-a716-446655440000",
            "project_id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
            "models": [
                {
                    "model_id": "gpt-4",
                    "provider": "openai",
                    "parameters": {
                        "temperature": 0.7,
                        "max_tokens": 2000
                    }
                }
            ]
        }
    ),
    x_request_id: str = Header(None, description="Request ID for tracing"),
    x_tenant_id: str = Header(..., description="Tenant identifier"),
    authorization: str = Header(..., description="Bearer token"),
    user: User = Depends(get_current_user),
    service: EvaluationService = Depends(get_evaluation_service)
) -> EvaluationResponse:
    """
    Create a new evaluation run.
    
    This endpoint creates a new evaluation run with the specified assessment
    and model configurations. The evaluation will be queued for processing
    and will run asynchronously.
    
    **Required permissions:**
    - `evaluation:create` in the specified project
    
    **Rate limits:**
    - 100 requests per minute per tenant
    - 10 concurrent evaluations per tenant
    """
    # Implementation
    pass

# GraphQL integration alongside REST
from strawberry import Schema, type, field
from strawberry.fastapi import GraphQLRouter

@type
class EvaluationType:
    """GraphQL type for evaluation"""
    id: str
    assessment_id: str
    project_id: str
    status: str
    created_at: datetime
    
    @field
    async def results(self) -> List['ResultType']:
        """Resolve evaluation results"""
        return await get_evaluation_results(self.id)
    
    @field
    async def models(self) -> List['ModelType']:
        """Resolve evaluation models"""
        return await get_evaluation_models(self.id)

@type
class Query:
    @field
    async def evaluation(self, id: str) -> Optional[EvaluationType]:
        """Get evaluation by ID"""
        return await get_evaluation(id)
    
    @field
    async def evaluations(
        self,
        project_id: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[EvaluationType]:
        """List evaluations with filters"""
        return await list_evaluations(project_id, status, limit, offset)

@type
class Mutation:
    @field
    async def create_evaluation(
        self,
        assessment_id: str,
        project_id: str,
        models: List[str]
    ) -> EvaluationType:
        """Create new evaluation"""
        return await create_evaluation_mutation(assessment_id, project_id, models)

# Create GraphQL schema
graphql_schema = Schema(query=Query, mutation=Mutation)
graphql_app = GraphQLRouter(graphql_schema)

# WebSocket support for real-time features
from fastapi import WebSocket, WebSocketDisconnect
from typing import Dict, Set

class ConnectionManager:
    """WebSocket connection manager"""
    
    def __init__(self):
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        
    async def connect(self, websocket: WebSocket, tenant_id: str):
        """Accept and track WebSocket connection"""
        await websocket.accept()
        if tenant_id not in self.active_connections:
            self.active_connections[tenant_id] = set()
        self.active_connections[tenant_id].add(websocket)
        
    def disconnect(self, websocket: WebSocket, tenant_id: str):
        """Remove WebSocket connection"""
        if tenant_id in self.active_connections:
            self.active_connections[tenant_id].discard(websocket)
            if not self.active_connections[tenant_id]:
                del self.active_connections[tenant_id]
    
    async def send_to_tenant(self, tenant_id: str, message: dict):
        """Send message to all connections for a tenant"""
        if tenant_id in self.active_connections:
            for connection in self.active_connections[tenant_id]:
                try:
                    await connection.send_json(message)
                except:
                    # Connection closed, will be cleaned up
                    pass
    
    async def broadcast(self, message: dict):
        """Broadcast message to all connections"""
        for tenant_connections in self.active_connections.values():
            for connection in tenant_connections:
                try:
                    await connection.send_json(message)
                except:
                    pass

manager = ConnectionManager()

@router.websocket("/ws/{tenant_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    tenant_id: str,
    token: str = Query(...)
):
    """WebSocket endpoint for real-time updates"""
    # Validate token
    user = await validate_websocket_token(token)
    if not user or tenant_id not in user.tenant_ids:
        await websocket.close(code=1008, reason="Unauthorized")
        return
    
    await manager.connect(websocket, tenant_id)
    
    try:
        while True:
            # Receive and process messages
            data = await websocket.receive_json()
            
            if data["type"] == "subscribe":
                # Subscribe to evaluation updates
                evaluation_id = data["evaluation_id"]
                await subscribe_to_evaluation(websocket, evaluation_id)
                
            elif data["type"] == "unsubscribe":
                # Unsubscribe from evaluation updates
                evaluation_id = data["evaluation_id"]
                await unsubscribe_from_evaluation(websocket, evaluation_id)
                
    except WebSocketDisconnect:
        manager.disconnect(websocket, tenant_id)
```

### API Versioning and Migration

```python
# API versioning with backward compatibility
from abc import ABC, abstractmethod
from typing import Dict, Any

class APIVersionAdapter(ABC):
    """Base class for API version adapters"""
    
    @abstractmethod
    def adapt_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt request from older version to current"""
        pass
    
    @abstractmethod
    def adapt_response(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt response from current to older version"""
        pass

class V1ToV2Adapter(APIVersionAdapter):
    """Adapter from API v1 to v2"""
    
    def adapt_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert v1 request to v2 format"""
        adapted = data.copy()
        
        # Handle renamed fields
        if 'model_name' in adapted:
            adapted['model_id'] = adapted.pop('model_name')
        
        # Handle structure changes
        if 'parameters' in adapted and isinstance(adapted['parameters'], list):
            # Convert list format to dict format
            adapted['parameters'] = {
                param['key']: param['value']
                for param in adapted['parameters']
            }
        
        return adapted
    
    def adapt_response(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert v2 response to v1 format"""
        adapted = data.copy()
        
        # Handle renamed fields
        if 'model_id' in adapted:
            adapted['model_name'] = adapted.pop('model_id')
        
        # Handle removed fields
        adapted.pop('new_v2_field', None)
        
        return adapted

class APIVersionManager:
    """Manage API versions and adaptations"""
    
    def __init__(self):
        self.adapters: Dict[tuple, APIVersionAdapter] = {
            ('v1', 'v2'): V1ToV2Adapter(),
        }
    
    def adapt_request(self, 
                       from_version: str,
                       to_version: str,
                       data: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt request between versions"""
        if from_version == to_version:
            return data
        
        adapter_key = (from_version, to_version)
        if adapter_key not in self.adapters:
            raise ValueError(f"No adapter from {from_version} to {to_version}")
        
        return self.adapters[adapter_key].adapt_request(data)
    
    def adapt_response(self,
                        from_version: str,
                        to_version: str,
                        data: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt response between versions"""
        if from_version == to_version:
            return data
        
        adapter_key = (to_version, from_version)
        if adapter_key not in self.adapters:
            raise ValueError(f"No adapter from {from_version} to {to_version}")
        
        return self.adapters[adapter_key].adapt_response(data)
```

---

## Repository Pattern Implementation

### Advanced Repository with Unit of Work

```python
# Unit of Work pattern for transaction management
from typing import TypeVar, Generic, Optional, List, Dict, Any, Type
from sqlalchemy.orm import Session, Query
from sqlalchemy.ext.declarative import DeclarativeMeta
from contextlib import contextmanager
import threading

T = TypeVar('T', bound=DeclarativeMeta)

class Specification(ABC):
    """Specification pattern for complex queries"""
    
    @abstractmethod
    def is_satisfied_by(self, candidate: Any) -> bool:
        pass
    
    @abstractmethod
    def to_sql_clause(self, model: Type[T]) -> Any:
        """Convert to SQLAlchemy filter clause"""
        pass
    
    def and_(self, other: 'Specification') -> 'CompositeSpecification':
        return AndSpecification(self, other)
    
    def or_(self, other: 'Specification') -> 'CompositeSpecification':
        return OrSpecification(self, other)
    
    def not_(self) -> 'NotSpecification':
        return NotSpecification(self)

class CompositeSpecification(Specification):
    """Composite specification for combining specifications"""
    
    def __init__(self, left: Specification, right: Specification):
        self.left = left
        self.right = right

class AndSpecification(CompositeSpecification):
    def is_satisfied_by(self, candidate: Any) -> bool:
        return self.left.is_satisfied_by(candidate) and self.right.is_satisfied_by(candidate)
    
    def to_sql_clause(self, model: Type[T]) -> Any:
        from sqlalchemy import and_
        return and_(
            self.left.to_sql_clause(model),
            self.right.to_sql_clause(model)
        )

class Repository(Generic[T], ABC):
    """Advanced repository with specification pattern"""
    
    def __init__(self, session: Session, model: Type[T]):
        self._session = session
        self._model = model
        
    def add(self, entity: T) -> T:
        """Add entity to repository"""
        self._session.add(entity)
        return entity
    
    def add_many(self, entities: List[T]) -> List[T]:
        """Add multiple entities"""
        self._session.add_all(entities)
        return entities
    
    def get(self, id: Any) -> Optional[T]:
        """Get entity by ID"""
        return self._session.query(self._model).filter(
            self._model.id == id
        ).first()
    
    def get_many(self, ids: List[Any]) -> List[T]:
        """Get multiple entities by IDs"""
        return self._session.query(self._model).filter(
            self._model.id.in_(ids)
        ).all()
    
    def find(self, specification: Specification) -> List[T]:
        """Find entities matching specification"""
        query = self._session.query(self._model)
        if specification:
            query = query.filter(specification.to_sql_clause(self._model))
        return query.all()
    
    def find_one(self, specification: Specification) -> Optional[T]:
        """Find single entity matching specification"""
        query = self._session.query(self._model)
        if specification:
            query = query.filter(specification.to_sql_clause(self._model))
        return query.first()
    
    def exists(self, specification: Specification) -> bool:
        """Check if any entity matches specification"""
        query = self._session.query(self._model)
        if specification:
            query = query.filter(specification.to_sql_clause(self._model))
        return query.count() > 0
    
    def count(self, specification: Optional[Specification] = None) -> int:
        """Count entities matching specification"""
        query = self._session.query(self._model)
        if specification:
            query = query.filter(specification.to_sql_clause(self._model))
        return query.count()
    
    def update(self, entity: T) -> T:
        """Update entity"""
        self._session.merge(entity)
        return entity
    
    def delete(self, entity: T) -> None:
        """Delete entity"""
        self._session.delete(entity)
    
    def delete_many(self, specification: Specification) -> int:
        """Delete entities matching specification"""
        query = self._session.query(self._model)
        if specification:
            query = query.filter(specification.to_sql_clause(self._model))
        count = query.count()
        query.delete(synchronize_session=False)
        return count
    
    def query(self) -> Query:
        """Get raw query for advanced operations"""
        return self._session.query(self._model)

class UnitOfWork:
    """Unit of Work pattern for transaction management"""
    
    def __init__(self, session_factory):
        self._session_factory = session_factory
        self._session: Optional[Session] = None
        self._repositories: Dict[Type, Repository] = {}
        
    def __enter__(self):
        self._session = self._session_factory()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.rollback()
        else:
            self.commit()
        self._session.close()
        self._session = None
        self._repositories.clear()
    
    def commit(self):
        """Commit the transaction"""
        if self._session:
            self._session.commit()
    
    def rollback(self):
        """Rollback the transaction"""
        if self._session:
            self._session.rollback()
    
    def repository(self, model: Type[T]) -> Repository[T]:
        """Get repository for model"""
        if model not in self._repositories:
            self._repositories[model] = self._create_repository(model)
        return self._repositories[model]
    
    def _create_repository(self, model: Type[T]) -> Repository[T]:
        """Create repository instance"""
        # Find specific repository class or use base
        repo_class = self._get_repository_class(model)
        return repo_class(self._session, model)
    
    def _get_repository_class(self, model: Type[T]) -> Type[Repository]:
        """Get specific repository class for model"""
        # Map models to specific repository classes
        repo_map = {
            EvaluationRun: EvaluationRepository,
            Tenant: TenantRepository,
            Project: ProjectRepository,
        }
        return repo_map.get(model, Repository)

# Example specific repository
class EvaluationRepository(Repository[EvaluationRun]):
    """Repository for evaluation-specific operations"""
    
    def get_running_evaluations(self, tenant_id: str) -> List[EvaluationRun]:
        """Get all running evaluations for tenant"""
        return self.query().filter(
            EvaluationRun.tenant_id == tenant_id,
            EvaluationRun.status == 'running'
        ).all()
    
    def get_with_results(self, evaluation_id: str) -> Optional[EvaluationRun]:
        """Get evaluation with eager-loaded results"""
        return self.query().options(
            joinedload(EvaluationRun.models).joinedload(RunModel.probe_results),
            joinedload(EvaluationRun.assessment)
        ).filter(
            EvaluationRun.id == evaluation_id
        ).first()
    
    def get_recent_by_project(self, 
                              project_id: str,
                              limit: int = 10) -> List[EvaluationRun]:
        """Get recent evaluations for project"""
        return self.query().filter(
            EvaluationRun.project_id == project_id
        ).order_by(
            EvaluationRun.created_at.desc()
        ).limit(limit).all()

# Usage example
async def create_evaluation_with_uow(evaluation_data: dict) -> EvaluationRun:
    """Create evaluation using Unit of Work"""
    async with UnitOfWork(get_session) as uow:
        # Get repositories
        eval_repo = uow.repository(EvaluationRun)
        project_repo = uow.repository(Project)
        
        # Verify project exists
        project = project_repo.get(evaluation_data['project_id'])
        if not project:
            raise ValueError("Project not found")
        
        # Create evaluation
        evaluation = EvaluationRun(**evaluation_data)
        eval_repo.add(evaluation)
        
        # Create related entities
        for model_data in evaluation_data['models']:
            model = RunModel(**model_data, evaluation_id=evaluation.id)
            uow.repository(RunModel).add(model)
        
        # Commit transaction
        uow.commit()
        
        return evaluation
```

---

## Service Layer Architecture

### Domain Services with Business Logic

```python
# Advanced service layer implementation
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import asyncio

@dataclass
class ServiceResult:
    """Result wrapper for service operations"""
    success: bool
    data: Optional[Any] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

class BaseService(ABC):
    """Base service with common functionality"""
    
    def __init__(self, uow_factory, event_bus, cache_manager):
        self.uow_factory = uow_factory
        self.event_bus = event_bus
        self.cache = cache_manager
        self.logger = logging.getLogger(self.__class__.__name__)
        
    async def execute_with_transaction(self, func, *args, **kwargs):
        """Execute function within transaction"""
        async with self.uow_factory() as uow:
            try:
                result = await func(uow, *args, **kwargs)
                await uow.commit()
                return ServiceResult(success=True, data=result)
            except Exception as e:
                await uow.rollback()
                self.logger.error(f"Service error: {e}")
                return ServiceResult(success=False, error=str(e))
    
    async def publish_event(self, event: DomainEvent):
        """Publish domain event"""
        await self.event_bus.publish(event)
    
    @traced("service_call")
    async def call_service(self, service_name: str, method: str, **kwargs):
        """Call another service with circuit breaker"""
        service = await self.discover_service(service_name)
        return await service.execute(method, **kwargs)

class EvaluationService(BaseService):
    """Service for evaluation management"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.probe_executor = ProbeExecutorService()
        self.scoring_service = ScoringService()
        self.notification_service = NotificationService()
        
    async def create_evaluation(self,
                                 assessment_id: str,
                                 project_id: str,
                                 models: List[ModelConfiguration],
                                 user_id: str,
                                 tenant_id: str) -> ServiceResult:
        """Create new evaluation with full orchestration"""
        
        # Validate inputs
        validation_result = await self._validate_evaluation_request(
            assessment_id, project_id, models, tenant_id
        )
        if not validation_result.success:
            return validation_result
        
        # Check quotas
        quota_result = await self._check_quotas(tenant_id, len(models))
        if not quota_result.success:
            return quota_result
        
        # Create evaluation
        async with self.uow_factory() as uow:
            try:
                # Create evaluation aggregate
                evaluation = EvaluationAggregate.create(
                    assessment_id=assessment_id,
                    project_id=project_id,
                    tenant_id=tenant_id,
                    models=models
                )
                
                # Persist to database
                eval_repo = uow.repository(EvaluationRun)
                db_evaluation = self._map_to_db_model(evaluation)
                eval_repo.add(db_evaluation)
                
                # Create run models
                for model_config in models:
                    run_model = RunModel(
                        evaluation_id=evaluation.id,
                        model_id=model_config.model_id,
                        parameters=model_config.parameters
                    )
                    uow.repository(RunModel).add(run_model)
                
                await uow.commit()
                
                # Publish event
                await self.publish_event(
                    EvaluationCreatedEvent(
                        aggregate_id=evaluation.id,
                        tenant_id=tenant_id,
                        user_id=user_id
                    )
                )
                
                # Queue for processing
                await self._queue_evaluation(evaluation.id)
                
                # Send notification
                await self.notification_service.notify_evaluation_created(
                    evaluation.id, user_id
                )
                
                return ServiceResult(
                    success=True,
                    data=evaluation,
                    metadata={'queued': True}
                )
                
            except Exception as e:
                self.logger.error(f"Failed to create evaluation: {e}")
                await uow.rollback()
                return ServiceResult(success=False, error=str(e))
    
    async def execute_evaluation(self, evaluation_id: str) -> ServiceResult:
        """Execute evaluation with probe orchestration"""
        
        async with self.uow_factory() as uow:
            # Load evaluation
            eval_repo = uow.repository(EvaluationRun)
            evaluation = eval_repo.get_with_results(evaluation_id)
            
            if not evaluation:
                return ServiceResult(success=False, error="Evaluation not found")
            
            try:
                # Update status
                evaluation.status = 'running'
                evaluation.started_at = datetime.utcnow()
                await uow.commit()
                
                # Execute probes for each model
                results = {}
                for run_model in evaluation.models:
                    model_results = await self._execute_model_probes(
                        run_model, evaluation.assessment
                    )
                    results[run_model.id] = model_results
                
                # Calculate scores
                scores = await self.scoring_service.calculate_scores(
                    evaluation_id, results
                )
                
                # Update evaluation with results
                evaluation.status = 'completed'
                evaluation.completed_at = datetime.utcnow()
                evaluation.final_scores = scores
                await uow.commit()
                
                # Publish completion event
                await self.publish_event(
                    EvaluationCompletedEvent(
                        aggregate_id=evaluation_id,
                        scores=scores
                    )
                )
                
                return ServiceResult(success=True, data=scores)
                
            except Exception as e:
                # Mark as failed
                evaluation.status = 'failed'
                evaluation.error_message = str(e)
                await uow.commit()
                
                return ServiceResult(success=False, error=str(e))
    
    async def _execute_model_probes(self,
                                     run_model: RunModel,
                                     assessment: Assessment) -> Dict[str, ProbeResult]:
        """Execute all probes for a model"""
        
        results = {}
        
        # Get all probes for assessment
        probes = await self._get_assessment_probes(assessment.id)
        
        # Check cache for existing results
        cached_results = await self._get_cached_results(
            run_model.model_id, probes
        )
        
        # Execute uncached probes
        tasks = []
        for probe in probes:
            cache_key = f"{run_model.model_id}:{probe.id}"
            
            if cache_key in cached_results:
                results[probe.id] = cached_results[cache_key]
            else:
                # Queue probe for execution
                task = asyncio.create_task(
                    self.probe_executor.execute_probe(
                        probe, run_model
                    )
                )
                tasks.append((probe.id, task))
        
        # Wait for all probes to complete
        for probe_id, task in tasks:
            try:
                result = await task
                results[probe_id] = result
                
                # Cache result
                await self._cache_result(
                    run_model.model_id, probe_id, result
                )
            except Exception as e:
                self.logger.error(f"Probe {probe_id} failed: {e}")
                results[probe_id] = ProbeResult(
                    score=0.0,
                    status='failed',
                    error=str(e)
                )
        
        return results
    
    async def _validate_evaluation_request(self,
                                            assessment_id: str,
                                            project_id: str,
                                            models: List[ModelConfiguration],
                                            tenant_id: str) -> ServiceResult:
        """Validate evaluation request"""
        
        async with self.uow_factory() as uow:
            # Check assessment exists
            assessment = uow.repository(Assessment).get(assessment_id)
            if not assessment:
                return ServiceResult(success=False, error="Assessment not found")
            
            # Check project exists and belongs to tenant
            project = uow.repository(Project).get(project_id)
            if not project or project.tenant_id != tenant_id:
                return ServiceResult(success=False, error="Invalid project")
            
            # Validate models
            for model_config in models:
                model = uow.repository(LLMModel).get(model_config.model_id)
                if not model:
                    return ServiceResult(
                        success=False,
                        error=f"Model {model_config.model_id} not found"
                    )
            
            return ServiceResult(success=True)
    
    async def _check_quotas(self, tenant_id: str, model_count: int) -> ServiceResult:
        """Check tenant quotas"""
        
        async with self.uow_factory() as uow:
            # Get tenant quota
            tenant = uow.repository(Tenant).get(tenant_id)
            quota = tenant.quota
            
            # Count current evaluations
            current_count = uow.repository(EvaluationRun).count(
                TenantSpecification(tenant_id).and_(
                    StatusSpecification(['running', 'queued'])
                )
            )
            
            if current_count + 1 > quota.max_concurrent_evaluations:
                return ServiceResult(
                    success=False,
                    error="Concurrent evaluation limit exceeded"
                )
            
            # Check model limit
            if model_count > quota.max_models_per_evaluation:
                return ServiceResult(
                    success=False,
                    error=f"Model limit exceeded (max: {quota.max_models_per_evaluation})"
                )
            
            return ServiceResult(success=True)
```

---

## Authentication & Authorization

### OAuth2 with Advanced Security

```python
# Advanced authentication implementation
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials, OAuth2PasswordBearer
from jose import JWTError, jwt, JWK
from passlib.context import CryptContext
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any
import redis
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend

# Password hashing
pwd_context = CryptContext(schemes=["argon2", "bcrypt"], deprecated="auto")

# JWT configuration
class JWTConfig:
    ALGORITHM = "RS256"  # Use RSA for production
    ACCESS_TOKEN_EXPIRE_MINUTES = 30
    REFRESH_TOKEN_EXPIRE_DAYS = 30
    ISSUER = "sacra2-auth"
    AUDIENCE = "sacra2-api"

class KeyManager:
    """Manage JWT signing keys with rotation"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.current_key_id = None
        self.keys = {}
        self._load_keys()
    
    def _load_keys(self):
        """Load or generate signing keys"""
        # Try to load existing keys from Redis
        stored_keys = self.redis.get("jwt_keys")
        
        if stored_keys:
            self.keys = json.loads(stored_keys)
            self.current_key_id = self.redis.get("current_key_id")
        else:
            # Generate new key pair
            self._generate_new_key_pair()
    
    def _generate_new_key_pair(self):
        """Generate new RSA key pair"""
        key_id = str(uuid4())
        
        # Generate RSA key pair
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=default_backend()
        )
        
        # Serialize keys
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        public_pem = private_key.public_key().public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        # Store keys
        self.keys[key_id] = {
            'private': private_pem.decode('utf-8'),
            'public': public_pem.decode('utf-8'),
            'created_at': datetime.utcnow().isoformat()
        }
        
        self.current_key_id = key_id
        
        # Save to Redis
        self.redis.set("jwt_keys", json.dumps(self.keys))
        self.redis.set("current_key_id", key_id)
    
    def get_signing_key(self) -> tuple:
        """Get current signing key"""
        return self.current_key_id, self.keys[self.current_key_id]['private']
    
    def get_verification_key(self, key_id: str) -> str:
        """Get public key for verification"""
        if key_id not in self.keys:
            raise ValueError(f"Unknown key ID: {key_id}")
        return self.keys[key_id]['public']
    
    def rotate_keys(self):
        """Rotate signing keys"""
        # Generate new key pair
        self._generate_new_key_pair()
        
        # Keep old keys for verification (grace period)
        cutoff = datetime.utcnow() - timedelta(days=7)
        self.keys = {
            kid: key_data
            for kid, key_data in self.keys.items()
            if datetime.fromisoformat(key_data['created_at']) > cutoff
        }
        
        # Save updated keys
        self.redis.set("jwt_keys", json.dumps(self.keys))

class TokenService:
    """Service for token generation and validation"""
    
    def __init__(self, key_manager: KeyManager, redis_client: redis.Redis):
        self.key_manager = key_manager
        self.redis = redis_client
    
    def create_access_token(self, 
                            user_id: str,
                            tenant_ids: List[str],
                            permissions: Dict[str, List[str]]) -> str:
        """Create JWT access token"""
        
        kid, private_key = self.key_manager.get_signing_key()
        
        # Token payload
        payload = {
            "sub": user_id,
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(minutes=JWTConfig.ACCESS_TOKEN_EXPIRE_MINUTES),
            "iss": JWTConfig.ISSUER,
            "aud": JWTConfig.AUDIENCE,
            "tenants": tenant_ids,
            "permissions": permissions,
            "jti": str(uuid4()),  # JWT ID for revocation
            "type": "access"
        }
        
        # Sign token
        token = jwt.encode(
            payload,
            private_key,
            algorithm=JWTConfig.ALGORITHM,
            headers={"kid": kid}
        )
        
        return token
    
    def create_refresh_token(self, user_id: str) -> str:
        """Create refresh token"""
        
        kid, private_key = self.key_manager.get_signing_key()
        
        payload = {
            "sub": user_id,
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(days=JWTConfig.REFRESH_TOKEN_EXPIRE_DAYS),
            "iss": JWTConfig.ISSUER,
            "aud": JWTConfig.AUDIENCE,
            "jti": str(uuid4()),
            "type": "refresh"
        }
        
        token = jwt.encode(
            payload,
            private_key,
            algorithm=JWTConfig.ALGORITHM,
            headers={"kid": kid}
        )
        
        # Store refresh token in Redis
        self.redis.setex(
            f"refresh_token:{payload['jti']}",
            timedelta(days=JWTConfig.REFRESH_TOKEN_EXPIRE_DAYS),
            user_id
        )
        
        return token
    
    def verify_token(self, token: str, token_type: str = "access") -> Dict[str, Any]:
        """Verify and decode JWT token"""
        
        try:
            # Get token header to find key ID
            unverified_header = jwt.get_unverified_header(token)
            kid = unverified_header.get("kid")
            
            if not kid:
                raise JWTError("No key ID in token")
            
            # Get public key for verification
            public_key = self.key_manager.get_verification_key(kid)
            
            # Verify and decode token
            payload = jwt.decode(
                token,
                public_key,
                algorithms=[JWTConfig.ALGORITHM],
                issuer=JWTConfig.ISSUER,
                audience=JWTConfig.AUDIENCE
            )
            
            # Verify token type
            if payload.get("type") != token_type:
                raise JWTError(f"Invalid token type: expected {token_type}")
            
            # Check if token is revoked
            if self._is_token_revoked(payload["jti"]):
                raise JWTError("Token has been revoked")
            
            return payload
            
        except JWTError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Invalid token: {str(e)}"
            )
    
    def revoke_token(self, jti: str, exp: datetime):
        """Revoke a token by adding to blacklist"""
        
        # Calculate TTL until token expires
        ttl = int((exp - datetime.utcnow()).total_seconds())
        
        if ttl > 0:
            self.redis.setex(f"revoked:{jti}", ttl, "1")
    
    def _is_token_revoked(self, jti: str) -> bool:
        """Check if token is revoked"""
        return self.redis.exists(f"revoked:{jti}") > 0

# Advanced permission system with ABAC
class PermissionEvaluator:
    """Attribute-based access control evaluator"""
    
    def __init__(self):
        self.policies = self._load_policies()
    
    def _load_policies(self) -> List[Policy]:
        """Load ABAC policies"""
        return [
            Policy(
                name="evaluation_create",
                resource="evaluation",
                action="create",
                conditions=[
                    AttributeCondition("user.tenant_id", "==", "resource.tenant_id"),
                    AttributeCondition("user.role", "in", ["admin", "evaluator"]),
                    TimeCondition("business_hours")
                ]
            ),
            Policy(
                name="model_configure",
                resource="model",
                action="configure",
                conditions=[
                    AttributeCondition("user.role", "==", "admin"),
                    AttributeCondition("user.department", "==", "ai_team")
                ]
            )
        ]
    
    def evaluate(self,
                 user: User,
                 resource: Any,
                 action: str,
                 context: Dict[str, Any]) -> bool:
        """Evaluate permission based on attributes"""
        
        # Find applicable policies
        applicable_policies = [
            p for p in self.policies
            if p.resource == resource.__class__.__name__.lower() and p.action == action
        ]
        
        # Evaluate each policy
        for policy in applicable_policies:
            if self._evaluate_policy(policy, user, resource, context):
                return True
        
        return False
    
    def _evaluate_policy(self,
                         policy: Policy,
                         user: User,
                         resource: Any,
                         context: Dict[str, Any]) -> bool:
        """Evaluate single policy"""
        
        for condition in policy.conditions:
            if not condition.evaluate(user, resource, context):
                return False
        
        return True

# Security middleware
class SecurityMiddleware:
    """Comprehensive security middleware"""
    
    def __init__(self, app: FastAPI):
        self.app = app
        self.rate_limiter = RateLimiter()
        self.ip_filter = IPFilter()
        
    async def __call__(self, request: Request, call_next):
        # IP filtering
        if not self.ip_filter.is_allowed(request.client.host):
            return JSONResponse(
                status_code=403,
                content={"detail": "IP address not allowed"}
            )
        
        # Rate limiting
        if not await self.rate_limiter.check_request(request):
            return JSONResponse(
                status_code=429,
                content={"detail": "Rate limit exceeded"}
            )
        
        # Security headers
        response = await call_next(request)
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        
        return response

# Dependency injection for authentication
async def get_current_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer()),
    token_service: TokenService = Depends(get_token_service)
) -> User:
    """Get current authenticated user"""
    
    # Verify token
    payload = token_service.verify_token(credentials.credentials)
    
    # Load user from database
    user = await get_user_by_id(payload["sub"])
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    # Add request context
    user.request_id = request.state.request_id
    user.permissions = payload.get("permissions", {})
    
    return user

def require_permission(permission: str):
    """Decorator for permission checking"""
    
    async def permission_checker(
        user: User = Depends(get_current_user),
        request: Request = None
    ):
        # Extract resource from request if needed
        resource = None
        if request and request.path_params:
            resource_id = request.path_params.get("id")
            if resource_id:
                resource = await load_resource(resource_id)
        
        # Evaluate permission
        evaluator = PermissionEvaluator()
        context = {
            "ip_address": request.client.host if request else None,
            "time": datetime.utcnow(),
            "method": request.method if request else None
        }
        
        if not evaluator.evaluate(user, resource, permission, context):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied: {permission}"
            )
        
        return user
    
    return permission_checker
```

---

## Background Processing Architecture

### Advanced Celery Implementation

```python
# Advanced Celery configuration and tasks
from celery import Celery, Task, group, chain, chord
from celery.signals import task_prerun, task_postrun, task_failure
from kombu import Queue, Exchange
from typing import Dict, List, Any
import structlog
from opentelemetry import trace
from prometheus_client import Counter, Histogram, Gauge
import asyncio

# Metrics
task_counter = Counter('celery_tasks_total', 'Total tasks executed', ['task_name', 'status'])
task_duration = Histogram('celery_task_duration_seconds', 'Task execution duration', ['task_name'])
queue_size = Gauge('celery_queue_size', 'Current queue size', ['queue_name'])

# Structured logging
logger = structlog.get_logger()

# Celery configuration
class CeleryConfig:
    broker_url = 'redis://localhost:6379/0'
    result_backend = 'redis://localhost:6379/1'
    
    task_serializer = 'json'
    accept_content = ['json']
    result_serializer = 'json'
    timezone = 'UTC'
    enable_utc = True
    
    # Task execution settings
    task_acks_late = True
    task_reject_on_worker_lost = True
    task_track_started = True
    task_time_limit = 3600  # 1 hour hard limit
    task_soft_time_limit = 3000  # 50 minutes soft limit
    
    # Worker settings
    worker_prefetch_multiplier = 4
    worker_max_tasks_per_child = 1000
    worker_disable_rate_limits = False
    
    # Queue configuration
    task_default_queue = 'default'
    task_queues = (
        Queue('critical', Exchange('critical'), routing_key='critical', priority=10),
        Queue('evaluations', Exchange('evaluations'), routing_key='evaluations', priority=5),
        Queue('scoring', Exchange('scoring'), routing_key='scoring', priority=3),
        Queue('notifications', Exchange('notifications'), routing_key='notifications', priority=1),
        Queue('maintenance', Exchange('maintenance'), routing_key='maintenance', priority=0),
    )
    
    # Task routing
    task_routes = {
        'tasks.evaluation.*': {'queue': 'evaluations'},
        'tasks.scoring.*': {'queue': 'scoring'},
        'tasks.notification.*': {'queue': 'notifications'},
        'tasks.maintenance.*': {'queue': 'maintenance'},
    }
    
    # Rate limiting
    task_annotations = {
        'tasks.evaluation.execute_probe': {'rate_limit': '100/m'},
        'tasks.notification.send_email': {'rate_limit': '10/s'},
    }
    
    # Result backend settings
    result_expires = 3600  # Results expire after 1 hour
    result_persistent = True
    
    # Beat schedule for periodic tasks
    beat_schedule = {
        'cleanup-old-results': {
            'task': 'tasks.maintenance.cleanup_old_results',
            'schedule': crontab(hour=2, minute=0),  # Daily at 2 AM
        },
        'rotate-keys': {
            'task': 'tasks.security.rotate_jwt_keys',
            'schedule': crontab(day_of_week=0, hour=3, minute=0),  # Weekly
        },
        'generate-reports': {
            'task': 'tasks.reporting.generate_daily_reports',
            'schedule': crontab(hour=6, minute=0),  # Daily at 6 AM
        },
    }

# Initialize Celery
celery_app = Celery('sacra2')
celery_app.config_from_object(CeleryConfig)

# Base task with enhanced functionality
class BaseTask(Task):
    """Enhanced base task with tracing, metrics, and error handling"""
    
    autoretry_for = (Exception,)
    max_retries = 3
    default_retry_delay = 60
    
    def __init__(self):
        super().__init__()
        self.tracer = trace.get_tracer(__name__)
    
    def before_start(self, task_id, args, kwargs):
        """Called before task execution"""
        # Start tracing span
        self.span = self.tracer.start_span(f"celery.{self.name}")
        self.span.set_attribute("task.id", task_id)
        self.span.set_attribute("task.name", self.name)
        
        # Log task start
        logger.info(
            "task_started",
            task_id=task_id,
            task_name=self.name,
            args=args,
            kwargs=kwargs
        )
    
    def on_success(self, retval, task_id, args, kwargs):
        """Called on successful task completion"""
        # Complete span
        if hasattr(self, 'span'):
            self.span.set_attribute("task.status", "success")
            self.span.end()
        
        # Update metrics
        task_counter.labels(task_name=self.name, status='success').inc()
        
        # Log success
        logger.info(
            "task_completed",
            task_id=task_id,
            task_name=self.name,
            result=retval
        )
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Called on task failure"""
        # Record error in span
        if hasattr(self, 'span'):
            self.span.record_exception(exc)
            self.span.set_attribute("task.status", "failed")
            self.span.end()
        
        # Update metrics
        task_counter.labels(task_name=self.name, status='failed').inc()
        
        # Log failure
        logger.error(
            "task_failed",
            task_id=task_id,
            task_name=self.name,
            error=str(exc),
            traceback=einfo.traceback
        )
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """Called when task is retried"""
        logger.warning(
            "task_retry",
            task_id=task_id,
            task_name=self.name,
            attempt=self.request.retries,
            error=str(exc)
        )

# Evaluation tasks with advanced orchestration
@celery_app.task(base=BaseTask, bind=True, name='tasks.evaluation.orchestrate')
def orchestrate_evaluation(self, evaluation_id: str) -> Dict[str, Any]:
    """Orchestrate complete evaluation workflow"""
    
    try:
        # Load evaluation details
        evaluation = load_evaluation(evaluation_id)
        
        # Update status
        update_evaluation_status(evaluation_id, 'running')
        
        # Create probe execution workflow
        probe_tasks = []
        for model in evaluation.models:
            for probe in evaluation.assessment.probes:
                # Check cache first
                cached_result = check_probe_cache(model.id, probe.id)
                
                if not cached_result:
                    # Create probe task
                    task = execute_probe.si(
                        model.id,
                        probe.id,
                        model.parameters
                    )
                    probe_tasks.append(task)
        
        # Create scoring workflow
        scoring_workflow = chord(
            probe_tasks,
            calculate_scores.s(evaluation_id)
        )
        
        # Execute workflow
        result = scoring_workflow.apply_async()
        
        # Wait for completion with timeout
        scores = result.get(timeout=3600)
        
        # Update evaluation with results
        update_evaluation_results(evaluation_id, scores)
        
        # Send notifications
        send_completion_notification.delay(evaluation_id)
        
        return {
            'evaluation_id': evaluation_id,
            'status': 'completed',
            'scores': scores
        }
        
    except SoftTimeLimitExceeded:
        # Handle soft timeout
        logger.warning(f"Evaluation {evaluation_id} approaching timeout")
        update_evaluation_status(evaluation_id, 'timeout')
        raise
        
    except Exception as e:
        # Handle failure
        update_evaluation_status(evaluation_id, 'failed', str(e))
        raise self.retry(exc=e, countdown=60 * (2 ** self.request.retries))

@celery_app.task(base=BaseTask, bind=True, name='tasks.evaluation.execute_probe')
def execute_probe(self,
                  model_id: str,
                  probe_id: str,
                  parameters: Dict[str, Any]) -> Dict[str, Any]:
    """Execute single probe with rate limiting and retries"""
    
    # Acquire rate limit token
    rate_limiter = RateLimiter()
    
    with rate_limiter.acquire(model_id):
        try:
            # Load probe configuration
            probe = load_probe(probe_id)
            model = load_model(model_id)
            
            # Execute probe
            result = asyncio.run(
                execute_probe_async(probe, model, parameters)
            )
            
            # Cache result
            cache_probe_result(model_id, probe_id, result)
            
            return {
                'model_id': model_id,
                'probe_id': probe_id,
                'score': result.score,
                'metadata': result.metadata
            }
            
        except RateLimitExceeded:
            # Retry with exponential backoff
            raise self.retry(countdown=30 * (2 ** self.request.retries))
            
        except ModelAPIError as e:
            # Handle API errors
            if e.is_retryable():
                raise self.retry(exc=e, countdown=60)
            else:
                return {
                    'model_id': model_id,
                    'probe_id': probe_id,
                    'score': 0.0,
                    'error': str(e)
                }

# Advanced task workflows
class EvaluationWorkflow:
    """Complex evaluation workflow orchestrator"""
    
    @staticmethod
    def create_parallel_evaluation(evaluation_id: str) -> group:
        """Create parallel evaluation workflow"""
        
        evaluation = load_evaluation(evaluation_id)
        tasks = []
        
        for model in evaluation.models:
            # Create model evaluation chain
            model_chain = chain(
                prepare_model.si(model.id),
                execute_model_probes.si(model.id, evaluation.assessment_id),
                calculate_model_scores.si(model.id)
            )
            tasks.append(model_chain)
        
        # Return parallel group
        return group(tasks)
    
    @staticmethod
    def create_sequential_evaluation(evaluation_id: str) -> chain:
        """Create sequential evaluation workflow"""
        
        evaluation = load_evaluation(evaluation_id)
        
        # Build sequential chain
        workflow = chain(
            validate_evaluation.si(evaluation_id),
            prepare_resources.si(evaluation_id)
        )
        
        for model in evaluation.models:
            workflow |= evaluate_model.si(model.id, evaluation.assessment_id)
        
        workflow |= aggregate_results.si(evaluation_id)
        workflow |= generate_report.si(evaluation_id)
        
        return workflow
    
    @staticmethod
    def create_map_reduce_evaluation(evaluation_id: str) -> chord:
        """Create map-reduce style evaluation"""
        
        evaluation = load_evaluation(evaluation_id)
        
        # Map phase: execute all probes
        map_tasks = []
        for model in evaluation.models:
            for probe in evaluation.assessment.probes:
                task = execute_probe.si(model.id, probe.id, model.parameters)
                map_tasks.append(task)
        
        # Reduce phase: aggregate results
        reduce_task = aggregate_probe_results.s(evaluation_id)
        
        return chord(map_tasks, reduce_task)

# Task monitoring and management
class TaskMonitor:
    """Monitor and manage Celery tasks"""
    
    def __init__(self, celery_app):
        self.app = celery_app
        self.inspector = celery_app.control.inspect()
    
    def get_active_tasks(self) -> Dict[str, List[Dict]]:
        """Get currently active tasks"""
        return self.inspector.active() or {}
    
    def get_scheduled_tasks(self) -> Dict[str, List[Dict]]:
        """Get scheduled tasks"""
        return self.inspector.scheduled() or {}
    
    def get_queue_lengths(self) -> Dict[str, int]:
        """Get queue lengths"""
        queues = {}
        
        with self.app.connection() as conn:
            for queue_name in ['critical', 'evaluations', 'scoring', 'notifications']:
                queue = conn.default_channel.queue_declare(
                    queue=queue_name,
                    passive=True
                )
                queues[queue_name] = queue.message_count
        
        return queues
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a task"""
        self.app.control.revoke(task_id, terminate=True)
        return True
    
    def pause_queue(self, queue_name: str) -> bool:
        """Pause processing of a queue"""
        self.app.control.cancel_consumer(queue_name)
        return True
    
    def resume_queue(self, queue_name: str) -> bool:
        """Resume processing of a queue"""
        self.app.control.add_consumer(queue_name)
        return True
    
    def get_worker_stats(self) -> Dict[str, Any]:
        """Get worker statistics"""
        stats = self.inspector.stats() or {}
        
        return {
            worker_name: {
                'total_tasks': stat.get('total', {}),
                'pool': stat.get('pool', {}),
                'rusage': stat.get('rusage', {}),
            }
            for worker_name, stat in stats.items()
        }

# Signal handlers for task lifecycle
@task_prerun.connect
def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kw):
    """Handle task pre-run signal"""
    # Update queue metrics
    queue_size.labels(queue_name=task.request.delivery_info['routing_key']).dec()
    
    # Set context for async tasks
    if hasattr(task.request, 'context'):
        set_request_context(task.request.context)

@task_postrun.connect
def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, retval=None, state=None, **kw):
    """Handle task post-run signal"""
    # Record task duration
    if hasattr(task.request, 'start_time'):
        duration = time.time() - task.request.start_time
        task_duration.labels(task_name=task.name).observe(duration)

@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, args=None, kwargs=None, traceback=None, einfo=None, **kw):
    """Handle task failure signal"""
    # Send alert for critical tasks
    if sender.name.startswith('tasks.critical'):
        send_alert(
            f"Critical task failed: {sender.name}",
            {
                'task_id': task_id,
                'exception': str(exception),
                'traceback': traceback
            }
        )
```

---

## Database Design & Optimization

### Advanced Database Patterns

```python
# Advanced database optimization and patterns
from sqlalchemy import create_engine, event, pool, Index, text
from sqlalchemy.orm import sessionmaker, scoped_session, Query
from sqlalchemy.ext.hybrid import hybrid_property, hybrid_method
from sqlalchemy.sql import func
from contextlib import contextmanager
import time

# Connection pool configuration
class DatabaseConfig:
    # Pool settings
    POOL_SIZE = 20
    MAX_OVERFLOW = 30
    POOL_TIMEOUT = 30
    POOL_RECYCLE = 3600
    POOL_PRE_PING = True
    
    # Query settings
    ECHO = False
    ECHO_POOL = False
    FUTURE = True
    
    # Performance settings
    QUERY_CACHE_SIZE = 1200
    STATEMENT_CACHE_SIZE = 1200

# Enhanced database manager
class DatabaseManager:
    """Advanced database manager with monitoring and optimization"""
    
    def __init__(self, database_url: str, **config):
        # Create engine with advanced configuration
        self.engine = create_engine(
            database_url,
            poolclass=pool.QueuePool,
            pool_size=config.get('pool_size', DatabaseConfig.POOL_SIZE),
            max_overflow=config.get('max_overflow', DatabaseConfig.MAX_OVERFLOW),
            pool_timeout=config.get('pool_timeout', DatabaseConfig.POOL_TIMEOUT),
            pool_recycle=config.get('pool_recycle', DatabaseConfig.POOL_RECYCLE),
            pool_pre_ping=config.get('pool_pre_ping', DatabaseConfig.POOL_PRE_PING),
            echo=config.get('echo', DatabaseConfig.ECHO),
            echo_pool=config.get('echo_pool', DatabaseConfig.ECHO_POOL),
            future=config.get('future', DatabaseConfig.FUTURE),
            query_cache_size=config.get('query_cache_size', DatabaseConfig.QUERY_CACHE_SIZE),
            connect_args={
                "connect_timeout": 10,
                "application_name": "sacra2_backend",
                "options": "-c statement_timeout=30000"  # 30 second statement timeout
            }
        )
        
        # Create session factory
        self.SessionLocal = scoped_session(
            sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine,
                expire_on_commit=False
            )
        )
        
        # Setup event listeners
        self._setup_event_listeners()
        
        # Initialize monitoring
        self.query_stats = QueryStatistics()
    
    def _setup_event_listeners(self):
        """Setup SQLAlchemy event listeners"""
        
        # Connection pool events
        @event.listens_for(self.engine, "connect")
        def receive_connect(dbapi_conn, connection_record):
            # Set connection parameters
            with dbapi_conn.cursor() as cursor:
                cursor.execute("SET TIME ZONE 'UTC'")
                cursor.execute("SET lock_timeout = '5s'")
                cursor.execute("SET idle_in_transaction_session_timeout = '60s'")
        
        # Query execution events
        @event.listens_for(self.engine, "before_execute")
        def receive_before_execute(conn, clauseelement, multiparams, params, execution_options):
            conn.info['query_start_time'] = time.time()
        
        @event.listens_for(self.engine, "after_execute")
        def receive_after_execute(conn, clauseelement, multiparams, params, execution_options, result):
            duration = time.time() - conn.info.get('query_start_time', time.time())
            self.query_stats.record_query(str(clauseelement), duration)
            
            # Log slow queries
            if duration > 1.0:
                logger.warning(
                    "slow_query",
                    query=str(clauseelement)[:500],
                    duration=duration,
                    params=params
                )
    
    @contextmanager
    def session(self):
        """Provide a transactional scope"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def execute_read_query(self, query: Query, use_replica: bool = True):
        """Execute read query with optional read replica"""
        
        if use_replica and hasattr(self, 'read_engine'):
            session = sessionmaker(bind=self.read_engine)()
        else:
            session = self.SessionLocal()
        
        try:
            return query.with_session(session).all()
        finally:
            session.close()
    
    def bulk_insert(self, model_class, data: List[Dict]):
        """Efficient bulk insert"""
        
        with self.session() as session:
            session.bulk_insert_mappings(model_class, data)
    
    def bulk_update(self, model_class, data: List[Dict]):
        """Efficient bulk update"""
        
        with self.session() as session:
            session.bulk_update_mappings(model_class, data)

# Query optimization helpers
class QueryOptimizer:
    """Query optimization utilities"""
    
    @staticmethod
    def create_covering_index(table_name: str, columns: List[str], include: List[str] = None):
        """Create covering index for optimal query performance"""
        
        index_name = f"ix_{table_name}_{'_'.join(columns)}"
        column_list = ', '.join(columns)
        
        if include:
            include_list = ', '.join(include)
            sql = f"""
            CREATE INDEX CONCURRENTLY {index_name}
            ON {table_name} ({column_list})
            INCLUDE ({include_list})
            """
        else:
            sql = f"""
            CREATE INDEX CONCURRENTLY {index_name}
            ON {table_name} ({column_list})
            """
        
        return sql
    
    @staticmethod
    def analyze_query_plan(session, query):
        """Analyze query execution plan"""
        
        sql = str(query.statement.compile(compile_kwargs={"literal_binds": True}))
        explain_sql = f"EXPLAIN (ANALYZE, BUFFERS) {sql}"
        
        result = session.execute(text(explain_sql))
        return result.fetchall()
    
    @staticmethod
    def optimize_pagination(query: Query, page: int, per_page: int):
        """Optimize pagination with keyset pagination"""
        
        # Use keyset pagination for better performance
        if hasattr(query.column_descriptions[0]['type'], 'id'):
            return query.order_by('id').limit(per_page).offset((page - 1) * per_page)
        else:
            return query.limit(per_page).offset((page - 1) * per_page)

# Advanced model mixins
class OptimizedMixin:
    """Mixin for optimized model operations"""
    
    @hybrid_property
    def is_recent(self):
        """Check if record is recent (hybrid property)"""
        cutoff = datetime.utcnow() - timedelta(days=7)
        return self.created_at > cutoff
    
    @is_recent.expression
    def is_recent(cls):
        """SQL expression for is_recent"""
        cutoff = datetime.utcnow() - timedelta(days=7)
        return cls.created_at > cutoff
    
    @hybrid_method
    def has_status(self, status):
        """Check status (hybrid method)"""
        return self.status == status
    
    @has_status.expression
    def has_status(cls, status):
        """SQL expression for has_status"""
        return cls.status == status
    
    @classmethod
    def bulk_create(cls, session, records: List[Dict]):
        """Optimized bulk create"""
        
        # Use COPY for very large datasets
        if len(records) > 10000:
            cls._bulk_copy(session, records)
        else:
            session.bulk_insert_mappings(cls, records)
            session.commit()
    
    @classmethod
    def _bulk_copy(cls, session, records: List[Dict]):
        """Use PostgreSQL COPY for ultra-fast inserts"""
        
        import csv
        import io
        
        # Create CSV in memory
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=records[0].keys())
        writer.writerows(records)
        output.seek(0)
        
        # Use raw connection for COPY
        raw_conn = session.connection().connection
        cursor = raw_conn.cursor()
        
        # Execute COPY
        cursor.copy_expert(
            f"COPY {cls.__tablename__} ({','.join(records[0].keys())}) FROM STDIN WITH CSV",
            output
        )
        raw_conn.commit()
        cursor.close()

# Partitioning support for large tables
class PartitionedTable:
    """Support for partitioned tables"""
    
    @staticmethod
    def create_partition_by_range(table_name: str, partition_column: str, partition_name: str, start_value: Any, end_value: Any):
        """Create range partition"""
        
        sql = f"""
        CREATE TABLE {partition_name} PARTITION OF {table_name}
        FOR VALUES FROM ('{start_value}') TO ('{end_value}')
        """
        return sql
    
    @staticmethod
    def create_monthly_partitions(table_name: str, start_date: datetime, num_months: int):
        """Create monthly partitions"""
        
        partitions = []
        current_date = start_date
        
        for i in range(num_months):
            partition_name = f"{table_name}_{current_date.strftime('%Y_%m')}"
            next_date = current_date + relativedelta(months=1)
            
            sql = PartitionedTable.create_partition_by_range(
                table_name,
                'created_at',
                partition_name,
                current_date,
                next_date
            )
            partitions.append(sql)
            
            current_date = next_date
        
        return partitions

# Query statistics and monitoring
class QueryStatistics:
    """Track query performance statistics"""
    
    def __init__(self):
        self.queries = {}
        self.slow_queries = []
    
    def record_query(self, query: str, duration: float):
        """Record query execution"""
        
        query_hash = hashlib.md5(query.encode()).hexdigest()
        
        if query_hash not in self.queries:
            self.queries[query_hash] = {
                'query': query[:200],
                'count': 0,
                'total_time': 0,
                'max_time': 0,
                'min_time': float('inf')
            }
        
        stats = self.queries[query_hash]
        stats['count'] += 1
        stats['total_time'] += duration
        stats['max_time'] = max(stats['max_time'], duration)
        stats['min_time'] = min(stats['min_time'], duration)
        
        # Track slow queries
        if duration > 1.0:
            self.slow_queries.append({
                'query': query,
                'duration': duration,
                'timestamp': datetime.utcnow()
            })
            
            # Keep only last 100 slow queries
            self.slow_queries = self.slow_queries[-100:]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get query statistics"""
        
        stats = []
        for query_hash, data in self.queries.items():
            stats.append({
                'query': data['query'],
                'count': data['count'],
                'avg_time': data['total_time'] / data['count'] if data['count'] > 0 else 0,
                'max_time': data['max_time'],
                'min_time': data['min_time']
            })
        
        # Sort by total time
        stats.sort(key=lambda x: x['avg_time'] * x['count'], reverse=True)
        
        return {
            'top_queries': stats[:20],
            'slow_queries': self.slow_queries[-20:],
            'total_queries': sum(q['count'] for q in self.queries.values())
        }
```

(Document continues with remaining sections in sacra2-backend-architecture-part2.md)