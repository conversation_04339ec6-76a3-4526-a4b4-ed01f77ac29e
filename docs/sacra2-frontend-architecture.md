# SACRA2 Frontend Architecture: Comprehensive Guide

## Executive Summary

El frontend de SACRA2 es una SPA moderna construida con React, Vite, TypeScript, Ant Design, Ant Design Pro y ProComponents. Esta guía establece la arquitectura de referencia para el desarrollo de tu panel admin, habilitando un entorno escalable, fácil de mantener y optimizado para productividad, seguridad y experiencia de usuario.

### Claves arquitectónicas

- **Desarrollo ultrarrápido y recarga en caliente** con Vite
- **Componentes listos para producción**: formularios, tablas, layouts profesionales (Ant Design, Ant Design Pro / ProComponents)
- **Manejo de estado eficiente**: React Query para sincronización del server-state
- **Inmutabilidad ligera** con hooks simples o Zustand si se necesita local state
- **Autenticación robusta** basada en JWT (almacena access token en memoria y cookie httpOnly para refresh)
- **Seguridad**: CORS bien configurado y lógica de token/re-intento en el front

---

## Table of Contents

1. [Technology Stack](#technology-stack)
2. [Application Architecture](#application-architecture)
3. [Component Architecture](#component-architecture)
4. [State Management](#state-management)
5. [Error Handling & Resilience](#error-handling--resilience)
6. [Performance Optimization](#performance-optimization)
7. [Authentication & Authorization](#authentication--authorization)
8. [Data Fetching & Caching](#data-fetching--caching)
9. [Routing & Navigation](#routing--navigation)
10. [Data Visualization](#data-visualization)
11. [Testing Strategy](#testing-strategy)
12. [Accessibility & Internationalization](#accessibility--internationalization)
13. [Performance Monitoring](#performance-monitoring)
14. [Progressive Web App Features](#progressive-web-app-features)
15. [Development Workflow](#development-workflow)
16. [Best Practices & Guidelines](#best-practices--guidelines)

---

## Technology Stack

Basado en experiencias reales de proyectos similares:

### Core Framework Stack

```typescript
// Package versions and configurations
{
  "dependencies": {
    "vite": "^5.0.0",              // Entorno de desarrollo ultrarrápido
    "react": "^18.2.0",            // React con soporte para hooks
    "react-dom": "^18.2.0",        // React DOM
    "typescript": "^5.3.0",        // Strict mode con todas las verificaciones habilitadas
    "antd": "^5.10.0"              // UI empresarial con componentes completos
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/node": "^20.0.0",
    "eslint": "^8.50.0",
    "prettier": "^3.0.0",
    "vite-plugin-react": "^1.0.0"  // Plugin de Vite para React
  }
}
```

### UI & Styling Libraries

```typescript
// UI Component Libraries
"antd": "^5.10.0",                // Biblioteca de componentes empresariales
"@ant-design/icons": "^5.2.0",    // Iconos para Ant Design
"@ant-design/pro-components": "^2.6.0", // Componentes Pro para Ant Design
"styled-components": "^6.0.0",     // CSS-in-JS para estilos personalizados

// Data Visualization
"@ant-design/charts": "^1.4.0",    // Componentes de gráficos basados en G2Plot
"@ant-design/plots": "^1.2.0",     // Gráficos estadísticos
"react-flow": "^11.10.0"           // Diagramas de flujo interactivos
```

Nota: Adoptamos Ant Design Pro y ProComponents (p. ej., ProLayout, ProTable, ProForm) para acelerar la construcción de pantallas administrativas y estandarizar patrones de UI.

### State Management & Data Fetching

```typescript
// Server State Management
"@tanstack/react-query": "^4.35.0", // Sincronización de estado del servidor
"@tanstack/react-query-devtools": "^4.35.0", // Herramientas de desarrollo

// Client State Management
"zustand": "^4.4.0",               // Gestión de estado ligera

// Form Management
"antd": "^5.10.0",                // Formularios integrados en Ant Design
"@ant-design/pro-form": "^2.13.0", // Formularios avanzados
```

### Development & Testing Tools

```typescript
// Testing Framework
"vitest": "^0.34.0",              // Test runner optimizado para Vite
"@testing-library/react": "^14.0.0", // Utilidades de prueba para React
"@testing-library/jest-dom": "^6.1.0", // Matchers DOM
"msw": "^2.0.0",                  // API mocking

// Code Quality
"eslint": "^8.50.0",              // Linter de código
"@typescript-eslint/parser": "^6.0.0", // Parser de TypeScript para ESLint
"husky": "^8.0.0",                // Git hooks
"lint-staged": "^15.0.0"          // Linter para archivos en stage
```

### Code Editor Component

```typescript
// Code Editor for Rules
"@codeium/react-code-editor": "^1.0.0" // Editor de código para reglas
```

### Tree View for Probe Rules (Folder-like UX)

To support rule editing for probes with a familiar file-explorer experience, we will:

- **Use react-arborist** to render a performant, virtualized tree view that simulates a folder/file structure of each probe’s test assets.
  - Library: https://github.com/brimdata/react-arborist
  - Key features we will leverage: keyboard navigation, drag & drop, context menus, async data loading, and controlled selection.
- **Use VS Code SVG icons** for consistent file/folder visuals.
  - Icons: https://github.com/vscode-icons/vscode-icons/tree/master/icons
  - Map common file extensions (e.g., .json, .yaml, .ts, .md) and folders to their corresponding SVGs for instant affordance.

Integration guidelines:
- **State source of truth**: keep the hierarchical data model in a typed structure (e.g., `TreeNode { id, name, type: 'folder' | 'file', children? }`).
- **Icons**: determine icon by `type` and/or extension; fall back to a generic file/folder SVG.
- **Actions**: support create/rename/delete/move via context menu; propagate changes to backend and invalidate React Query caches.
- **Accessibility**: maintain keyboard support and aria attributes provided by react-arborist.

---

## Application Architecture

Esqueleto ideal para tu desarrollo:

### Directory Structure

```
src/
├── auth/               # token store, hooks login/logout
│   ├── AuthContext.tsx
│   ├── AuthProvider.tsx
│   ├── useAuth.ts
│   └── tokenStore.ts
├── api/                # API client (fetch wrapper con reintentos/refetch)
│   ├── client.ts
│   ├── endpoints.ts
│   ├── interceptors.ts
│   └── queries/
│       ├── useProjects.ts
│       ├── useEvaluations.ts
│       └── useModels.ts
├── components/         # UI components (AntD-based)
│   ├── tables/         # tablas CRUD
│   │   ├── ProjectsTable.tsx
│   │   ├── EvaluationsTable.tsx
│   │   └── ModelsTable.tsx
│   ├── forms/          # formularios AntD
│   │   ├── ProjectForm.tsx
│   │   ├── EvaluationForm.tsx
│   │   └── ModelConfigForm.tsx
│   └── layout/         # header, sidebar, layout
│       ├── AppLayout.tsx
│       ├── Header.tsx
│       ├── Sidebar.tsx
│       └── Footer.tsx
├── pages/              # React Router pages
│   ├── Dashboard/
│   │   ├── index.tsx
│   │   └── DashboardMetrics.tsx
│   ├── Projects/
│   │   ├── index.tsx
│   │   ├── ProjectDetail.tsx
│   │   └── ProjectCreate.tsx
│   ├── Evaluations/
│   │   ├── index.tsx
│   │   ├── EvaluationDetail.tsx
│   │   └── EvaluationCreate.tsx
│   ├── Models/
│   │   ├── index.tsx
│   │   └── ModelDetail.tsx
│   └── Settings/
│       ├── index.tsx
│       └── UserSettings.tsx
├── routes.tsx          # configuración de rutas
├── App.tsx             # entry point con React Query provider, layout, router
└── main.tsx            # render raíz Vite config
```

### Component Structure

Utiliza los componentes de Ant Design y ProComponents para UI: tablas avanzadas, formularios con validación, layouts robustos.

#### Organización por responsabilidad

- **Form Components**: Formularios con validación y manejo de errores
- **Data Tables**: Tablas con ordenación, filtrado y paginación
- **Navigation Layouts**: Estructuras de navegación y disposición
- **Feedback UI**: Notificaciones, loaders, mensajes de estado

---

## Component Architecture

### Componentes Ant Design y Ant Design Pro (ProComponents)

La arquitectura de componentes se basa en Ant Design y Ant Design Pro (ProComponents), una biblioteca de UI empresarial completa.

#### Componentes Base (Ant Design)

```typescript
// Uso de componentes Ant Design
import { Button, Space, Typography } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

const ActionButtons = ({ onEdit, onDelete }) => {
  return (
    <Space>
      <Button 
        type="primary" 
        icon={<EditOutlined />} 
        onClick={onEdit}
      >
        Editar
      </Button>
      <Button 
        danger 
        icon={<DeleteOutlined />} 
        onClick={onDelete}
      >
        Eliminar
      </Button>
    </Space>
  );
};
```

#### Patrón de Composición de Tablas

```typescript
// Tabla con Ant Design
import { Table, Tag, Space, Button } from 'antd';
import { EyeOutlined, EditOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const ProjectsTable = ({ data, loading }) => {
  const navigate = useNavigate();
  
  const columns = [
    {
      title: 'Nombre',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Estado',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        let color = status === 'active' ? 'green' : 'volcano';
        return <Tag color={color}>{status.toUpperCase()}</Tag>;
      },
      filters: [
        { text: 'Activo', value: 'active' },
        { text: 'Inactivo', value: 'inactive' },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: 'Fecha',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: (a, b) => new Date(a.createdAt) - new Date(b.createdAt),
    },
    {
      title: 'Acciones',
      key: 'actions',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            icon={<EyeOutlined />} 
            onClick={() => navigate(`/projects/${record.id}`)}
          />
          <Button 
            type="primary" 
            icon={<EditOutlined />} 
            onClick={() => navigate(`/projects/${record.id}/edit`)}
          />
        </Space>
      ),
    },
  ];

  return (
    <Table 
      columns={columns} 
      dataSource={data} 
      rowKey="id" 
      loading={loading}
      pagination={{ 
        pageSize: 10,
        showSizeChanger: true,
        showTotal: (total) => `Total: ${total} proyectos` 
      }}
    />
  );
};
```

### Componentes Inteligentes vs. Presentacionales

```typescript
// Componente inteligente con React Query
import { useQuery } from '@tanstack/react-query';
import { Table, message } from 'antd';
import { fetchProjects } from '@/api/queries/useProjects';
import ProjectsTable from '@/components/tables/ProjectsTable';

export function ProjectsPage() {
  const { data, isLoading, error } = useQuery({
    queryKey: ['projects'],
    queryFn: fetchProjects,
  });

  if (error) {
    message.error('Error al cargar los proyectos');
  }

  return <ProjectsTable data={data} loading={isLoading} />;
}

// Componente presentacional que recibe datos como props
export function ProjectsTable({ data, loading }) {
  // Implementación de la tabla como se mostró anteriormente
}
```

### Patrones de Componentes

#### Formularios con Ant Design

```typescript
// Formulario con Ant Design
import { Form, Input, Select, DatePicker, Button, Space } from 'antd';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createProject } from '@/api/endpoints';
import { useNavigate } from 'react-router-dom';

const ProjectForm = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  
  const mutation = useMutation({
    mutationFn: createProject,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      message.success('Proyecto creado correctamente');
      navigate('/projects');
    },
    onError: () => {
      message.error('Error al crear el proyecto');
    }
  });

  const onFinish = (values) => {
    mutation.mutate(values);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      initialValues={{ status: 'active' }}
    >
      <Form.Item
        name="name"
        label="Nombre"
        rules={[{ required: true, message: 'Por favor ingrese el nombre' }]}
      >
        <Input />
      </Form.Item>
      
      <Form.Item
        name="description"
        label="Descripción"
      >
        <Input.TextArea rows={4} />
      </Form.Item>
      
      <Form.Item
        name="status"
        label="Estado"
      >
        <Select>
          <Select.Option value="active">Activo</Select.Option>
          <Select.Option value="inactive">Inactivo</Select.Option>
        </Select>
      </Form.Item>
      
      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit" loading={mutation.isPending}>
            Guardar
          </Button>
          <Button onClick={() => navigate('/projects')}>Cancelar</Button>
        </Space>
      </Form.Item>
    </Form>
  );
};
```

---

## State Management

### React Query para Server State

React Query es la solución recomendada para manejar el estado del servidor y las operaciones asíncronas.

```typescript
// src/api/queries/useProjects.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { fetchProjects, createProject, updateProject, deleteProject } from '../endpoints';

// Hook para obtener proyectos
export const useProjects = () => {
  return useQuery({
    queryKey: ['projects'],
    queryFn: fetchProjects,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: 2,
    onError: (error) => {
      message.error('Error al cargar los proyectos');
      console.error('Error fetching projects:', error);
    }
  });
};

// Hook para crear un proyecto
export const useCreateProject = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: createProject,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      message.success('Proyecto creado correctamente');
    },
    onError: (error) => {
      message.error('Error al crear el proyecto');
      console.error('Error creating project:', error);
    }
  });
};

// Hook para actualizar un proyecto
export const useUpdateProject = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: updateProject,
    // Actualización optimista
    onMutate: async (updatedProject) => {
      // Cancelar consultas en curso
      await queryClient.cancelQueries({ queryKey: ['projects', updatedProject.id] });
      
      // Guardar el estado anterior
      const previousProject = queryClient.getQueryData(['projects', updatedProject.id]);
      
      // Actualizar la caché optimistamente
      queryClient.setQueryData(['projects', updatedProject.id], updatedProject);
      
      // Actualizar la lista de proyectos
      queryClient.setQueryData(['projects'], (old) => {
        return old?.map(project => 
          project.id === updatedProject.id ? updatedProject : project
        );
      });
      
      return { previousProject };
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      message.success('Proyecto actualizado correctamente');
    },
    onError: (error, variables, context) => {
      // Revertir a los datos anteriores en caso de error
      if (context?.previousProject) {
        queryClient.setQueryData(['projects', variables.id], context.previousProject);
      }
      message.error('Error al actualizar el proyecto');
      console.error('Error updating project:', error);
    }
  });
};
```

### Zustand para UI State

Zustand es ideal para manejar el estado local de la UI cuando React Query no es suficiente.

```typescript
// src/stores/appStore.ts
import { create } from 'zustand';

type AppState = {
  sidebarCollapsed: boolean;
  currentTheme: 'light' | 'dark' | 'system';
  setSidebarCollapsed: (collapsed: boolean) => void;
  toggleSidebar: () => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
};

export const useAppStore = create<AppState>((set) => ({
  sidebarCollapsed: false,
  currentTheme: 'system',
  setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
  toggleSidebar: () => set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed })),
  setTheme: (theme) => set({ currentTheme: theme }),
}));

// Uso en componentes
import { useAppStore } from '@/stores/appStore';

const Header = () => {
  const { toggleSidebar, sidebarCollapsed } = useAppStore();
  
  return (
    <header>
      <Button 
        icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        onClick={toggleSidebar}
      />
    </header>
  );
};
```

### Combinando React Query y Zustand

```typescript
// Ejemplo de uso combinado
const ProjectsPage = () => {
  // Estado del servidor con React Query
  const { data: projects, isLoading } = useProjects();
  const createProjectMutation = useCreateProject();
  
  // Estado de UI con Zustand
  const { sidebarCollapsed } = useAppStore();
  
  // Estado local con useState para el formulario
  const [formVisible, setFormVisible] = useState(false);
  
  const handleCreateProject = (values) => {
    createProjectMutation.mutate(values);
    setFormVisible(false);
  };
  
  return (
    <Layout>
      <Sidebar collapsed={sidebarCollapsed} />
      <Content>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={() => setFormVisible(true)}
        >
          Nuevo Proyecto
        </Button>
        
        <ProjectsTable data={projects} loading={isLoading} />
        
        <Modal
          title="Crear Proyecto"
          open={formVisible}
          onCancel={() => setFormVisible(false)}
          footer={null}
        >
          <ProjectForm onFinish={handleCreateProject} />
        </Modal>
      </Content>
    </Layout>
  );
};
```

---

## Authentication Flow

### Arquitectura de Autenticación

La autenticación se implementa con JWT, siguiendo las mejores prácticas de seguridad:

- **Access Token**: Almacenado en memoria (no en localStorage)
- **Refresh Token**: Almacenado en cookie httpOnly
- **Protección de rutas**: Redirección a login para usuarios no autenticados

### Implementación del AuthContext

```typescript
// src/auth/AuthContext.tsx
import { createContext, useContext, useState, useEffect } from 'react';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';

type User = {
  id: string;
  name: string;
  email: string;
  role: string;
};

type AuthContextType = {
  user: User | null;
  accessToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<string | null>;
};

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  // Verificar autenticación al cargar
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = await refreshToken();
        if (token) {
          const userData = await fetchUserData(token);
          setUser(userData);
          setAccessToken(token);
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Función para iniciar sesión
  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
        credentials: 'include', // Importante para cookies
      });

      if (!response.ok) {
        throw new Error('Login failed');
      }

      const { accessToken } = await response.json();
      setAccessToken(accessToken);

      // Obtener datos del usuario
      const userData = await fetchUserData(accessToken);
      setUser(userData);

      message.success('Inicio de sesión exitoso');
      navigate('/dashboard');
    } catch (error) {
      message.error('Error al iniciar sesión');
      console.error('Login error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Función para cerrar sesión
  const logout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      setAccessToken(null);
      navigate('/login');
    }
  };

  // Función para refrescar el token
  const refreshToken = async (): Promise<string | null> => {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include',
      });

      if (!response.ok) {
        return null;
      }

      const { accessToken } = await response.json();
      setAccessToken(accessToken);
      return accessToken;
    } catch (error) {
      console.error('Token refresh error:', error);
      return null;
    }
  };

  // Función para obtener datos del usuario
  const fetchUserData = async (token: string): Promise<User> => {
    const response = await fetch('/api/auth/me', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch user data');
    }

    return response.json();
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        accessToken,
        isAuthenticated: !!user,
        isLoading,
        login,
        logout,
        refreshToken,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Hook personalizado para usar el contexto de autenticación
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

### Protección de Rutas

```typescript
// src/routes.tsx
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from './auth/useAuth';
import { Spin } from 'antd';

// Componente para rutas protegidas
const ProtectedRoute = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" />
      </div>
    );
  }

  return isAuthenticated ? <Outlet /> : <Navigate to="/login" replace />;
};

// Configuración de rutas
const routes = [
  {
    path: '/',
    element: <Navigate to="/login" replace />,
  },
  {
    path: '/login',
    element: <LoginPage />,
  },
  {
    path: '/',
    element: <ProtectedRoute />,
    children: [
      {
        path: 'dashboard',
        element: <DashboardPage />,
      },
      {
        path: 'projects',
        element: <ProjectsPage />,
      },
      {
        path: 'projects/:id',
        element: <ProjectDetailPage />,
      },
      // Más rutas protegidas...
    ],
  },
];

export default routes;
```

### Interceptor para Refresh Token

```typescript
// src/api/interceptors.ts
import { useAuth } from '../auth/useAuth';

export const createApiClient = () => {
  const { accessToken, refreshToken } = useAuth();
  
  const apiClient = axios.create({
    baseURL: '/api',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  // Interceptor para agregar token a las solicitudes
  apiClient.interceptors.request.use(
    (config) => {
      if (accessToken) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );
  
  // Interceptor para manejar errores 401 (token expirado)
  apiClient.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;
      
      // Si es error 401 y no hemos intentado refrescar el token
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        
        try {
          // Intentar refrescar el token
          const newToken = await refreshToken();
          
          if (newToken) {
            // Reintentar la solicitud original con el nuevo token
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return apiClient(originalRequest);
          }
        } catch (refreshError) {
          console.error('Error refreshing token:', refreshError);
        }
      }
      
      return Promise.reject(error);
    }
  );
  
  return apiClient;
};
```

---

## Developer Experience

### Entorno de Desarrollo Rápido

- **Vite**: Arranque ultrarrápido y recarga en caliente (HMR)
- **TypeScript**: Autocompletado y detección de errores en tiempo de desarrollo
- **Ant Design / ProComponents**: Componentes listos para usar sin configuración adicional

### Herramientas de Desarrollo

- **React Query DevTools**: Inspección del estado de las consultas y caché
- **React Developer Tools**: Inspección de componentes y profiling
- **ESLint + Prettier**: Formateo de código y detección de problemas

### Consideraciones para SSR

Si en el futuro se requiere SSR, se puede migrar a Next.js 15 manteniendo la mayoría del código existente.

---

## Best Practices

### Seguridad

- **NO usar localStorage para tokens**: Vulnerable a XSS
- **Usar cookies httpOnly para refresh tokens**: Protección contra ataques XSS
- **Implementar CORS correctamente**: Limitar orígenes permitidos
- **Validar entradas de usuario**: Tanto en cliente como en servidor

```typescript
// Ejemplo de configuración CORS en el servidor
app.use(cors({
  origin: ['https://tu-dominio.com'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));
```

### Rendimiento

- **Lazy loading de rutas**: Cargar componentes solo cuando se necesitan
- **Optimización de imágenes**: Usar formatos modernos (WebP) y tamaños adecuados
- **Memoización de componentes**: Usar React.memo para componentes puros
- **Virtualización para listas largas**: Renderizar solo elementos visibles

```typescript
// Lazy loading de rutas
import { lazy, Suspense } from 'react';
import { Spin } from 'antd';

const ProjectsPage = lazy(() => import('./pages/Projects'));

// En el router
<Suspense fallback={<Spin size="large" />}>
  <ProjectsPage />
</Suspense>
```

### Testing

- **Tests unitarios**: Componentes y hooks con Vitest y Testing Library
- **Tests de integración**: Flujos completos con MSW para mock de API
- **Tests E2E**: Flujos críticos con Playwright

```typescript
// Test de componente con Testing Library
import { render, screen, fireEvent } from '@testing-library/react';
import { ProjectForm } from './ProjectForm';

test('muestra error cuando el nombre está vacío', async () => {
  const onFinish = vi.fn();
  render(<ProjectForm onFinish={onFinish} />);
  
  fireEvent.click(screen.getByText('Guardar'));
  
  expect(await screen.findByText('Por favor ingrese el nombre')).toBeInTheDocument();
  expect(onFinish).not.toHaveBeenCalled();
});
```

### CI/CD

- **Linting y tests automáticos**: En cada PR
- **Builds de previsualización**: Para cada rama de feature
- **Despliegue automático**: En merge a main

### Mantenibilidad

- **Documentación de componentes**: Con JSDoc o Storybook
- **Convenciones de nombrado**: Consistentes en todo el proyecto
- **Estructura modular**: Componentes pequeños y reutilizables

```typescript
/**
 * Componente de tabla de proyectos con ordenación y filtrado.
 * @param {Object} props - Propiedades del componente
 * @param {Project[]} props.data - Lista de proyectos a mostrar
 * @param {boolean} props.loading - Estado de carga
 * @param {Function} props.onEdit - Callback para edición
 * @param {Function} props.onDelete - Callback para eliminación
 */
export const ProjectsTable = ({ data, loading, onEdit, onDelete }) => {
  // Implementación
};
```

---

## Conclusión

Esta arquitectura proporciona una base sólida para el desarrollo del frontend de SACRA2, con énfasis en:

- **Desarrollo rápido** con Vite y Ant Design/Ant Design Pro (ProComponents)
- **Manejo eficiente del estado** con React Query y Zustand
- **Autenticación segura** con JWT y cookies httpOnly
- **Estructura modular** para facilitar el mantenimiento
- **Prácticas de seguridad** para proteger datos sensibles

Siguiendo estas pautas, el equipo podrá desarrollar una aplicación robusta, mantenible y con una excelente experiencia de usuario.
