# SACRA2 Platform: Comprehensive Architecture Design

## Executive Summary

SACRA2 (Scalable AI Capability & Risk Assessment) is an enterprise-grade microservices platform designed for evaluating AI agents and language models at scale. This document presents a complete architectural solution covering frontend, backend, background processing, and deployment strategies.

### Key Business Value
- **Cost Optimization**: Intelligent caching reduces evaluation costs by up to 80%
- **Scalability**: Multi-tenant architecture supports thousands of concurrent evaluations
- **Security**: Hierarchical RBAC with comprehensive audit trails
- **Extensibility**: Modular design supports 20+ AI providers with pluggable evaluation frameworks

### Architecture Pillars
1. **Frontend**: Next.js 15 with TypeScript, shadcn/ui, and progressive enhancement
2. **Backend**: FastAPI microservices with SQLAlchemy ORM and hierarchical permissions
3. **Background Processing**: Redis-based job queues with Celery for async operations
4. **Infrastructure**: Docker containerization with OAuth2 proxy and PostgreSQL database

---

## Table of Contents

1. [System Architecture Overview](#system-architecture-overview)
2. [Frontend Architecture](#frontend-architecture)
3. [Backend Architecture](#backend-architecture)
4. [Background Processing Architecture](#background-processing-architecture)
5. [Database Design](#database-design)
6. [Security Architecture](#security-architecture)
7. [Implementation Roadmap](#implementation-roadmap)
8. [Performance & Scalability](#performance--scalability)
9. [Deployment Strategy](#deployment-strategy)
10. [Operational Considerations](#operational-considerations)

## Related Documents

- SACRA2 Frontend Architecture: [docs/sacra2-frontend-architecture.md](./sacra2-frontend-architecture.md)
- SACRA2 Backend Architecture: [docs/sacra2-backend-architecture.md](./sacra2-backend-architecture.md)
- Execution Optimization: [docs/sacra2-execution-optimization.md](./sacra2-execution-optimization.md)

---

## System Architecture Overview

### High-Level Component Interaction

```mermaid
%%{init: {'theme': 'neutral'}}%%
graph TD
    %% Application Components
    Frontend["sacra-frontend<br/>Next.js 15<br/>TypeScript<br/>shadcn/ui"] 
    Backend["sacra-backend<br/>FastAPI<br/>SQLAlchemy<br/>Alembic"] 
    Evaluator["sacra-evaluator<br/>LangChain<br/>Provider<br/>Drivers"] 
    LiteLLM["litellm<br/>Proxy Server<br/>Rate Limiting<br/>Fallbacks"] 
    
    %% Infrastructure Components
    Platform["sacra-platform<br/>OAuth2 Proxy<br/>Docker Compose"] 
    Database["PostgreSQL<br/>SACRA2 Schema<br/>LiteLLM Schema"] 
    Redis["Redis<br/>Job Queues<br/>Caching"] 
    
    %% Connections
    Frontend <--> Backend
    Backend <--> Evaluator
    Evaluator <--> LiteLLM
    
    Frontend --> Platform
    Backend --> Database
    Backend --> Redis
    Evaluator --> Database
    Evaluator --> Redis
    LiteLLM --> Database
    
    %% Styling
    classDef app fill:#f9f9f9,stroke:#333,stroke-width:1px
    classDef infra fill:#e6f3ff,stroke:#333,stroke-width:1px
    
    class Frontend,Backend,Evaluator,LiteLLM app
    class Platform,Database,Redis infra
```

### Evaluation Flow Sequence

```mermaid
%%{init: {'theme': 'neutral'}}%%
sequenceDiagram
    autonumber
    actor U as User
    participant F as Frontend (Next.js)
    participant B as Backend (FastAPI)
    participant R as Redis (Broker/Cache)
    participant C as Celery Workers
    participant D as Database (PostgreSQL)
    
    U->>F: Create Evaluation
    F->>B: POST /evaluations
    B->>R: Enqueue tasks (evaluations, probe-exec)
    Note right of R: Routes to appropriate queues
    R-->>C: Deliver tasks
    C->>D: Read Assessment/Probes/Models
    C->>B: Call provider drivers / LiteLLM
    C->>R: Cache interim results
    C->>D: Persist RunProbeResults and Scores
    C-->>B: Task completion callbacks
    B-->>F: Evaluation status updates (poll/websocket)
```

### Core Design Principles

1. **Microservices Architecture**: Each component has distinct responsibilities with clean interfaces
2. **Event-Driven Communication**: Async messaging for evaluation workflows
3. **Multi-Tenant Isolation**: Complete data segregation at tenant level
4. **Horizontal Scalability**: Stateless services with database clustering
5. **Fault Tolerance**: Circuit breakers, retries, and graceful degradation
6. **Observability**: Comprehensive logging, metrics, and distributed tracing

---

## Frontend Architecture

> For the complete frontend architecture, please refer to the dedicated document: [SACRA2 Frontend Architecture](./sacra2-frontend-architecture.md). The summary below highlights key points; implementation details and patterns live in the linked doc.

### Technology Stack

```typescript
// Core Framework Stack
Next.js 15.0+          // App Router with React 19
TypeScript 5.3+        // Strict mode enabled
React 19.0+            // Server Components & Suspense
Tailwind CSS 3.4+      // Utility-first styling
```

### Component Architecture

#### 1. Application Structure

```
src/
├── app/                    # Next.js 15 App Router
│   ├── (auth)/            # Authentication routes
│   ├── (dashboard)/       # Protected dashboard routes
│   ├── api/               # API routes (minimal, prefer backend)
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Landing page
├── components/            # Reusable UI components
│   ├── ui/               # shadcn/ui primitives
│   ├── forms/            # Form components
│   ├── charts/           # Data visualization
│   └── layout/           # Layout components
├── lib/                  # Utilities and configurations
│   ├── api.ts            # API client configuration
│   ├── auth.ts           # Authentication utilities
│   ├── utils.ts          # General utilities
│   └── validations.ts    # Zod schemas
├── hooks/                # Custom React hooks
├── stores/               # State management
├── types/                # TypeScript definitions
└── constants/            # Application constants
```

#### 2. State Management Strategy

**Primary**: React Query (TanStack Query) for server state
**Secondary**: Zustand for client-side state
**Forms**: React Hook Form with Zod validation

```typescript
// API State Management with React Query
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

// Client State Management with Zustand
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface AppState {
  selectedTenant: string | null
  theme: 'light' | 'dark'
  sidebar: {
    collapsed: boolean
    activeSection: string
  }
}

const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      selectedTenant: null,
      theme: 'light',
      sidebar: {
        collapsed: false,
        activeSection: 'dashboard'
      },
      // actions...
    }),
    { name: 'sacra2-app-state' }
  )
)
```

#### 3. Component Design System

**Base Components** (shadcn/ui primitives):
- Button, Input, Select, Dialog, Sheet
- Table, Card, Badge, Progress
- Command, Popover, Tooltip

**Composite Components**:
```typescript
// Example: EvaluationRunCard
interface EvaluationRunCardProps {
  run: EvaluationRun
  onViewDetails: (id: string) => void
  onRetry: (id: string) => void
}

const EvaluationRunCard = ({ run, onViewDetails, onRetry }: EvaluationRunCardProps) => {
  return (
    <Card className="p-6">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          {run.name}
          <Badge variant={getStatusVariant(run.status)}>
            {run.status}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Assessment:</span>
              <span className="ml-2">{run.assessment.name}</span>
            </div>
            <div>
              <span className="font-medium">Models:</span>
              <span className="ml-2">{run.models.length}</span>
            </div>
          </div>
          
          {run.status === 'running' && (
            <Progress value={run.progress} className="w-full" />
          )}
          
          <div className="flex gap-2">
            <Button onClick={() => onViewDetails(run.id)}>
              View Results
            </Button>
            {run.status === 'failed' && (
              <Button variant="outline" onClick={() => onRetry(run.id)}>
                Retry
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
```

#### 4. Routing & Navigation

**App Router Structure**:
```
/                          # Public landing page
/auth/login               # Authentication
/auth/callback           # OAuth callback

/[tenant]/               # Tenant-scoped routes
├── dashboard/           # Overview dashboard
├── projects/           # Project management
├── assessments/        # Assessment configuration
├── models/             # Model management
├── evaluations/        # Evaluation runs
└── settings/           # Tenant settings
```

**Navigation Component**:
```typescript
const Navigation = () => {
  const { tenant } = useAuth()
  const pathname = usePathname()
  
  const navigationItems = [
    { href: `/${tenant}/dashboard`, label: 'Dashboard', icon: LayoutDashboard },
    { href: `/${tenant}/projects`, label: 'Projects', icon: FolderOpen },
    { href: `/${tenant}/assessments`, label: 'Assessments', icon: CheckSquare },
    { href: `/${tenant}/models`, label: 'Models', icon: Cpu },
    { href: `/${tenant}/evaluations`, label: 'Evaluations', icon: Play },
    { href: `/${tenant}/settings`, label: 'Settings', icon: Settings },
  ]
  
  return (
    <nav className="space-y-2">
      {navigationItems.map((item) => (
        <Link
          key={item.href}
          href={item.href}
          className={cn(
            "flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium",
            pathname.startsWith(item.href) 
              ? "bg-accent text-accent-foreground"
              : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
          )}
        >
          <item.icon className="h-4 w-4" />
          <span>{item.label}</span>
        </Link>
      ))}
    </nav>
  )
}
```

#### 5. Data Visualization

**Chart Components** (using Recharts):
```typescript
import { LineChart, BarChart, PieChart } from 'recharts'

// Evaluation Results Dashboard
const EvaluationResultsChart = ({ data }: { data: EvaluationResult[] }) => {
  const chartData = data.map(result => ({
    model: result.model.name,
    score: result.assessment_score,
    capability_scores: result.capability_scores
  }))
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Overall Assessment Scores</CardTitle>
        </CardHeader>
        <CardContent>
          <BarChart width={800} height={400} data={chartData}>
            <XAxis dataKey="model" />
            <YAxis domain={[0, 1]} />
            <Tooltip formatter={(value) => [(value * 100).toFixed(1) + '%', 'Score']} />
            <Bar dataKey="score" fill="#3b82f6" />
          </BarChart>
        </CardContent>
      </Card>
      
      {/* Capability breakdown charts */}
    </div>
  )
}
```

### Authentication & Authorization

#### Client-Side Auth Flow

```typescript
// lib/auth.ts
interface AuthUser {
  id: string
  email: string
  display_name: string
  tenants: Array<{
    id: string
    name: string
    role: string
  }>
}

const useAuth = () => {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    // Check authentication status
    checkAuthStatus()
  }, [])
  
  const login = (tenant?: string) => {
    const params = tenant ? `?tenant=${tenant}` : ''
    window.location.href = `/api/auth/login${params}`
  }
  
  const logout = () => {
    window.location.href = '/api/auth/logout'
```

```python
# repositories/base.py
from typing import Generic, TypeVar, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy.ext.declarative import DeclarativeMeta

ModelType = TypeVar("ModelType", bound=DeclarativeMeta)

class BaseRepository(Generic[ModelType]):
    def __init__(self, model: ModelType, db: Session):
        self.model = model
        self.db = db
    
    def get(self, id: str) -> Optional[ModelType]:
        return self.db.query(self.model).filter(self.model.id == id).first()
    
    def get_multi(
        self, 
        skip: int = 0, 
        limit: int = 100,
        **filters
    ) -> List[ModelType]:
        query = self.db.query(self.model)
        for key, value in filters.items():
            if hasattr(self.model, key):
                query = query.filter(getattr(self.model, key) == value)
        return query.offset(skip).limit(limit).all()
    
    def create(self, obj_in: dict) -> ModelType:
        db_obj = self.model(**obj_in)
        self.db.add(db_obj)
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj
    
    def update(self, db_obj: ModelType, obj_in: dict) -> ModelType:
        for key, value in obj_in.items():
            setattr(db_obj, key, value)
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj

# repositories/evaluation_run.py
class EvaluationRunRepository(BaseRepository[EvaluationRun]):
    def get_by_project(self, project_id: str) -> List[EvaluationRun]:
        return self.db.query(EvaluationRun).filter(
            EvaluationRun.project_id == project_id
        ).order_by(EvaluationRun.created_at.desc()).all()
    
    def get_running_evaluations(self) -> List[EvaluationRun]:
        return self.db.query(EvaluationRun).filter(
            EvaluationRun.status == "running"
        ).all()
```

#### 3. Service Layer Architecture

```python
# services/evaluation_service.py
from typing import List, Optional
from celery import Celery
from .repositories import EvaluationRunRepository, ProbeResultRepository
from .schemas import EvaluationRunCreate, EvaluationRunResponse

class EvaluationService:
    def __init__(
        self,
        eval_repo: EvaluationRunRepository,
        probe_repo: ProbeResultRepository,
        celery_app: Celery
    ):
        self.eval_repo = eval_repo
        self.probe_repo = probe_repo
        self.celery_app = celery_app
    
    async def create_evaluation_run(
        self, 
        run_data: EvaluationRunCreate,
        user_id: str,
        tenant_id: str
    ) -> EvaluationRunResponse:
        """Create and queue new evaluation run"""
        
        # Validate permissions
        await self._validate_evaluation_permissions(
            user_id, tenant_id, run_data.project_id
        )
        
        # Create evaluation run record
        eval_run = self.eval_repo.create({
            "assessment_id": run_data.assessment_id,
            "project_id": run_data.project_id,
            "name": run_data.name,
            "status": "queued",
            "created_at": datetime.utcnow()
        })
        
        # Create run-model associations
        for model_config in run_data.models:
            self._create_run_model(eval_run.id, model_config)
        
        # Queue evaluation job
        self.celery_app.send_task(
            'sacra.evaluate_assessment',
            args=[eval_run.id],
            queue='evaluations'
        )
        
        return EvaluationRunResponse.from_orm(eval_run)
    
    async def get_cached_probe_results(
        self, 
        model_id: str, 
        probe_ids: List[str]
    ) -> Dict[str, Optional[float]]:
        """Retrieve cached probe results for cost optimization"""
        
        cached_results = {}
        for probe_id in probe_ids:
            result = self.probe_repo.get_cached_result(model_id, probe_id)
            cached_results[probe_id] = result.score if result else None
        
        return cached_results
```

#### 4. Authentication & Authorization

```python
# core/security.py
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from sqlalchemy.orm import Session

security = HTTPBearer()

class PermissionChecker:
    def __init__(self, required_permissions: List[str]):
        self.required_permissions = required_permissions
    
    async def __call__(
        self,
        credentials: HTTPAuthorizationCredentials = Depends(security),
        db: Session = Depends(get_database)
    ) -> UserPermissions:
        
        try:
            payload = jwt.decode(
                credentials.credentials,
                settings.JWT_SECRET_KEY,
                algorithms=[settings.JWT_ALGORITHM]
            )
            user_id = payload.get("sub")
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication credentials"
                )
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials"
            )
        
        # Load user permissions
        user_perms = self._get_user_permissions(db, user_id)
        
        # Check required permissions
        if not self._has_permissions(user_perms, self.required_permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        
        return user_perms

# Usage in API endpoints
require_evaluation_read = PermissionChecker(["evaluation:read"])
require_evaluation_write = PermissionChecker(["evaluation:write", "evaluation:read"])

@router.post("/evaluations/", response_model=EvaluationRunResponse)
async def create_evaluation(
    evaluation: EvaluationRunCreate,
    user: UserPermissions = Depends(require_evaluation_write),
    eval_service: EvaluationService = Depends(get_evaluation_service)
):
    return await eval_service.create_evaluation_run(
        evaluation, user.user_id, user.tenant_id
    )
```

#### 5. API Endpoint Design

```python
# api/v1/evaluations.py
from fastapi import APIRouter, Depends, BackgroundTasks, Query
from typing import List, Optional

router = APIRouter(prefix="/evaluations", tags=["evaluations"])

@router.get("/", response_model=List[EvaluationRunResponse])
async def list_evaluations(
    project_id: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    user: UserPermissions = Depends(require_evaluation_read),
    eval_service: EvaluationService = Depends(get_evaluation_service)
):
    """List evaluation runs with filtering and pagination"""
    return await eval_service.list_evaluations(
        user.tenant_id,
        project_id=project_id,
        status=status,
        skip=skip,
        limit=limit
    )

@router.get("/{evaluation_id}/results", response_model=EvaluationResultsResponse)
async def get_evaluation_results(
    evaluation_id: str,
    include_details: bool = Query(False),
    user: UserPermissions = Depends(require_evaluation_read),
    eval_service: EvaluationService = Depends(get_evaluation_service)
):
    """Get detailed results for an evaluation run"""
    return await eval_service.get_evaluation_results(
        evaluation_id,
        user.tenant_id,
        include_details=include_details
    )

@router.post("/{evaluation_id}/retry")
async def retry_evaluation(
    evaluation_id: str,
    background_tasks: BackgroundTasks,
    user: UserPermissions = Depends(require_evaluation_write),
    eval_service: EvaluationService = Depends(get_evaluation_service)
):
    """Retry a failed evaluation run"""
    background_tasks.add_task(
        eval_service.retry_evaluation,
        evaluation_id,
        user.tenant_id
    )
    return {"message": "Evaluation retry queued"}
```

---

## Backend Architecture

> For the complete backend architecture, please refer to the dedicated document: [SACRA2 Backend Architecture](./sacra2-backend-architecture.md). The comprehensive details (DDD, CQRS, repositories, services) live there.

## Background Processing Architecture

> Background processing implementation details are described in the backend architecture document's background processing section.

### Celery-Based Job Queue System

#### 1. Queue Architecture
  
   ```mermaid
   flowchart LR
     A[FastAPI App] -- Enqueue Jobs --> B[Redis Broker]
     C[Celery Workers] -- Consume Tasks --> B
     A -- Monitor Status --> C
     subgraph Queues
       Q1[evaluations]
       Q2[scoring]
       Q3[maintenance/cleanup]
       Q4[probe-exec]
     end
     B <-. routes .-> Q1
     B <-. routes .-> Q2
     B <-. routes .-> Q3
     B <-. routes .-> Q4
     classDef queue fill:#eef6ff,stroke:#60a5fa,color:#1f2937;
     class Q1,Q2,Q3,Q4 queue
   ```

#### 2. Celery Configuration

```python
# celery_app.py
from celery import Celery
from kombu import Queue

celery_app = Celery(
    "sacra2",
    broker="redis://localhost:6379/0",
    backend="redis://localhost:6379/1"
)

celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_routes={
        'sacra.evaluate_assessment': {'queue': 'evaluations'},
        'sacra.calculate_scores': {'queue': 'scoring'},
        'sacra.cleanup_cache': {'queue': 'maintenance'},
    },
    task_annotations={
        'sacra.evaluate_assessment': {'rate_limit': '10/s'},
        'sacra.execute_probe': {'rate_limit': '50/s'},
    }
)

# Queue definitions
celery_app.conf.task_default_queue = 'default'
celery_app.conf.task_queues = (
    Queue('evaluations', routing_key='evaluations'),
    Queue('scoring', routing_key='scoring'),
    Queue('maintenance', routing_key='maintenance'),
)
```

#### 3. Task Definitions

```python
# tasks/evaluation_tasks.py
from celery import Task
from typing import List, Dict
import asyncio

class CallbackTask(Task):
    """Base task with callback support"""
    def on_success(self, retval, task_id, args, kwargs):
        # Update task status in database
        pass
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        # Handle task failure
        pass

@celery_app.task(base=CallbackTask, bind=True)
def evaluate_assessment(self, evaluation_run_id: str):
    """Main evaluation orchestration task"""
    try:
        # Load evaluation run details
        eval_run = get_evaluation_run(evaluation_run_id)
        
        # Update status to running
        update_evaluation_status(evaluation_run_id, "running")
        
        # Get all probes for the assessment
        probes = get_assessment_probes(eval_run.assessment_id)
        
        # Process each model in the evaluation
        for run_model in eval_run.models:
            # Create subtasks for each probe
            probe_tasks = []
            for probe in probes:
                task = execute_probe.delay(
                    run_model.id,
                    probe.id,
                    run_model.model_params_snapshot
                )
                probe_tasks.append(task)
            
            # Wait for all probe tasks to complete
            for task in probe_tasks:
                task.get(propagate=True)
            
            # Calculate aggregated scores
            calculate_model_scores.delay(run_model.id)
        
        # Mark evaluation as complete
        update_evaluation_status(evaluation_run_id, "done")
        
    except Exception as exc:
        update_evaluation_status(evaluation_run_id, "failed", str(exc))
        raise self.retry(exc=exc, countdown=60, max_retries=3)

@celery_app.task(bind=True)
def execute_probe(self, run_model_id: str, probe_id: str, model_params: Dict):
    """Execute a single probe against a model"""
    try:
        # Check cache first
        cached_result = get_cached_probe_result(
            run_model_id, probe_id, model_params
        )
        if cached_result:
            return cached_result
        
        # Load probe and model details
        probe = get_probe(probe_id)
        run_model = get_run_model(run_model_id)
        
        # Rate limiting check
        await rate_limit_check(run_model.model_id)
        
        # Execute probe via evaluator service
        result = await execute_probe_via_evaluator(
            probe, run_model.model_id, model_params
        )
        
        # Cache the result
        cache_probe_result(
            run_model_id, probe_id, model_params, result
        )
        
        return result
        
    except Exception as exc:
        # Log error and retry
        logger.error(f"Probe execution failed: {exc}")
        raise self.retry(exc=exc, countdown=30, max_retries=3)

@celery_app.task
def calculate_model_scores(run_model_id: str):
    """Calculate aggregated scores for a model"""
    try:
        # Get all probe results for this model
        probe_results = get_run_model_probe_results(run_model_id)
        
        # Calculate probeset scores
        probeset_scores = calculate_probeset_scores(run_model_id, probe_results)
        
        # Calculate capability scores
        capability_scores = calculate_capability_scores(run_model_id, probeset_scores)
        
        # Calculate assessment score
        assessment_score = calculate_assessment_score(run_model_id, capability_scores)
        
        # Store all calculated scores
        store_calculated_scores(run_model_id, {
            'probeset_scores': probeset_scores,
            'capability_scores': capability_scores,
            'assessment_score': assessment_score
        })
        
    except Exception as exc:
        logger.error(f"Score calculation failed: {exc}")
        raise
```

#### 4. Rate Limiting & Provider Management

```python
# services/rate_limiter.py
import redis
import asyncio
from typing import Dict, Optional

class RateLimiter:
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.limits: Dict[str, Dict[str, int]] = {}
    
    async def check_rate_limit(
        self, 
        provider_id: str, 
        model_id: str,
        tenant_id: Optional[str] = None
    ) -> bool:
        """Check if request is within rate limits"""
        
        # Get rate limits for model
        rpm_limit = self.get_model_rpm_limit(model_id)
        tpm_limit = self.get_model_tpm_limit(model_id)
        
        # Check concurrent requests limit
        concurrent_key = f"concurrent:{provider_id}:{tenant_id or 'global'}"
        concurrent_count = await self.redis.get(concurrent_key) or 0
        max_concurrent = self.get_max_concurrent_requests(provider_id, tenant_id)
        
        if int(concurrent_count) >= max_concurrent:
            return False
        
        # Check RPM limit
        rpm_key = f"rpm:{model_id}:{tenant_id or 'global'}"
        rpm_count = await self.redis.get(rpm_key) or 0
        
        if int(rpm_count) >= rpm_limit:
            return False
        
        # Increment counters
        await self.redis.incr(concurrent_key)
        await self.redis.incr(rpm_key)
        await self.redis.expire(rpm_key, 60)  # 1 minute window
        
        return True
    
    async def release_rate_limit(
        self, 
        provider_id: str, 
        tenant_id: Optional[str] = None
    ):
        """Release concurrent request slot"""
        concurrent_key = f"concurrent:{provider_id}:{tenant_id or 'global'}"
        await self.redis.decr(concurrent_key)

# Usage in probe execution
class ProbeExecutor:
    def __init__(self, rate_limiter: RateLimiter):
        self.rate_limiter = rate_limiter
    
    async def execute_probe(self, probe_config: Dict) -> Dict:
        provider_id = probe_config['provider_id']
        model_id = probe_config['model_id']
        tenant_id = probe_config.get('tenant_id')
        
        # Wait for rate limit availability
        while not await self.rate_limiter.check_rate_limit(
            provider_id, model_id, tenant_id
        ):
            await asyncio.sleep(1)
        
        try:
            # Execute probe
            result = await self._execute_probe_impl(probe_config)
            return result
        finally:
            # Release rate limit
            await self.rate_limiter.release_rate_limit(provider_id, tenant_id)
```

#### 5. Monitoring & Health Checks

```python
# monitoring/celery_monitor.py
from celery.events.state import State
from celery import Celery
import json

class CeleryMonitor:
    def __init__(self, celery_app: Celery):
        self.celery_app = celery_app
        self.state = State()
    
    def get_queue_lengths(self) -> Dict[str, int]:
        """Get current queue lengths"""
        inspect = self.celery_app.control.inspect()
        active_queues = inspect.active_queues()
        
        queue_lengths = {}
        for worker, queues in active_queues.items():
            for queue in queues:
                queue_name = queue['name']
                if queue_name not in queue_lengths:
                    queue_lengths[queue_name] = 0
                queue_lengths[queue_name] += len(queue.get('messages', []))
        
        return queue_lengths
    
    def get_worker_stats(self) -> Dict[str, Dict]:
        """Get worker statistics"""
        inspect = self.celery_app.control.inspect()
        stats = inspect.stats()

## Probe Configuration and Execution Model

### Overview

```mermaid
flowchart LR
  subgraph Config[Probe Config (JSON filesystem tree)]
    T1[Folders/Files JSON]
    T2[Versioned + Auditable]
  end
  subgraph Materialize[Materialize at Runtime]
    M1[/Temp dir: /tmp/sacra2/run/<eval>/<probe>/]
    M2[Isolated workspace]
  end
  subgraph Execute[Framework-specific Docker]
    D1[(garak)]
    D2[(promptfoo)]
    D3[(deepeval)]
  end
  subgraph Outputs[Results & Artifacts]
    O1[Probe results]
    O2[Logs/metrics]
  end
  Config --> Materialize --> Execute --> Outputs
  classDef grp fill:#f8fafc,stroke:#94a3b8,color:#0f172a;
  class Config,Materialize,Execute,Outputs grp
```

### Probe configuration as a JSON filesystem tree
- **Source of truth**: Hierarchical JSON tree modeling files/folders.
- **Structure**: `{ id, name, type: 'folder'|'file', content?, children? }`.
- **Content**: Inline text/base64 or external references for large artifacts.
- **Versioning**: Versioned per tenant/project for reproducibility.

### Materialization at runtime
- **Temp workspace**: Materialize JSON to unique sandboxed path per run.
- **Isolation**: Namespaced under evaluation and probe IDs.
- **Lifecycle**: JIT create, read by executor, cleanup per retention policy.

### Execution via framework-specific Docker images
- **Executor images**: Mount workspace into `garak`, `promptfoo`, `deepeval`, etc.
- **Convention**: Respect each framework’s expected layout and entrypoints.
- **Parameters**: Inject credentials/params via env and mounted configs.
- **Outputs**: Write results/artifacts back for collection and scoring.

### Caching and reproducibility

- **Deterministic inputs**: Hash of the JSON tree + model params forms the cache key for probe results.
- **Provenance**: Store metadata (tree version, image tag, entrypoint, env) to ensure runs are auditable and reproducible.

---

## Database Design

> For schema design, migrations, indexing strategies, and performance tuning, see the dedicated backend document: [SACRA2 Backend Architecture](./sacra2-backend-architecture.md).

### Optimized Schema Implementation

#### 1. SQLAlchemy Models

```python
# models/base.py
from sqlalchemy import Column, String, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.mysql import CHAR
from datetime import datetime
import uuid

Base = declarative_base()

class TimestampMixin:
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class BaseModel(Base, TimestampMixin):
    __abstract__ = True
    
    id = Column(
        CHAR(36), 
        primary_key=True, 
        default=lambda: str(uuid.uuid4()),
        unique=True,
        nullable=False
    )

# models/tenant.py
from sqlalchemy import Column, String, Text, ForeignKey, Integer, Boolean, JSON
from sqlalchemy.orm import relationship

class Tenant(BaseModel):
    __tablename__ = "tenants"
    
    name = Column(String(255), nullable=False)
    
    # Relationships
    projects = relationship("Project", back_populates="tenant")
    users = relationship("TenantUser", back_populates="tenant")
    provider_configs = relationship("TenantProviderConfig", back_populates="tenant")

class Project(BaseModel):
    __tablename__ = "projects"
    
    tenant_id = Column(CHAR(36), ForeignKey("tenants.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Relationships
    tenant = relationship("Tenant", back_populates="projects")
    evaluation_runs = relationship("EvaluationRun", back_populates="project")

# models/provider.py
class Provider(BaseModel):
    __tablename__ = "providers"
    
    name = Column(String(255), nullable=False)
    url = Column(String(512))
    api_key = Column(String(512))  # Encrypted
    max_concurrent_requests = Column(Integer, default=10)
    driver_id = Column(CHAR(36), ForeignKey("drivers.id"), nullable=False)
    
    # Relationships
    driver = relationship("Driver", back_populates="providers")
    models = relationship("LLMModel", back_populates="provider")
    tenant_configs = relationship("TenantProviderConfig", back_populates="provider")

class LLMModel(BaseModel):
    __tablename__ = "llm_models"
    
    name = Column(String(255), nullable=False)
    version = Column(String(100))
    provider_id = Column(CHAR(36), ForeignKey("providers.id"), nullable=False)
    request_per_minute = Column(Integer, default=60)
    tokens_per_minute = Column(Integer, default=10000)
    
    # Relationships
    provider = relationship("Provider", back_populates="models")
    default_params = relationship("ModelDefaultParam", back_populates="model")
    probe_results = relationship("RunProbeResult", back_populates="model")

# models/evaluation.py
class EvaluationRun(BaseModel):
    __tablename__ = "evaluation_runs"
    
    assessment_id = Column(CHAR(36), ForeignKey("assessments.id"), nullable=False)
    project_id = Column(CHAR(36), ForeignKey("projects.id"), nullable=False)
    name = Column(String(255), nullable=False)
    status = Column(String(50), default="queued")  # queued|running|done|failed
    error_message = Column(Text)
    progress = Column(Integer, default=0)  # 0-100
    
    # Relationships
    assessment = relationship("Assessment", back_populates="evaluation_runs")
    project = relationship("Project", back_populates="evaluation_runs")
    models = relationship("RunModel", back_populates="run")

class RunProbeResult(BaseModel):
    __tablename__ = "run_probe_results"
    
    model_id = Column(CHAR(36), ForeignKey("llm_models.id"), nullable=False)
    probe_id = Column(CHAR(36), ForeignKey("probes.id"), nullable=False)
    score = Column(Float, nullable=False)  # 0..1
    execution_log = Column(JSON)
    model_params_used = Column(JSON)
    executed_at = Column(DateTime, default=datetime.utcnow)
    status = Column(String(50), default="success")  # success|failed|timeout
    version_hash = Column(String(64), nullable=False)  # For cache invalidation
    
    # Unique constraint for caching
    __table_args__ = (
        Index('ix_cache_lookup', 'model_id', 'probe_id', 'version_hash', unique=True),
        Index('ix_executed_at', 'executed_at'),
    )
    
    # Relationships
    model = relationship("LLMModel", back_populates="probe_results")
    probe = relationship("Probe", back_populates="results")
```

#### 2. Database Optimization

```python
# Database connection and optimization
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool

class DatabaseManager:
    def __init__(self, database_url: str):
        self.engine = create_engine(
            database_url,
            # Connection pooling
            poolclass=QueuePool,
            pool_size=20,
            max_overflow=30,
            pool_timeout=30,
            pool_recycle=1800,
            # Performance optimizations
            echo=False,  # Set to True for debugging
            future=True,
        )
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
    
    def get_db(self):
        db = self.SessionLocal()
        try:
            yield db
        finally:
            db.close()

# Query optimization with eager loading
def get_evaluation_runs_with_details(db: Session, project_id: str) -> List[EvaluationRun]:
    return db.query(EvaluationRun)\
        .options(
            joinedload(EvaluationRun.assessment),
            joinedload(EvaluationRun.models).joinedload(RunModel.model),
            joinedload(EvaluationRun.project)
        )\
        .filter(EvaluationRun.project_id == project_id)\
        .order_by(EvaluationRun.created_at.desc())\
        .all()

# Efficient probe result caching queries
def get_cached_probe_results(
    db: Session, 
    model_id: str, 
    probe_ids: List[str],
    version_hash: str
) -> Dict[str, Optional[RunProbeResult]]:
    results = db.query(RunProbeResult)\
        .filter(
            RunProbeResult.model_id == model_id,
            RunProbeResult.probe_id.in_(probe_ids),
            RunProbeResult.version_hash == version_hash
        ).all()
    
    return {result.probe_id: result for result in results}
```

#### 3. Migration Strategy

```python
# alembic/versions/001_initial_schema.py
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

def upgrade():
    # Create core tables
    op.create_table(
        'tenants',
        sa.Column('id', mysql.CHAR(36), primary_key=True),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('created_at', sa.DateTime, nullable=False),
        sa.Column('updated_at', sa.DateTime),
        sa.Index('ix_tenants_name', 'name')
    )
    
    op.create_table(
        'providers',
        sa.Column('id', mysql.CHAR(36), primary_key=True),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('url', sa.String(512)),
        sa.Column('api_key', sa.String(512)),  # Will be encrypted
        sa.Column('max_concurrent_requests', sa.Integer, default=10),
        sa.Column('driver_id', mysql.CHAR(36), nullable=False),
        sa.Column('created_at', sa.DateTime, nullable=False),
        sa.Column('updated_at', sa.DateTime),
        sa.ForeignKeyConstraint(['driver_id'], ['drivers.id']),
        sa.Index('ix_providers_name', 'name')
    )
    
    # Optimized probe results table for caching
    op.create_table(
        'run_probe_results',
        sa.Column('id', mysql.CHAR(36), primary_key=True),
        sa.Column('model_id', mysql.CHAR(36), nullable=False),
        sa.Column('probe_id', mysql.CHAR(36), nullable=False),
        sa.Column('score', sa.Float, nullable=False),
        sa.Column('execution_log', sa.JSON),
        sa.Column('model_params_used', sa.JSON),
        sa.Column('executed_at', sa.DateTime, nullable=False),
        sa.Column('status', sa.String(50), default='success'),
        sa.Column('version_hash', sa.String(64), nullable=False),
        sa.Column('created_at', sa.DateTime, nullable=False),
        sa.Column('updated_at', sa.DateTime),
        sa.ForeignKeyConstraint(['model_id'], ['llm_models.id']),
        sa.ForeignKeyConstraint(['probe_id'], ['probes.id']),
        # Critical index for cache lookups
        sa.Index(
            'ix_cache_lookup', 
            'model_id', 'probe_id', 'version_hash', 
            unique=True
        ),
        sa.Index('ix_executed_at', 'executed_at'),
        sa.Index('ix_model_probe', 'model_id', 'probe_id')
    )

def downgrade():
    op.drop_table('run_probe_results')
    op.drop_table('providers')
    op.drop_table('tenants')
```

#### 4. Data Archival Strategy

```python
# Data archival for long-term storage optimization
from datetime import datetime, timedelta

class DataArchivalService:
    def __init__(self, db: Session):
        self.db = db
    
    def archive_old_evaluations(self, days_old: int = 90):
        """Archive evaluations older than specified days"""
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        
        # Find old evaluations
        old_runs = self.db.query(EvaluationRun)\
            .filter(EvaluationRun.created_at < cutoff_date)\
            .filter(EvaluationRun.status.in_(['done', 'failed']))\
            .all()
        
        for run in old_runs:
            # Create archive record
            archive_data = self._serialize_evaluation_run(run)
            
            # Store in archive table or cold storage
            self._store_archive(archive_data)
            
            # Remove from active tables
            self._cleanup_evaluation_run(run.id)
    
    def cleanup_expired_probe_cache(self, days_old: int = 30):
        """Remove old cached probe results"""
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        
        deleted_count = self.db.query(RunProbeResult)\
            .filter(RunProbeResult.executed_at < cutoff_date)\
            .delete(synchronize_session=False)
        
        self.db.commit()
        return deleted_count
```

---

## Security Architecture

> For authentication/authorization, OAuth2 proxy integration, RBAC, audit logging, and encryption practices, see: [SACRA2 Backend Architecture](./sacra2-backend-architecture.md).

### Multi-Layer Security Implementation

#### 1. Authentication Flow

```python
# OAuth2 Integration with Microsoft Azure AD
from authlib.integrations.fastapi_oauth2 import OAuth2Token
from authlib.integrations.httpx_oauth2 import OAuth2Auth

class AuthenticationService:
    def __init__(self, oauth_config: dict):
        self.oauth = OAuth2Auth(
            client_id=oauth_config['client_id'],
            client_secret=oauth_config['client_secret'],
            server_metadata_url=oauth_config['metadata_url']
        )
    
    async def authenticate_user(self, token: str) -> UserSession:
        """Validate OAuth token and create user session"""
        try:
            # Validate token with provider
            user_info = await self.oauth.parse_id_token(token)
            
            # Get or create user
            user = await self._get_or_create_user(user_info)
            
            # Load user permissions
            permissions = await self._load_user_permissions(user.id)
            
            # Create session
            session = UserSession(
                user_id=user.id,
                email=user.email,
                tenants=permissions.tenants,
                roles=permissions.roles
            )
            
            return session
            
        except Exception as exc:
            logger.error(f"Authentication failed: {exc}")
            raise AuthenticationError("Invalid token")

# JWT Token Management
import jwt
from datetime import datetime, timedelta

class JWTManager:
    def __init__(self, secret_key: str, algorithm: str = "HS256"):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire = timedelta(hours=24)
        self.refresh_token_expire = timedelta(days=30)
    
    def create_access_token(self, user_session: UserSession) -> str:
        """Create JWT access token with user permissions"""
        payload = {
            "sub": user_session.user_id,
            "email": user_session.email,
            "tenants": [
                {
                    "id": t.id,
                    "name": t.name,
                    "roles": t.roles
                } for t in user_session.tenants
            ],
            "exp": datetime.utcnow() + self.access_token_expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str) -> UserSession:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(
                token, self.secret_key, algorithms=[self.algorithm]
            )
            
            if payload.get("type") != "access":
                raise jwt.InvalidTokenError("Invalid token type")
            
            return UserSession.from_jwt_payload(payload)
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.InvalidTokenError as exc:
            raise AuthenticationError(f"Invalid token: {exc}")
```

#### 2. Authorization System

```python
# Hierarchical RBAC Implementation
from enum import Enum
from typing import Set, Optional

class Permission(Enum):
    # Platform-level permissions
    PLATFORM_ADMIN = "platform:admin"
    PLATFORM_READ = "platform:read"
    
    # Tenant-level permissions
    TENANT_ADMIN = "tenant:admin"
    TENANT_USER_MANAGE = "tenant:users:manage"
    TENANT_PROJECT_CREATE = "tenant:projects:create"
    
    # Project-level permissions
    PROJECT_ADMIN = "project:admin"
    PROJECT_READ = "project:read"
    PROJECT_WRITE = "project:write"
    
    # Evaluation permissions
    EVALUATION_CREATE = "evaluation:create"
    EVALUATION_READ = "evaluation:read"
    EVALUATION_DELETE = "evaluation:delete"
    
    # Model permissions
    MODEL_READ = "model:read"
    MODEL_CONFIGURE = "model:configure"

class PermissionChecker:
    def __init__(self, user_session: UserSession):
        self.user_session = user_session
    
    def has_permission(
        self, 
        permission: Permission,
        tenant_id: Optional[str] = None,
        project_id: Optional[str] = None
    ) -> bool:
        """Check if user has specific permission in context"""
        
        # Platform-level permissions
        if permission in [Permission.PLATFORM_ADMIN, Permission.PLATFORM_READ]:
            return self._has_platform_permission(permission)
        
        # Tenant-level permissions
        if tenant_id and permission.value.startswith("tenant:"):
            return self._has_tenant_permission(permission, tenant_id)
        
        # Project-level permissions
        if project_id and permission.value.startswith("project:"):
            return self._has_project_permission(permission, project_id, tenant_id)
        
        return False
    
    def _has_platform_permission(self, permission: Permission) -> bool:
        """Check platform-level permission"""
        return permission in self.user_session.platform_permissions
    
    def _has_tenant_permission(
        self, 
        permission: Permission, 
        tenant_id: str
    ) -> bool:
        """Check tenant-level permission"""
        tenant = self.user_session.get_tenant(tenant_id)
        if not tenant:
            return False
        
        # Check if user has admin role in tenant
        if "tenant_admin" in tenant.roles:
            return True
        
        # Check specific permission
        return permission in tenant.permissions
    
    def _has_project_permission(
        self,
        permission: Permission,
        project_id: str,
        tenant_id: Optional[str] = None
    ) -> bool:
        """Check project-level permission"""
        if not tenant_id:
            # Resolve tenant from project
            tenant_id = self._get_project_tenant(project_id)
        
        project = self.user_session.get_project(project_id, tenant_id)
        if not project:
            return False
        
        # Check if user has admin role in project
        if "project_admin" in project.roles:
            return True
        
        # Check specific permission
        return permission in project.permissions

# Authorization decorators
def require_permission(
    permission: Permission,
    tenant_id_param: Optional[str] = None,
    project_id_param: Optional[str] = None
):
    """Decorator to enforce permission checks"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            user_session = kwargs.get('user_session')
            if not user_session:
                raise HTTPException(
                    status_code=401,
                    detail="Authentication required"
                )
            
            checker = PermissionChecker(user_session)
            
            # Extract tenant/project IDs from parameters
            tenant_id = kwargs.get(tenant_id_param) if tenant_id_param else None
            project_id = kwargs.get(project_id_param) if project_id_param else None
            
            if not checker.has_permission(permission, tenant_id, project_id):
                raise HTTPException(
                    status_code=403,
                    detail=f"Insufficient permissions: {permission.value}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator
```

#### 3. Data Encryption

```python
# Data encryption for sensitive information
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

class EncryptionService:
    def __init__(self, master_key: str):
        # Derive encryption key from master key
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'sacra2_salt',  # Use unique salt per deployment
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(master_key.encode()))
        self.cipher_suite = Fernet(key)
    
    def encrypt(self, plaintext: str) -> str:
        """Encrypt sensitive data"""
        return self.cipher_suite.encrypt(plaintext.encode()).decode()
    
    def decrypt(self, ciphertext: str) -> str:
        """Decrypt sensitive data"""
        return self.cipher_suite.decrypt(ciphertext.encode()).decode()

# API Key management
class APIKeyManager:
    def __init__(self, encryption_service: EncryptionService):
        self.encryption = encryption_service
    
    def store_api_key(self, provider_id: str, api_key: str, tenant_id: str):
        """Securely store provider API key"""
        encrypted_key = self.encryption.encrypt(api_key)
        
        # Store in database with encryption
        config = TenantProviderConfig(
            tenant_id=tenant_id,
            provider_id=provider_id,
            api_key=encrypted_key,
            enabled=True
        )
        # Save to database
    
    def get_api_key(self, provider_id: str, tenant_id: str) -> str:
        """Retrieve and decrypt API key"""
        config = get_tenant_provider_config(tenant_id, provider_id)
        if not config or not config.api_key:
            raise ValueError("API key not found")
        
        return self.encryption.decrypt(config.api_key)
```

#### 4. Audit Logging

```python
# Comprehensive audit trail
from typing import Any, Dict, Optional
import json

class AuditLogger:
    def __init__(self, db: Session):
        self.db = db
    
    def log_action(
        self,
        user_id: str,
        tenant_id: Optional[str],
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """Log user action for audit trail"""
        
        audit_log = AuditLog(
            user_id=user_id,
            tenant_id=tenant_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            details=json.dumps(details) if details else None,
            ip_address=ip_address,
            user_agent=user_agent,
            timestamp=datetime.utcnow()
        )
        
        self.db.add(audit_log)
        self.db.commit()
    
    def get_audit_trail(
        self,
        tenant_id: str,
        user_id: Optional[str] = None,
        resource_type: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100
    ) -> List[AuditLog]:
        """Retrieve audit trail with filters"""
        
        query = self.db.query(AuditLog)\
            .filter(AuditLog.tenant_id == tenant_id)
        
        if user_id:
            query = query.filter(AuditLog.user_id == user_id)
        
        if resource_type:
            query = query.filter(AuditLog.resource_type == resource_type)
        
        if start_date:
            query = query.filter(AuditLog.timestamp >= start_date)
        
        if end_date:
            query = query.filter(AuditLog.timestamp <= end_date)
        
        return query.order_by(AuditLog.timestamp.desc()).limit(limit).all()

# Usage in API endpoints
@require_permission(Permission.EVALUATION_CREATE, tenant_id_param='tenant_id')
async def create_evaluation(
    evaluation_data: EvaluationCreateRequest,
    tenant_id: str,
    user_session: UserSession = Depends(get_current_user),
    audit_logger: AuditLogger = Depends(get_audit_logger),
    request: Request
):
    # Create evaluation
    evaluation = await create_evaluation_run(evaluation_data)
    
    # Log action
    audit_logger.log_action(
        user_id=user_session.user_id,
        tenant_id=tenant_id,
        action="create",
        resource_type="evaluation_run",
        resource_id=evaluation.id,
        details={
            "assessment_id": evaluation_data.assessment_id,
            "project_id": evaluation_data.project_id,
            "models": [m.model_id for m in evaluation_data.models]
        },
        ip_address=request.client.host,
        user_agent=request.headers.get("user-agent")
    )
    
    return evaluation
```

---

## Implementation Roadmap

### Phase-Based Development Plan

#### Phase 1: Foundation (Weeks 1-4)
**Goal**: Establish core infrastructure and basic functionality

**Backend Tasks**:
- [ ] Set up FastAPI project structure with SQLAlchemy
- [ ] Implement base repository pattern and database models  
- [ ] Create Alembic migrations for core tables (tenants, users, projects)
- [ ] Implement OAuth2 authentication with Microsoft Azure AD
- [ ] Set up basic RBAC with permission checking
- [ ] Create health check endpoints

**Frontend Tasks**:
- [ ] Set up Next.js 15 project with TypeScript and Tailwind CSS
- [ ] Install and configure shadcn/ui components
- [ ] Implement authentication flow with OAuth callback handling
- [ ] Create basic layout with navigation and tenant switching
- [ ] Set up React Query for API state management
- [ ] Implement protected route wrapper

**Infrastructure Tasks**:
- [ ] Set up Docker Compose for development environment
- [ ] Configure OAuth2 proxy for authentication
- [ ] Set up PostgreSQL database with schemas for SACRA2 and LiteLLM
- [ ] Implement Redis for session storage
- [ ] Create basic monitoring and logging

**Deliverables**:
- Working authentication system
- Basic tenant and project management
- Development environment ready for team

#### Phase 2: Evaluation Engine (Weeks 5-8)
**Goal**: Implement core evaluation capabilities

**Backend Tasks**:
- [ ] Implement provider and model management APIs
- [ ] Create probe, probeset, capability, and assessment models
- [ ] Build evaluation orchestration service
- [ ] Set up Celery job queues for background processing
- [ ] Implement rate limiting for AI provider APIs
- [ ] Create probe result caching system

**Evaluator Service Tasks**:
- [ ] Implement driver pattern for AI providers
- [ ] Create LangChain integration for probe execution
- [ ] Build rate limiter with Redis backend
- [ ] Implement retry logic and error handling
- [ ] Create provider-specific drivers (OpenAI, Anthropic, etc.)

**Frontend Tasks**:
- [ ] Build assessment configuration interface
- [ ] Create model selection and configuration forms
- [ ] Implement evaluation run creation wizard
- [ ] Add real-time status updates for running evaluations
- [ ] Create basic results visualization

**Deliverables**:
- Functional evaluation engine
- Provider integration for major AI services
- Basic evaluation workflow from frontend

#### Phase 3: Advanced Features (Weeks 9-12)
**Goal**: Add sophisticated features and optimizations

**Backend Tasks**:
- [ ] Implement intelligent probe result caching with version hashing
- [ ] Add advanced scoring algorithms (weighted, rule-based, custom)
- [ ] Create batch evaluation capabilities
- [ ] Implement evaluation comparison and analytics
- [ ] Add comprehensive audit logging
- [ ] Build data export and reporting APIs

**Frontend Tasks**:
- [ ] Create advanced results dashboard with charts
- [ ] Implement evaluation comparison interface
- [ ] Build comprehensive settings and configuration pages
- [ ] Add data export functionality
- [ ] Create audit trail viewer
- [ ] Implement advanced filtering and search

**Background Processing**:
- [ ] Optimize job queue performance
- [ ] Implement job priority and scheduling
- [ ] Add automatic retry and failure recovery
- [ ] Create monitoring and alerting for background jobs

**Deliverables**:
- Production-ready platform with advanced features
- Comprehensive monitoring and alerting
- Full-featured frontend interface

#### Phase 4: Production Readiness (Weeks 13-16)
**Goal**: Prepare for production deployment

**Infrastructure Tasks**:
- [ ] Set up Kubernetes deployment manifests
- [ ] Implement horizontal pod autoscaling
- [ ] Configure production monitoring with Prometheus/Grafana
- [ ] Set up log aggregation with ELK stack
- [ ] Implement backup and disaster recovery
- [ ] Create CI/CD pipelines

**Security & Compliance**:
- [ ] Complete security audit and penetration testing
- [ ] Implement data encryption at rest
- [ ] Add compliance reporting features
- [ ] Create security documentation
- [ ] Implement automated security scanning

**Performance Optimization**:
- [ ] Database query optimization and indexing
- [ ] Frontend performance optimization
- [ ] API response caching
- [ ] CDN setup for static assets
- [ ] Load testing and optimization

**Documentation & Training**:
- [ ] Complete API documentation
- [ ] Create user guides and tutorials
- [ ] Develop admin documentation
- [ ] Conduct team training sessions

**Deliverables**:
- Production-ready deployment
- Comprehensive documentation
- Trained operations team

---

## Performance & Scalability

> For detailed performance strategies, caching patterns, and execution optimizations, see: [SACRA2 Execution Optimization](./sacra2-execution-optimization.md). This section provides only high-level targets and architecture.

### Performance Targets

#### Response Time Requirements
- **API Endpoints**: < 200ms average, < 500ms 95th percentile
- **Frontend Page Loads**: < 2s initial load, < 1s navigation
- **Evaluation Processing**: Variable based on probe complexity
- **Database Queries**: < 100ms for simple queries, < 1s for complex aggregations

#### Throughput Requirements
- **Concurrent Users**: 1,000+ per tenant
- **API Requests**: 10,000+ requests per minute
- **Background Jobs**: 100+ concurrent evaluations
- **Database**: 1,000+ queries per second

### Scalability Architecture

#### 1. Horizontal Scaling Strategy

```yaml
# Kubernetes deployment for horizontal scaling
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sacra-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: sacra-backend
  template:
    metadata:
      labels:
        app: sacra-backend
    spec:
      containers:
      - name: backend
        image: sacra/backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: sacra-backend-service
spec:
  selector:
    app: sacra-backend
  ports:
  - port: 80
    targetPort: 8000
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: sacra-backend-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: sacra-backend
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

#### 2. Database Scaling

```python
# Database read/write splitting
class DatabaseRouter:
    def __init__(self, write_db_url: str, read_db_urls: List[str]):
        self.write_engine = create_engine(write_db_url)
        self.read_engines = [
            create_engine(url) for url in read_db_urls
        ]
        self.read_engine_index = 0
    
    def get_write_session(self) -> Session:
        """Get session for write operations"""
        return sessionmaker(bind=self.write_engine)()
    
    def get_read_session(self) -> Session:
        """Get session for read operations with load balancing"""
        engine = self.read_engines[self.read_engine_index]
        self.read_engine_index = (self.read_engine_index + 1) % len(self.read_engines)
        return sessionmaker(bind=engine)()

# Usage in repositories
class OptimizedRepository:
    def __init__(self, db_router: DatabaseRouter):
        self.db_router = db_router
    
    def get(self, id: str) -> Optional[Model]:
        """Use read replica for get operations"""
        db = self.db_router.get_read_session()
        try:
            return db.query(self.model).filter(self.model.id == id).first()
        finally:
            db.close()
    
    def create(self, data: dict) -> Model:
        """Use write database for create operations"""
        db = self.db_router.get_write_session()
        try:
            obj = self.model(**data)
            db.add(obj)
            db.commit()
            db.refresh(obj)
            return obj
        finally:
            db.close()
```

#### 3. Caching Strategy

```python
# Multi-layer caching implementation
import redis
from functools import wraps
import json
import hashlib

class CacheManager:
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.default_ttl = 3600  # 1 hour
    
    def cache_key(self, prefix: str, **kwargs) -> str:
        """Generate consistent cache keys"""
        key_data = json.dumps(kwargs, sort_keys=True)
        key_hash = hashlib.md5(key_data.encode()).hexdigest()
        return f"{prefix}:{key_hash}"
    
    def get(self, key: str):
        """Get cached value"""
        value = self.redis.get(key)
        return json.loads(value) if value else None
    
    def set(self, key: str, value, ttl: int = None):
        """Set cached value"""
        ttl = ttl or self.default_ttl
        self.redis.setex(key, ttl, json.dumps(value))
    
    def invalidate_pattern(self, pattern: str):
        """Invalidate cache keys matching pattern"""
        keys = self.redis.keys(pattern)
        if keys:
            self.redis.delete(*keys)

# Caching decorator
def cached(prefix: str, ttl: int = 3600):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_manager = get_cache_manager()
            cache_key = cache_manager.cache_key(prefix, args=args, kwargs=kwargs)
            
            # Try cache first
            cached_result = cache_manager.get(cache_key)
            if cached_result:
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl)
            
            return result
        return wrapper
    return decorator

# Usage in services
@cached("evaluation_results", ttl=1800)
async def get_evaluation_results(evaluation_id: str) -> EvaluationResults:
    """Cached evaluation results with 30-minute TTL"""
    return await fetch_evaluation_results_from_db(evaluation_id)
```

#### 4. Frontend Performance Optimization

```typescript
// Next.js optimization strategies
import { memo, useMemo, useCallback } from 'react'
import dynamic from 'next/dynamic'
import { useVirtualizer } from '@tanstack/react-virtual'

// Code splitting for heavy components
const EvaluationResultsChart = dynamic(
  () => import('./EvaluationResultsChart'),
  { 
    loading: () => <ChartSkeleton />,
    ssr: false  // Disable SSR for client-only components
  }
)

// Memoized expensive components
const EvaluationRunList = memo(({ runs, onSelectRun }: EvaluationRunListProps) => {
  const sortedRuns = useMemo(
    () => runs.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()),
    [runs]
  )
  
  const handleSelectRun = useCallback(
    (runId: string) => {
      onSelectRun(runId)
    },
    [onSelectRun]
  )
  
  return (
    <div className="space-y-4">
      {sortedRuns.map(run => (
        <EvaluationRunCard
          key={run.id}
          run={run}
          onSelect={handleSelectRun}
        />
      ))}
    </div>
  )
})

// Virtual scrolling for large lists
const VirtualizedEvaluationList = ({ evaluations }: { evaluations: Evaluation[] }) => {
  const parentRef = useRef<HTMLDivElement>(null)
  
  const virtualizer = useVirtualizer({
    count: evaluations.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 120,  // Estimated row height
    overscan: 5
  })
  
  return (
    <div ref={parentRef} className="h-96 overflow-auto">
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative'
        }}
      >
        {virtualizer.getVirtualItems().map(virtualItem => (
          <div
            key={virtualItem.key}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualItem.size}px`,
              transform: `translateY(${virtualItem.start}px)`
            }}
          >
            <EvaluationRunCard run={evaluations[virtualItem.index]} />
          </div>
        ))}
      </div>
    </div>
  )
}

// API data prefetching and caching
const useEvaluationData = (projectId: string) => {
  // Prefetch related data
  const queryClient = useQueryClient()
  
  const evaluationsQuery = useQuery({
    queryKey: ['evaluations', projectId],
    queryFn: () => fetchEvaluations(projectId),
    staleTime: 5 * 60 * 1000,  // 5 minutes
    onSuccess: (data) => {
      // Prefetch individual evaluation details
      data.forEach(evaluation => {
        queryClient.prefetchQuery({
          queryKey: ['evaluation', evaluation.id],
          queryFn: () => fetchEvaluationDetails(evaluation.id),
          staleTime: 10 * 60 * 1000  // 10 minutes
        })
      })
    }
  })
  
  return evaluationsQuery
}
```

---

## Deployment Strategy

### Production Environment Architecture

#### 1. Kubernetes Cluster Setup

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: sacra2-production
  labels:
    name: sacra2-production

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: sacra2-config
  namespace: sacra2-production
data:
  DATABASE_HOST: "mysql-primary.sacra2-production.svc.cluster.local"
  DATABASE_NAME: "sacra2"
  REDIS_HOST: "redis.sacra2-production.svc.cluster.local"
  CELERY_BROKER_URL: "redis://redis.sacra2-production.svc.cluster.local:6379/0"
  OAUTH_PROVIDER_URL: "https://login.microsoftonline.com/tenant-id"
  
---
# secrets.yaml (encrypted in practice)
apiVersion: v1
kind: Secret
metadata:
  name: sacra2-secrets
  namespace: sacra2-production
type: Opaque
data:
  database-password: <base64-encoded>
  jwt-secret: <base64-encoded>
  oauth-client-secret: <base64-encoded>
  encryption-key: <base64-encoded>
```

#### 2. Database Deployment

```yaml
# PostgreSQL StatefulSet with multiple schemas
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres-primary
  namespace: sacra2-production
spec:
  serviceName: postgres-primary
  replicas: 1
  selector:
    matchLabels:
      app: postgres
      role: primary
  template:
    metadata:
      labels:
        app: postgres
        role: primary
    spec:
      containers:
      - name: postgres
        image: postgres:15
        env:
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: sacra2-secrets
              key: database-password
        - name: POSTGRES_DB
          value: sacra2_platform
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: postgres-config
          mountPath: /etc/postgresql/conf.d
        - name: postgres-init
          mountPath: /docker-entrypoint-initdb.d
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
      volumes:
      - name: postgres-config
        configMap:
          name: postgres-config
      - name: postgres-init
        configMap:
          name: postgres-init-scripts
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: ssd-storage
      resources:
        requests:
          storage: 100Gi

---
# PostgreSQL Schema Initialization
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-init-scripts
  namespace: sacra2-production
data:
  init-schemas.sql: |
    -- Create schemas for SACRA2 and LiteLLM
    CREATE SCHEMA IF NOT EXISTS sacra2;
    CREATE SCHEMA IF NOT EXISTS litellm;
    
    -- Set default permissions
    ALTER DEFAULT PRIVILEGES IN SCHEMA sacra2 GRANT ALL ON TABLES TO sacra2_user;
    ALTER DEFAULT PRIVILEGES IN SCHEMA litellm GRANT ALL ON TABLES TO litellm_user;
    
    -- Create application users
    CREATE USER sacra2_user WITH PASSWORD '${SACRA2_DB_PASSWORD}';
    CREATE USER litellm_user WITH PASSWORD '${LITELLM_DB_PASSWORD}';
    
    -- Grant schema usage
    GRANT USAGE ON SCHEMA sacra2 TO sacra2_user;
    GRANT USAGE ON SCHEMA litellm TO litellm_user;
    
    -- Grant cross-schema read permissions for integration
    GRANT SELECT ON ALL TABLES IN SCHEMA litellm TO sacra2_user;

---
# PostgreSQL Read Replica
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql-replica
  namespace: sacra2-production
spec:
  replicas: 2
  selector:
    matchLabels:
      app: mysql
      role: replica
  template:
    metadata:
      labels:
        app: mysql
        role: replica
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: sacra2-secrets
              key: database-password
        # Replica configuration
        command:
        - mysqld
        - --server-id=2
        - --log-bin=mysql-bin
        - --relay-log=relay-log
        - --read-only=1
```

#### 3. Application Deployment

```yaml
# Backend API Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sacra-backend
  namespace: sacra2-production
spec:
  replicas: 5
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 1
  selector:
    matchLabels:
      app: sacra-backend
  template:
    metadata:
      labels:
        app: sacra-backend
    spec:
      containers:
      - name: backend
        image: sacra2/backend:v1.0.0
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: sacra2-config
        - secretRef:
            name: sacra2-secrets
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"

---
# Frontend Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sacra-frontend
  namespace: sacra2-production
spec:
  replicas: 3
  selector:
    matchLabels:
      app: sacra-frontend
  template:
    metadata:
      labels:
        app: sacra-frontend
    spec:
      containers:
      - name: frontend
        image: sacra2/frontend:v1.0.0
        ports:
        - containerPort: 3000
        env:
        - name: NEXT_PUBLIC_API_URL
          value: "https://api.sacra2.com"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

#### 4. Background Workers

```yaml
# Celery Worker Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: celery-workers
  namespace: sacra2-production
spec:
  replicas: 10
  selector:
    matchLabels:
      app: celery-worker
  template:
    metadata:
      labels:
        app: celery-worker
    spec:
      containers:
      - name: worker
        image: sacra2/backend:v1.0.0
        command: ["celery"]
        args: [
          "-A", "src.celery_app",
          "worker",
          "--loglevel=info",
          "--concurrency=4",
          "--max-tasks-per-child=100"
        ]
        envFrom:
        - configMapRef:
            name: sacra2-config
        - secretRef:
            name: sacra2-secrets
        resources:
          requests:
            memory: "1Gi"
            cpu: "1000m"
          limits:
            memory: "2Gi"
            cpu: "2000m"

---
# Celery Beat Scheduler
apiVersion: apps/v1
kind: Deployment
metadata:
  name: celery-beat
  namespace: sacra2-production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: celery-beat
  template:
    metadata:
      labels:
        app: celery-beat
    spec:
      containers:
      - name: beat
        image: sacra2/backend:v1.0.0
        command: ["celery"]
        args: [
          "-A", "src.celery_app",
          "beat",
          "--loglevel=info",
          "--schedule=/tmp/celerybeat-schedule"
        ]
        envFrom:
        - configMapRef:
            name: sacra2-config
        - secretRef:
            name: sacra2-secrets
```

#### 5. Ingress and Load Balancing

```yaml
# Ingress Configuration
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: sacra2-ingress
  namespace: sacra2-production
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "1000"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - sacra2.com
    - api.sacra2.com
    secretName: sacra2-tls
  rules:
  - host: sacra2.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: sacra-frontend
            port:
              number: 80
  - host: api.sacra2.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: sacra-backend
            port:
              number: 80
```

### CI/CD Pipeline

#### 1. GitHub Actions Workflow

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test
          MYSQL_DATABASE: sacra2_test
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
      
      redis:
        image: redis:7
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - uses: actions/checkout@v3
    
    # Backend tests
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install backend dependencies
      run: |
        cd sacra-backend
        pip install -e .
        pip install pytest pytest-cov
    
    - name: Run backend tests
      env:
        DATABASE_URL: mysql://root:test@localhost:3306/sacra2_test
        REDIS_URL: redis://localhost:6379/0
      run: |
        cd sacra-backend
        pytest tests/ --cov=src --cov-report=xml
    
    # Frontend tests
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Install frontend dependencies
      run: |
        cd sacra-frontend
        npm install
    
    - name: Run frontend tests
      run: |
        cd sacra-frontend
        npm run test:ci
        npm run build

  build:
    needs: test
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract version
      id: version
      run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
    
    - name: Build and push backend
      uses: docker/build-push-action@v4
      with:
        context: ./sacra-backend
        push: true
        tags: |
          ghcr.io/sacra2/backend:${{ steps.version.outputs.VERSION }}
          ghcr.io/sacra2/backend:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Build and push frontend
      uses: docker/build-push-action@v4
      with:
        context: ./sacra-frontend
        push: true
        tags: |
          ghcr.io/sacra2/frontend:${{ steps.version.outputs.VERSION }}
          ghcr.io/sacra2/frontend:latest

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    environment: production
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'latest'
    
    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBECONFIG }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig
    
    - name: Extract version
      id: version
      run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
    
    - name: Deploy to Kubernetes
      env:
        VERSION: ${{ steps.version.outputs.VERSION }}
      run: |
        export KUBECONFIG=kubeconfig
        
        # Update deployment images
        kubectl set image deployment/sacra-backend \
          backend=ghcr.io/sacra2/backend:$VERSION \
          -n sacra2-production
        
        kubectl set image deployment/sacra-frontend \
          frontend=ghcr.io/sacra2/frontend:$VERSION \
          -n sacra2-production
        
        kubectl set image deployment/celery-workers \
          worker=ghcr.io/sacra2/backend:$VERSION \
          -n sacra2-production
        
        # Wait for rollout
        kubectl rollout status deployment/sacra-backend -n sacra2-production
        kubectl rollout status deployment/sacra-frontend -n sacra2-production
        kubectl rollout status deployment/celery-workers -n sacra2-production
        
        # Run database migrations
        kubectl run migration-job --rm -i --restart=Never \
          --image=ghcr.io/sacra2/backend:$VERSION \
          --env="DATABASE_URL=$DATABASE_URL" \
          -- alembic upgrade head
```

---

## Operational Considerations

### Monitoring and Observability

#### 1. Prometheus Metrics

```python
# metrics/prometheus.py
from prometheus_client import Counter, Histogram, Gauge, start_http_server
import time

# Application metrics
REQUEST_COUNT = Counter(
    'sacra2_requests_total',
    'Total requests',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'sacra2_request_duration_seconds',
    'Request duration',
    ['method', 'endpoint']
)

ACTIVE_EVALUATIONS = Gauge(
    'sacra2_active_evaluations',
    'Number of running evaluations',
    ['tenant_id']
)

PROBE_EXECUTION_COUNT = Counter(
    'sacra2_probes_executed_total',
    'Total probes executed',
    ['provider', 'model', 'status']
)

CACHE_HIT_RATE = Gauge(
    'sacra2_cache_hit_rate',
    'Cache hit rate for probe results',
    ['cache_type']
)

# Middleware for request metrics
class PrometheusMiddleware:
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        method = scope["method"]
        path = scope["path"]
        
        start_time = time.time()
        
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                status = message["status"]
                REQUEST_COUNT.labels(
                    method=method,
                    endpoint=path,
                    status=status
                ).inc()
                
                duration = time.time() - start_time
                REQUEST_DURATION.labels(
                    method=method,
                    endpoint=path
                ).observe(duration)
            
            await send(message)
        
        await self.app(scope, receive, send_wrapper)

# Start metrics server
start_http_server(8001)
```

#### 2. Structured Logging

```python
# logging/structured.py
import structlog
import logging
from datetime import datetime
import json

def setup_logging():
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

logger = structlog.get_logger()

# Usage in application
async def create_evaluation_run(run_data: EvaluationRunCreate):
    logger.info(
        "evaluation_run_create_started",
        assessment_id=run_data.assessment_id,
        project_id=run_data.project_id,
        model_count=len(run_data.models)
    )
    
    try:
        # Create evaluation
        evaluation = await create_evaluation_impl(run_data)
        
        logger.info(
            "evaluation_run_created",
            evaluation_id=evaluation.id,
            status=evaluation.status,
            duration_ms=(time.time() - start_time) * 1000
        )
        
        return evaluation
        
    except Exception as exc:
        logger.error(
            "evaluation_run_create_failed",
            error=str(exc),
            error_type=type(exc).__name__,
            assessment_id=run_data.assessment_id
        )
        raise
```

#### 3. Health Checks and Readiness Probes

```python
# health/checks.py
from typing import Dict, List
import asyncio
from enum import Enum

class HealthStatus(Enum):
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"

class HealthCheck:
    def __init__(self, name: str):
        self.name = name
    
    async def check(self) -> Dict[str, any]:
        raise NotImplementedError

class DatabaseHealthCheck(HealthCheck):
    def __init__(self, db_session):
        super().__init__("database")
        self.db_session = db_session
    
    async def check(self) -> Dict[str, any]:
        try:
            start_time = time.time()
            # Simple query to test connectivity
            result = await self.db_session.execute("SELECT 1")
            duration = (time.time() - start_time) * 1000
            
            return {
                "status": HealthStatus.HEALTHY.value,
                "response_time_ms": duration,
                "details": "Database connection successful"
            }
        except Exception as exc:
            return {
                "status": HealthStatus.UNHEALTHY.value,
                "error": str(exc),
                "details": "Database connection failed"
            }

class RedisHealthCheck(HealthCheck):
    def __init__(self, redis_client):
        super().__init__("redis")
        self.redis_client = redis_client
    
    async def check(self) -> Dict[str, any]:
        try:
            start_time = time.time()
            await self.redis_client.ping()
            duration = (time.time() - start_time) * 1000
            
            return {
                "status": HealthStatus.HEALTHY.value,
                "response_time_ms": duration,
                "details": "Redis connection successful"
            }
        except Exception as exc:
            return {
                "status": HealthStatus.UNHEALTHY.value,
                "error": str(exc),
                "details": "Redis connection failed"
            }

class CeleryHealthCheck(HealthCheck):
    def __init__(self, celery_app):
        super().__init__("celery")
        self.celery_app = celery_app
    
    async def check(self) -> Dict[str, any]:
        try:
            # Check if workers are responding
            inspect = self.celery_app.control.inspect()
            stats = inspect.stats()
            
            if not stats:
                return {
                    "status": HealthStatus.UNHEALTHY.value,
                    "details": "No active Celery workers found"
                }
            
            worker_count = len(stats)
            return {
                "status": HealthStatus.HEALTHY.value,
                "worker_count": worker_count,
                "details": f"{worker_count} workers active"
            }
        except Exception as exc:
            return {
                "status": HealthStatus.UNHEALTHY.value,
                "error": str(exc),
                "details": "Celery health check failed"
            }

class HealthCheckService:
    def __init__(self):
        self.checks: List[HealthCheck] = []
    
    def add_check(self, health_check: HealthCheck):
        self.checks.append(health_check)
    
    async def run_all_checks(self) -> Dict[str, any]:
        results = {}
        overall_status = HealthStatus.HEALTHY
        
        # Run all checks concurrently
        tasks = [check.check() for check in self.checks]
        check_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for check, result in zip(self.checks, check_results):
            if isinstance(result, Exception):
                results[check.name] = {
                    "status": HealthStatus.UNHEALTHY.value,
                    "error": str(result)
                }
                overall_status = HealthStatus.UNHEALTHY
            else:
                results[check.name] = result
                if result["status"] != HealthStatus.HEALTHY.value:
                    overall_status = HealthStatus.DEGRADED
        
        return {
            "status": overall_status.value,
            "checks": results,
            "timestamp": datetime.utcnow().isoformat()
        }

# API endpoints
@router.get("/health")
async def health_check(
    health_service: HealthCheckService = Depends(get_health_service)
):
    """Comprehensive health check"""
    return await health_service.run_all_checks()

@router.get("/ready")
async def readiness_check(
    health_service: HealthCheckService = Depends(get_health_service)
):
    """Kubernetes readiness probe"""
    result = await health_service.run_all_checks()
    
    if result["status"] == HealthStatus.UNHEALTHY.value:
        raise HTTPException(status_code=503, detail="Service not ready")
    
    return {"status": "ready"}
```

### Backup and Disaster Recovery

#### 1. Database Backup Strategy

```bash
#!/bin/bash
# backup/mysql_backup.sh

set -e

# Configuration
BACKUP_DIR="/backups/mysql"
RETENTION_DAYS=30
DATABASE_NAME="sacra2"
MYSQL_HOST="mysql-primary.sacra2-production.svc.cluster.local"
MYSQL_USER="backup_user"
MYSQL_PASSWORD_FILE="/secrets/mysql-backup-password"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Generate backup filename with timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/sacra2_backup_$TIMESTAMP.sql.gz"

# Create backup
echo "Starting MySQL backup..."
mysqldump \
    --host="$MYSQL_HOST" \
    --user="$MYSQL_USER" \
    --password="$(cat $MYSQL_PASSWORD_FILE)" \
    --single-transaction \
    --routines \
    --triggers \
    --quick \
    --lock-tables=false \
    "$DATABASE_NAME" | gzip > "$BACKUP_FILE"

# Upload to cloud storage (S3)
aws s3 cp "$BACKUP_FILE" "s3://sacra2-backups/mysql/"

# Clean up old backups
find "$BACKUP_DIR" -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete

echo "Backup completed: $BACKUP_FILE"
```

#### 2. Disaster Recovery Plan

```yaml
# disaster-recovery/restore-procedure.md
# SACRA2 Disaster Recovery Procedures

## RTO/RPO Targets
- Recovery Time Objective (RTO): 4 hours
- Recovery Point Objective (RPO): 1 hour

## Backup Schedule
- Full database backup: Daily at 2 AM UTC
- Incremental backups: Every 4 hours
- Configuration backups: Daily
- Code repositories: Continuous (Git)

## Recovery Procedures

### 1. Database Recovery
```bash
# Stop all services writing to database
kubectl scale deployment --replicas=0 -n sacra2-production sacra-backend
kubectl scale deployment --replicas=0 -n sacra2-production celery-workers

# Download latest backup
aws s3 cp s3://sacra2-backups/mysql/latest.sql.gz /tmp/

# Restore database
gunzip < /tmp/latest.sql.gz | mysql -h mysql-primary -u root -p sacra2

# Restart services
kubectl scale deployment --replicas=5 -n sacra2-production sacra-backend
kubectl scale deployment --replicas=10 -n sacra2-production celery-workers
```

### 2. Full Infrastructure Recovery
```bash
# 1. Provision new Kubernetes cluster
terraform apply -var="environment=disaster-recovery"

# 2. Deploy base infrastructure
kubectl apply -f k8s/infrastructure/

# 3. Restore database
./scripts/restore-database.sh

# 4. Deploy applications
kubectl apply -f k8s/applications/

# 5. Update DNS records
# Point sacra2.com to new load balancer IP

# 6. Verify functionality
./scripts/smoke-tests.sh
```

## Communication Plan
1. Incident detection: Automated alerts
2. Team notification: PagerDuty → Slack → Email
3. Status page updates: Every 30 minutes
4. Customer communication: Within 1 hour
```

### Performance Monitoring

#### 1. Grafana Dashboards

```json
{
  "dashboard": {
    "title": "SACRA2 Application Metrics",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "sum(rate(sacra2_requests_total[5m])) by (endpoint)",
            "legendFormat": "{{endpoint}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, sum(rate(sacra2_request_duration_seconds_bucket[5m])) by (le, endpoint))",
            "legendFormat": "95th percentile - {{endpoint}}"
          }
        ]
      },
      {
        "title": "Active Evaluations",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(sacra2_active_evaluations)",
            "legendFormat": "Total Active"
          }
        ]
      },
      {
        "title": "Cache Hit Rate",
        "type": "gauge",
        "targets": [
          {
            "expr": "sacra2_cache_hit_rate",
            "legendFormat": "{{cache_type}}"
          }
        ]
      }
    ]
  }
}
```

#### 2. Alerting Rules

```yaml
# alerts/prometheus-rules.yaml
groups:
- name: sacra2.rules
  rules:
  - alert: HighErrorRate
    expr: |
      (
        sum(rate(sacra2_requests_total{status=~"5.."}[5m])) /
        sum(rate(sacra2_requests_total[5m]))
      ) > 0.05
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value | humanizePercentage }}"

  - alert: HighResponseTime
    expr: |
      histogram_quantile(0.95,
        sum(rate(sacra2_request_duration_seconds_bucket[5m])) by (le)
      ) > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      description: "95th percentile response time is {{ $value }}s"

  - alert: DatabaseConnectionFailed
    expr: up{job="mysql"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Database connection failed"
      description: "MySQL database is not responding"

  - alert: NoActiveWorkers
    expr: sacra2_celery_workers == 0
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "No active Celery workers"
      description: "All background workers are down"

  - alert: LowCacheHitRate
    expr: sacra2_cache_hit_rate < 0.5
    for: 10m
    labels:
      severity: info
    annotations:
      summary: "Low cache hit rate"
      description: "Cache hit rate is {{ $value | humanizePercentage }}"
```

---

## Conclusion

This comprehensive architecture design for SACRA2 provides a robust, scalable, and secure platform for AI model evaluation at enterprise scale. The solution addresses all critical requirements:

### Key Architectural Strengths

1. **Scalability**: Horizontal scaling across all tiers with intelligent caching
2. **Security**: Multi-layer security with OAuth2, RBAC, and data encryption
3. **Performance**: Optimized database design with intelligent probe result caching
4. **Reliability**: Comprehensive monitoring, health checks, and disaster recovery
5. **Maintainability**: Clean architecture patterns with clear separation of concerns

### Implementation Success Factors

- **Phased Approach**: Incremental delivery with validated milestones
- **Modern Stack**: Proven technologies with strong community support
- **DevOps Integration**: Automated testing, deployment, and monitoring
- **Documentation**: Comprehensive technical documentation for long-term maintenance

### Next Steps

1. **Phase 1 Kickoff**: Establish development environment and core team
2. **Architecture Review**: Validate design decisions with stakeholders
3. **Proof of Concept**: Build minimal viable product to validate approach
4. **Team Onboarding**: Train development team on architecture patterns

The SACRA2 platform is positioned to become a leading solution for AI model evaluation, with architecture designed for long-term evolution and enterprise adoption.