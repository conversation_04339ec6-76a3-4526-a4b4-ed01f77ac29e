# SACRA2 Model Proposal

## Overview

The SACRA2 (Security, Alignment, Confidentiality, and Rule Adherence) model is a comprehensive data architecture designed for multi-tenant LLM evaluation and security assessment platforms. This model serves as the foundational database schema for building scalable, secure, and flexible AI evaluation systems.

### Key Features

- **Multi-tenant Architecture**: Complete tenant isolation with project-level granularity
- **Flexible Provider Integration**: Support for multiple LLM providers with configurable rate limits
- **Hierarchical Evaluation Framework**: PROBE → PROBESET → CAPABILITY → ASSESSMENT flow
- **Comprehensive RBAC**: Role-based access control at tenant and project levels
- **Audit Trail**: Complete authentication and evaluation execution tracking
- **Rate Limit Management**: Provider-level concurrency and model-level RPM/TPM controls

### Design Principles

1. **Separation of Concerns**: Clear boundaries between authentication, authorization, evaluation logic, and execution
2. **Tenant Isolation**: Complete data separation between organizations
3. **Flexibility**: Configurable evaluation frameworks and provider integrations
4. **Scalability**: Designed for high-volume evaluation workloads
5. **Security**: Comprehensive audit trails and secure credential management
6. **Extensibility**: Easy addition of new providers, evaluation methods, and capabilities

## General Model

```mermaid {align: "center", codeblock: true}
erDiagram

    %% =========================
    %% Models & Providers
    %% =========================
    DRIVERS {
        string id PK
        string name
        string type
        %% NOTES: Driver type (litellm, openai, openrouter, vertex, bedrock, etc.)
    }
    PROVIDERS {
        string id PK
        string name
        string url
        string api_key
        int    max_concurrent_requests
        string driver_id FK
        %% FK: driver_id -> DRIVERS.id
        %% NOTES: Provider-level API key (optional); tenant API key via TENANT_PROVIDER_CONFIG when present. Provider-level maximum concurrent requests
    }
    TENANT_PROVIDER_CONFIG {
        string id PK
        string tenant_id FK
        string provider_id FK
        string api_key
        int    max_concurrent_requests
        boolean enabled
        %% FK: tenant_id -> TENANTS.id ; provider_id -> PROVIDERS.id
        %% UNIQUE: (tenant_id, provider_id)  -- 1 config per tenant/provider pair
        %% NOTES: Tenant-scoped API keys; optional override for provider max concurrent; RPM/TPM at model level
    }
    LLM_MODELS {
        string id PK
        string name
        string version
        string provider_id FK
        int    request_per_minute
        int    tokens_per_minute
        %% FK: provider_id -> PROVIDERS.id
        %% UNIQUE: (provider_id, name, version)  -- optional but recommended
        %% NOTES: Model-specific rate limits (RPM/TPM)
    }
    MODEL_DEFAULT_PARAMS {
        string id PK
        string model_id FK
        string key
        string value
        %% FK: model_id -> LLM_MODELS.id
        %% NOTES: Default K/V for the model (temperature, top_p, safety, etc.)
    }

    %% =========================
    %% Organization / Projects
    %% =========================
    TENANTS {
        string id PK
        string name
        %% NOTES: Logical organization/workspace
    }
    PROJECTS {
        string id PK
        string tenant_id FK
        string name
        %% FK: tenant_id -> TENANTS.id
    }

    %% =========================
    %% Users & Authentication
    %% =========================
    USERS {
        string id PK
        string email
        boolean email_verified
        string display_name
        string picture_url
        string locale
        string timezone
        string status
        datetime created_at
        datetime last_login_at
        %% UNIQUE: email
        %% CHECK: status in ('active','blocked','invited')
        %% NOTES: Email is the anchor to link local + federated identities
    }
    PASSWORD_CREDENTIALS {
        string id PK
        string user_id FK
        string password_hash
        string password_alg
        datetime password_set_at
        boolean must_reset
        %% FK: user_id -> USERS.id
        %% NOTES: Hash (argon2id/bcrypt/scrypt). 0..1 per user
    }
    AUTH_IDENTITIES {
        string id PK
        string user_id FK
        string provider
        string provider_user_id
        string email_at_provider
        string raw_profile "serialized JSON"
        datetime linked_at
        %% FK: user_id -> USERS.id
        %% UNIQUE: (provider, provider_user_id)
        %% CHECK: provider in ('google','microsoft','apple','github','custom')
        %% NOTES: Social identities (OIDC/OAuth). Linked by email or explicit flow
    }
    AUTH_FACTORS {
        string id PK
        string user_id FK
        string type
        string label
        string secret_or_public
        string aaguid
        int    sign_count
        datetime created_at
        datetime last_used_at
        boolean disabled
        %% FK: user_id -> USERS.id
        %% CHECK: type in ('totp','webauthn','recovery')
        %% NOTES: MFA; AAGUID/sign_count for WebAuthn
    }
    LOGIN_EVENTS {
        string id PK
        string user_id FK
        string identity_id FK
        string provider
        string ip
        string user_agent
        boolean success
        string failure_reason
        datetime at
        %% FK: user_id -> USERS.id ; identity_id -> AUTH_IDENTITIES.id
        %% NOTES: Audit/traceability of logins
    }

    %% =========================
    %% RBAC (Multi-tenant / Project)
    %% =========================
    GROUPS {
        string id PK
        string tenant_id FK
        string name
        string description
        %% FK: tenant_id -> TENANTS.id
    }
    ROLES {
        string id PK
        string tenant_id FK
        string name
        string description
        %% FK: tenant_id -> TENANTS.id
    }
    TENANT_USERS {
        string id PK
        string tenant_id FK
        string user_id FK
        string status
        datetime joined_at
        %% FK: tenant_id -> TENANTS.id ; user_id -> USERS.id
        %% UNIQUE: (tenant_id, user_id)
        %% CHECK: status in ('member','owner','invited')
    }
    USER_GROUPS {
        string id PK
        string group_id FK
        string user_id FK
        %% FK: group_id -> GROUPS.id ; user_id -> USERS.id
        %% UNIQUE: (group_id, user_id)
    }
    TENANT_USER_ROLES {
        string id PK
        string tenant_id FK
        string user_id FK
        string role_id FK
        string project_id FK
        %% FK: tenant_id -> TENANTS.id ; user_id -> USERS.id ; role_id -> ROLES.id ; project_id -> PROJECTS.id (nullable)
        %% UNIQUE: (tenant_id, user_id, role_id, project_id)
        %% NOTES: If project_id = NULL → role at tenant-level
    }
    TENANT_GROUP_ROLES {
        string id PK
        string tenant_id FK
        string group_id FK
        string role_id FK
        string project_id FK
        %% FK: tenant_id -> TENANTS.id ; group_id -> GROUPS.id ; role_id -> ROLES.id ; project_id -> PROJECTS.id (nullable)
        %% UNIQUE: (tenant_id, group_id, role_id, project_id)
        %% NOTES: If project_id = NULL → role at tenant-level
    }

    %% =========================
    %% Evaluation: PROBE→SET→CAP→ASSESS
    %% =========================
    PROBE {
        string id PK
        string code
        string name
        string description
        json   params
        string version
        boolean active
        %% UNIQUE: code
        %% UNIQUE: (name, version)
        %% NOTES: Basic test (prompt/tool/regex/etc.)
    }
    PROBESET {
        string id PK
        string code
        string name
        string description
        string version
        string scoring_method "binary|avg|rule|custom"
        json   scoring_config
        %% UNIQUE: code
        %% UNIQUE: (name, version)
        %% NOTES: Set of PROBEs with a single score
    }
    PROBESET_PROBES {
        string id PK
        string probeset_id FK
        string probe_id FK
        int    order
        json   override_params
        %% FK: probeset_id -> PROBESET.id ; probe_id -> PROBE.id
        %% UNIQUE: (probeset_id, probe_id)
        %% NOTES: Order and optional overrides
    }
    CAPABILITY {
        string id PK
        string code
        string name
        string description
        %% UNIQUE: code
        %% UNIQUE: name
        %% NOTES: e.g., prompt-leaking, tool-leaking, pii-leaking
    }
    CAPABILITY_SETS {
        string id PK
        string capability_id FK
        string probeset_id FK
        float  weight
        %% FK: capability_id -> CAPABILITY.id ; probeset_id -> PROBESET.id
        %% CHECK: weight >= 0
        %% UNIQUE: (capability_id, probeset_id)
        %% NOTES: Weights normalized at runtime (e.g., sum=1)
    }
    ASSESSMENT {
        string id PK
        string code
        string name
        string description
        %% UNIQUE: code
        %% UNIQUE: name
        %% NOTES: e.g., SACRA = Security + Alignment + Confidentiality + Rules
    }
    ASSESSMENT_CAPABILITIES {
        string id PK
        string assessment_id FK
        string capability_id FK
        float  weight
        %% FK: assessment_id -> ASSESSMENT.id ; capability_id -> CAPABILITY.id
        %% CHECK: weight >= 0
        %% UNIQUE: (assessment_id, capability_id)
        %% NOTES: Weights normalized at runtime (e.g., sum=1)
    }

    %% =========================
    %% Execution & Results (Optimized)
    %% =========================
    EVALUATION_RUN {
        string id PK
        string assessment_id FK
        string project_id FK
        string name
        string status "queued|running|done|failed"
        datetime created_at
        %% FK: assessment_id -> ASSESSMENT.id ; project_id -> PROJECTS.id
        %% NOTES: Specific launch of an ASSESSMENT
    }
    RUN_MODEL {
        string id PK
        string run_id FK
        string model_id FK
        json   model_params_snapshot
        %% FK: run_id -> EVALUATION_RUN.id ; model_id -> LLM_MODELS.id
        %% UNIQUE: (run_id, model_id)
        %% NOTES: Allows evaluating multiple LLMs in the same run
    }
    RUN_PROBE_RESULT {
        string id PK
        string model_id FK
        string probe_id FK
        float  score "0..1"
        json   execution_log
        json   model_params_used
        datetime executed_at
        string status "success|failed|timeout"
        string version_hash
        %% FK: model_id -> LLM_MODELS.id ; probe_id -> PROBE.id
        %% UNIQUE: (model_id, probe_id, version_hash)
        %% NOTES: Individual probe execution result per model; cached to avoid re-running
        %% NOTES: version_hash includes probe version + model params for cache invalidation
    }
    RUN_PROBESET_RESULT {
        string id PK
        string run_model_id FK
        string probeset_id FK
        float  score "0..1"
        json   details
        datetime calculated_at
        %% FK: run_model_id -> RUN_MODEL.id ; probeset_id -> PROBESET.id
        %% UNIQUE: (run_model_id, probeset_id)
        %% NOTES: Aggregated from RUN_PROBE_RESULT using probeset scoring method
        %% NOTES: Can be calculated on-demand or cached for performance
    }
    RUN_CAPABILITY_SCORE {
        string id PK
        string run_model_id FK
        string capability_id FK
        float  score "0..1"
        datetime calculated_at
        %% FK: run_model_id -> RUN_MODEL.id ; capability_id -> CAPABILITY.id
        %% UNIQUE: (run_model_id, capability_id)
        %% NOTES: Calculated from RUN_PROBE_RESULT via probeset weights
        %% NOTES: Weighted aggregation of probe results grouped by capability
    }
    RUN_ASSESSMENT_SCORE {
        string id PK
        string run_model_id FK
        string assessment_id FK
        float  score "0..1"
        datetime calculated_at
        %% FK: run_model_id -> RUN_MODEL.id ; assessment_id -> ASSESSMENT.id
        %% UNIQUE: (run_model_id, assessment_id)
        %% NOTES: Calculated from RUN_PROBE_RESULT via capability weights (e.g., SACRA)
        %% NOTES: Top-level aggregation of all probe results for the assessment
    }

    %% =========================
    %% Global relations
    %% =========================
    DRIVERS ||--o{ PROVIDERS : "implements"
    PROVIDERS ||--o{ LLM_MODELS : "offers"
    LLM_MODELS ||--o{ MODEL_DEFAULT_PARAMS : "has default"
    TENANTS ||--o{ TENANT_PROVIDER_CONFIG : "config for"
    PROVIDERS ||--o{ TENANT_PROVIDER_CONFIG : "scoped key"

    TENANTS ||--o{ PROJECTS : "contains"

    TENANTS ||--o{ GROUPS : "has"
    TENANTS ||--o{ ROLES : "defines"
    TENANTS ||--o{ TENANT_USERS : "enrolls"
    USERS ||--o{ TENANT_USERS : "member of"
    GROUPS ||--o{ USER_GROUPS : "maps"
    USERS ||--o{ USER_GROUPS : "in"
    ROLES ||--o{ TENANT_USER_ROLES : "assigned"
    USERS ||--o{ TENANT_USER_ROLES : "has"
    TENANTS ||--o{ TENANT_USER_ROLES : "scope"
    PROJECTS ||--o{ TENANT_USER_ROLES : "project-scope"
    ROLES ||--o{ TENANT_GROUP_ROLES : "assigned"
    GROUPS ||--o{ TENANT_GROUP_ROLES : "has"
    TENANTS ||--o{ TENANT_GROUP_ROLES : "scope"
    PROJECTS ||--o{ TENANT_GROUP_ROLES : "project-scope"

    USERS ||--o{ PASSWORD_CREDENTIALS : "0..1 local"
    USERS ||--o{ AUTH_IDENTITIES : "0..n federated"
    USERS ||--o{ AUTH_FACTORS : "MFA"
    USERS ||--o{ LOGIN_EVENTS : "audit"

    PROJECTS ||--o{ EVALUATION_RUN : "runs"

    ASSESSMENT ||--o{ EVALUATION_RUN : "executed in"
    PROBESET ||--o{ PROBESET_PROBES : "contains"
    PROBE ||--o{ PROBESET_PROBES : "in"
    PROBE ||--o{ RUN_PROBE_RESULT : "executed"

    LLM_MODELS ||--o{ RUN_MODEL : "evaluated"
    LLM_MODELS ||--o{ RUN_PROBE_RESULT : "cached results"
    EVALUATION_RUN ||--o{ RUN_MODEL : "targets"
    RUN_MODEL ||--o{ RUN_PROBESET_RESULT : "scores"
    RUN_MODEL ||--o{ RUN_CAPABILITY_SCORE : "roll-up"
    RUN_MODEL ||--o{ RUN_ASSESSMENT_SCORE : "roll-up"
```

## Models & Providers

```mermaid {align: "center", codeblock: true}
erDiagram

    %% =========================
    %% Models & Providers
    %% =========================
    DRIVERS {
        string id PK
        string name
        string type
        %% NOTES: Driver type (litellm, openai, openrouter, vertex, bedrock, etc.)
    }
    PROVIDERS {
        string id PK
        string name
        string url
        string api_key
        int    max_concurrent_requests
        string driver_id FK
        %% FK: driver_id -> DRIVERS.id
        %% NOTES: Provider-level API key (optional); tenant API key via TENANT_PROVIDER_CONFIG when present. Provider-level maximum concurrent requests
    }
    TENANT_PROVIDER_CONFIG {
        string id PK
        string tenant_id FK
        string provider_id FK
        string api_key
        int    max_concurrent_requests
        boolean enabled
        %% FK: tenant_id -> TENANTS.id ; provider_id -> PROVIDERS.id
        %% UNIQUE: (tenant_id, provider_id)  -- 1 config per tenant/provider pair
        %% NOTES: Tenant-scoped API keys; optional override for provider max concurrent; RPM/TPM at model level
    }
    LLM_MODELS {
        string id PK
        string name
        string version
        string provider_id FK
        int    request_per_minute
        int    tokens_per_minute
        %% FK: provider_id -> PROVIDERS.id
        %% UNIQUE: (provider_id, name, version)  -- optional but recommended
        %% NOTES: Model-specific rate limits (RPM/TPM)
    }
    MODEL_DEFAULT_PARAMS {
        string id PK
        string model_id FK
        string key
        string value
        %% FK: model_id -> LLM_MODELS.id
        %% NOTES: Default K/V for the model (temperature, top_p, safety, etc.)
    }

    %% =========================
    %% Relations
    %% =========================
    DRIVERS ||--o{ PROVIDERS : "implements"
    PROVIDERS ||--o{ LLM_MODELS : "offers"
    LLM_MODELS ||--o{ MODEL_DEFAULT_PARAMS : "has default"
    TENANTS ||--o{ TENANT_PROVIDER_CONFIG : "config for"
    PROVIDERS ||--o{ TENANT_PROVIDER_CONFIG : "scoped key"
```

## Organization / Projects

```mermaid {align: "center", codeblock: true}
erDiagram

    %% =========================
    %% Organization / Projects
    %% =========================
    TENANTS {
        string id PK
        string name
        %% NOTES: Logical organization/workspace
    }
    PROJECTS {
        string id PK
        string tenant_id FK
        string name
        %% FK: tenant_id -> TENANTS.id
    }

    %% =========================
    %% Relations
    %% =========================
    TENANTS ||--o{ PROJECTS : "contains"
```

## Users & Authentication

```mermaid {align: "center", codeblock: true}
erDiagram

    %% =========================
    %% Users & Authentication
    %% =========================
    USERS {
        string id PK
        string email
        boolean email_verified
        string display_name
        string picture_url
        string locale
        string timezone
        string status
        datetime created_at
        datetime last_login_at
        %% UNIQUE: email
        %% CHECK: status in ('active','blocked','invited')
        %% NOTES: Email is the anchor to link local + federated identities
    }
    PASSWORD_CREDENTIALS {
        string id PK
        string user_id FK
        string password_hash
        string password_alg
        datetime password_set_at
        boolean must_reset
        %% FK: user_id -> USERS.id
        %% NOTES: Hash (argon2id/bcrypt/scrypt). 0..1 per user
    }
    AUTH_IDENTITIES {
        string id PK
        string user_id FK
        string provider
        string provider_user_id
        string email_at_provider
        string raw_profile "serialized JSON"
        datetime linked_at
        %% FK: user_id -> USERS.id
        %% UNIQUE: (provider, provider_user_id)
        %% CHECK: provider in ('google','microsoft','apple','github','custom')
        %% NOTES: Social identities (OIDC/OAuth). Linked by email or explicit flow
    }
    AUTH_FACTORS {
        string id PK
        string user_id FK
        string type
        string label
        string secret_or_public
        string aaguid
        int    sign_count
        datetime created_at
        datetime last_used_at
        boolean disabled
        %% FK: user_id -> USERS.id
        %% CHECK: type in ('totp','webauthn','recovery')
        %% NOTES: MFA; AAGUID/sign_count for WebAuthn
    }
    LOGIN_EVENTS {
        string id PK
        string user_id FK
        string identity_id FK
        string provider
        string ip
        string user_agent
        boolean success
        string failure_reason
        datetime at
        %% FK: user_id -> USERS.id ; identity_id -> AUTH_IDENTITIES.id
        %% NOTES: Audit/traceability of logins
    }

    %% =========================
    %% Relations
    %% =========================
    USERS ||--o{ PASSWORD_CREDENTIALS : "0..1 local"
    USERS ||--o{ AUTH_IDENTITIES : "0..n federated"
    USERS ||--o{ AUTH_FACTORS : "MFA"
    USERS ||--o{ LOGIN_EVENTS : "audit"
```

## RBAC multi-tenant / project

```mermaid {align: "center", codeblock: true}
erDiagram

    %% =========================
    %% RBAC (Multi-tenant / Project)
    %% =========================
    GROUPS {
        string id PK
        string tenant_id FK
        string name
        string description
        %% FK: tenant_id -> TENANTS.id
    }
    ROLES {
        string id PK
        string tenant_id FK
        string name
        string description
        %% FK: tenant_id -> TENANTS.id
    }
    TENANT_USERS {
        string id PK
        string tenant_id FK
        string user_id FK
        string status
        datetime joined_at
        %% FK: tenant_id -> TENANTS.id ; user_id -> USERS.id
        %% UNIQUE: (tenant_id, user_id)
        %% CHECK: status in ('member','owner','invited')
    }
    USER_GROUPS {
        string id PK
        string group_id FK
        string user_id FK
        %% FK: group_id -> GROUPS.id ; user_id -> USERS.id
        %% UNIQUE: (group_id, user_id)
    }
    TENANT_USER_ROLES {
        string id PK
        string tenant_id FK
        string user_id FK
        string role_id FK
        string project_id FK
        %% FK: tenant_id -> TENANTS.id ; user_id -> USERS.id ; role_id -> ROLES.id ; project_id -> PROJECTS.id (nullable)
        %% UNIQUE: (tenant_id, user_id, role_id, project_id)
        %% NOTES: If project_id = NULL → role at tenant-level
    }
    TENANT_GROUP_ROLES {
        string id PK
        string tenant_id FK
        string group_id FK
        string role_id FK
        string project_id FK
        %% FK: tenant_id -> TENANTS.id ; group_id -> GROUPS.id ; role_id -> ROLES.id ; project_id -> PROJECTS.id (nullable)
        %% UNIQUE: (tenant_id, group_id, role_id, project_id)
        %% NOTES: If project_id = NULL → role at tenant-level
    }

    %% =========================
    %% Relations
    %% =========================
    TENANTS ||--o{ GROUPS : "has"
    TENANTS ||--o{ ROLES : "defines"
    TENANTS ||--o{ TENANT_USERS : "enrolls"
    USERS ||--o{ TENANT_USERS : "member of"
    GROUPS ||--o{ USER_GROUPS : "maps"
    USERS ||--o{ USER_GROUPS : "in"
    ROLES ||--o{ TENANT_USER_ROLES : "assigned"
    USERS ||--o{ TENANT_USER_ROLES : "has"
    TENANTS ||--o{ TENANT_USER_ROLES : "scope"
    PROJECTS ||--o{ TENANT_USER_ROLES : "project-scope"
    ROLES ||--o{ TENANT_GROUP_ROLES : "assigned"
    GROUPS ||--o{ TENANT_GROUP_ROLES : "has"
    TENANTS ||--o{ TENANT_GROUP_ROLES : "scope"
    PROJECTS ||--o{ TENANT_GROUP_ROLES : "project-scope"
```

## Evaluation: PROBE→SET→CAP→ASSESS

```mermaid {align: "center", codeblock: true}
erDiagram

    %% =========================
    %% Evaluation: PROBE→SET→CAP→ASSESS
    %% =========================
    PROBE {
        string id PK
        string code
        string name
        string description
        json   params
        string version
        boolean active
        %% UNIQUE: code
        %% UNIQUE: (name, version)
        %% NOTES: Basic test (prompt/tool/regex/etc.)
    }
    PROBESET {
        string id PK
        string code
        string name
        string description
        string version
        string scoring_method "binary|avg|rule|custom"
        json   scoring_config
        %% UNIQUE: code
        %% UNIQUE: (name, version)
        %% NOTES: Set of PROBEs with a single score
    }
    PROBESET_PROBES {
        string id PK
        string probeset_id FK
        string probe_id FK
        int    order
        json   override_params
        %% FK: probeset_id -> PROBESET.id ; probe_id -> PROBE.id
        %% UNIQUE: (probeset_id, probe_id)
        %% NOTES: Order and optional overrides
    }
    CAPABILITY {
        string id PK
        string code
        string name
        string description
        %% UNIQUE: code
        %% UNIQUE: name
        %% NOTES: e.g., prompt-leaking, tool-leaking, pii-leaking
    }
    CAPABILITY_SETS {
        string id PK
        string capability_id FK
        string probeset_id FK
        float  weight
        %% FK: capability_id -> CAPABILITY.id ; probeset_id -> PROBESET.id
        %% CHECK: weight >= 0
        %% UNIQUE: (capability_id, probeset_id)
        %% NOTES: Weights normalized at runtime (e.g., sum=1)
    }
    ASSESSMENT {
        string id PK
        string code
        string name
        string description
        %% UNIQUE: code
        %% UNIQUE: name
        %% NOTES: e.g., SACRA = Security + Alignment + Confidentiality + Rules
    }
    ASSESSMENT_CAPABILITIES {
        string id PK
        string assessment_id FK
        string capability_id FK
        float  weight
        %% FK: assessment_id -> ASSESSMENT.id ; capability_id -> CAPABILITY.id
        %% CHECK: weight >= 0
        %% UNIQUE: (assessment_id, capability_id)
        %% NOTES: Weights normalized at runtime (e.g., sum=1)
    }

    %% =========================
    %% Global relations
    %% =========================
    PROBESET ||--o{ PROBESET_PROBES : "contains"
    CAPABILITY ||--o{ CAPABILITY_SETS : "weights"
    PROBESET ||--o{ CAPABILITY_SETS : "part of"
    ASSESSMENT ||--o{ ASSESSMENT_CAPABILITIES : "weights"
    CAPABILITY ||--o{ ASSESSMENT_CAPABILITIES : "part of"
```

## Execution & Results

```mermaid {align: "center", codeblock: true}
erDiagram

    %% =========================
    %% Execution & Results (Optimized)
    %% =========================
    EVALUATION_RUN {
        string id PK
        string assessment_id FK
        string project_id FK
        string name
        string status "queued|running|done|failed"
        datetime created_at
        %% FK: assessment_id -> ASSESSMENT.id ; project_id -> PROJECTS.id
        %% NOTES: Specific launch of an ASSESSMENT
    }
    RUN_MODEL {
        string id PK
        string run_id FK
        string model_id FK
        json   model_params_snapshot
        %% FK: run_id -> EVALUATION_RUN.id ; model_id -> LLM_MODELS.id
        %% UNIQUE: (run_id, model_id)
        %% NOTES: Allows evaluating multiple LLMs in the same run
    }
    RUN_PROBE_RESULT {
        string id PK
        string model_id FK
        string probe_id FK
        float  score "0..1"
        json   execution_log
        json   model_params_used
        datetime executed_at
        string status "success|failed|timeout"
        string version_hash
        %% FK: model_id -> LLM_MODELS.id ; probe_id -> PROBE.id
        %% UNIQUE: (model_id, probe_id, version_hash)
        %% NOTES: Individual probe execution result per model; cached to avoid re-running
        %% NOTES: version_hash includes probe version + model params for cache invalidation
    }
    RUN_PROBESET_RESULT {
        string id PK
        string run_model_id FK
        string probeset_id FK
        float  score "0..1"
        json   details
        datetime calculated_at
        %% FK: run_model_id -> RUN_MODEL.id ; probeset_id -> PROBESET.id
        %% UNIQUE: (run_model_id, probeset_id)
        %% NOTES: Aggregated from RUN_PROBE_RESULT using probeset scoring method
        %% NOTES: Can be calculated on-demand or cached for performance
    }
    RUN_CAPABILITY_SCORE {
        string id PK
        string run_model_id FK
        string capability_id FK
        float  score "0..1"
        datetime calculated_at
        %% FK: run_model_id -> RUN_MODEL.id ; capability_id -> CAPABILITY.id
        %% UNIQUE: (run_model_id, capability_id)
        %% NOTES: Calculated from RUN_PROBE_RESULT via probeset weights
        %% NOTES: Weighted aggregation of probe results grouped by capability
    }
    RUN_ASSESSMENT_SCORE {
        string id PK
        string run_model_id FK
        string assessment_id FK
        float  score "0..1"
        datetime calculated_at
        %% FK: run_model_id -> RUN_MODEL.id ; assessment_id -> ASSESSMENT.id
        %% UNIQUE: (run_model_id, assessment_id)
        %% NOTES: Calculated from RUN_PROBE_RESULT via capability weights (e.g., SACRA)
        %% NOTES: Top-level aggregation of all probe results for the assessment
    }

    %% =========================
    %% Relations
    %% =========================
    PROJECTS ||--o{ EVALUATION_RUN : "runs"

    ASSESSMENT ||--o{ EVALUATION_RUN : "executed in"
    PROBE ||--o{ RUN_PROBE_RESULT : "executed"

    LLM_MODELS ||--o{ RUN_MODEL : "evaluated"
    LLM_MODELS ||--o{ RUN_PROBE_RESULT : "cached results"
    EVALUATION_RUN ||--o{ RUN_MODEL : "targets"
    RUN_MODEL ||--o{ RUN_PROBESET_RESULT : "scores"
    RUN_MODEL ||--o{ RUN_CAPABILITY_SCORE : "roll-up"
    RUN_MODEL ||--o{ RUN_ASSESSMENT_SCORE : "roll-up"
```
