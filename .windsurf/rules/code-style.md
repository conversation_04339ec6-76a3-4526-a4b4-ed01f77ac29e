---
trigger: always_on
---

# Code Style Guide

This document outlines the coding standards and best practices for both Python and TypeScript code in this project.

## Python Code Style

### General Guidelines
- Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/) style guide
- Use 4 spaces for indentation (no tabs)
- Maximum line length: 88 characters (Black default)
- Use double quotes for strings (enforced by Black formatter)

### Naming Conventions
- `snake_case` for variables, functions, and methods
- `PascalCase` for class names
- `UPPER_CASE` for constants
- `_single_leading_underscore` for private members
- `__double_leading_underscore` for name mangling

### Type Hints
- Use Python type hints for all function signatures and class attributes
- Prefer `list[str]` over `List[str]` (Python 3.9+ style)
- Use `|` for union types (Python 3.10+)
- Use `Optional[Type]` or `Type | None` for optional values

### Imports
- Group imports in the following order:
  1. Standard library imports
  2. Third-party imports
  3. Local application imports
- Use absolute imports
- One import per line
- Avoid wildcard imports (`from module import *`)

### Error Handling
- Use specific exceptions instead of bare `except` clauses
- Include meaningful error messages
- Use custom exceptions for domain-specific errors
- Follow the "Easier to Ask for Forgiveness than Permission" (EAFP) principle

### Testing
- Use `pytest` for testing
- Test files should be named `test_*.py`
- Test functions should be named `test_*`
- Use fixtures for test dependencies
- Follow the Arrange-Act-Assert pattern

## TypeScript Code Style

### General Guidelines
- Follow [TypeScript ESLint](https://typescript-eslint.io/) rules
- Use 2 spaces for indentation
- Maximum line length: 100 characters
- Use single quotes for strings
- Always use semicolons
- Always include curly braces for control statements

### Naming Conventions
- `camelCase` for variables, functions, and methods
- `PascalCase` for classes, interfaces, and type aliases
- `UPPER_CASE` for constants
- `_privateField` for private class fields
- `I` prefix for interfaces (e.g., `IUser`)
- `T` prefix for generic types (e.g., `TResponse`)

### Type System
- Always explicitly define return types
- Use `interface` for public API definitions
- Use `type` for complex type operations
- Avoid `any` type; use `unknown` instead when type is uncertain
- Use `readonly` for immutable properties
- Use `const` assertions for literal types

### React Components
- Use functional components with hooks
- Define props and state using TypeScript interfaces
- Use `React.FC<Props>` or `const Component = (props: Props) => { ... }`
- Destructure props at the start of the component
- Use `useState` for local component state
- Use `useEffect` for side effects
- Memoize expensive computations with `useMemo`
- Use `useCallback` for functions passed as props

### Error Handling
- Use custom error classes for domain-specific errors
- Handle errors at the appropriate level
- Use error boundaries for React components
- Log errors appropriately

### Testing
- Use `@testing-library/react` for React component testing
- Use `jest` as the test runner
- Test files should be named `*.test.tsx` or `*.spec.tsx`
- Use `describe` blocks to group related tests
- Follow the Arrange-Act-Assert pattern
- Use `@testing-library/user-event` for user interactions

## Editor Configuration

### VS Code Recommended Extensions
- Python Extension Pack
- ESLint
- Prettier
- EditorConfig
- GitLens
- Path IntelliSense
- Import Cost
- Bracket Pair Colorizer

### Recommended Settings
```json
{
  "editor.tabSize": 2,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true,
    "source.fixAll.eslint": true
  },
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"],
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": true,
  "python.linting.mypyEnabled": true
}
```

## Pre-commit Hooks

Use the following tools to enforce code quality:

### Python
- `black` for code formatting
- `isort` for import sorting
- `flake8` for linting
- `mypy` for static type checking
- `pytest` for running tests

### TypeScript/JavaScript
- `prettier` for code formatting
- `eslint` for linting
- `typescript` for type checking
- `jest` for running tests

## Version Control

### Commit Messages
Follow [Conventional Commits](https://www.conventionalcommits.org/) specification:
- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation changes
- `style:` for formatting changes
- `refactor:` for code refactoring
- `test:` for test changes
- `chore:` for maintenance tasks

Example:
```
feat(auth): add login functionality

- Add login form component
- Implement authentication service
- Add login page tests
```

### Branch Naming
Use the following pattern:
- `feature/feature-name` for new features
- `bugfix/issue-description` for bug fixes
- `hotfix/issue-description` for critical fixes
- `chore/task-description` for maintenance tasks

## Code Review Guidelines

### General
- Keep PRs small and focused
- Include tests for new features and bug fixes
- Update documentation when necessary
- Follow the Boy Scout Rule: leave the code better than you found it

### Python-Specific
- Check for proper type hints
- Ensure proper error handling
- Look for code duplication
- Check for performance implications

### TypeScript-Specific
- Check for proper TypeScript types
- Ensure proper error boundaries
- Look for unnecessary re-renders
- Check for accessibility issues

## Performance Considerations

### Python
- Use list comprehensions and generator expressions
- Use `f-strings` for string formatting
- Use `@lru_cache` for expensive function calls
- Consider using `asyncio` for I/O-bound operations
- Use `multiprocessing` for CPU-bound operations

### TypeScript/React
- Use `React.memo` for expensive components
- Use `useMemo` for expensive calculations
- Use `useCallback` for function references
- Implement code splitting with `React.lazy`
- Use `React.memo` for pure components

## Security Best Practices

### Python
- Use parameterized queries for database access
- Sanitize all user inputs
- Use environment variables for sensitive data
- Keep dependencies updated
- Use `bandit` for security scanning

### TypeScript/React
- Sanitize all user inputs
- Use Content Security Policy (CSP)
- Implement proper CORS policies
- Use `helmet` for Express.js security
- Keep dependencies updated
- Use `npm audit` for vulnerability scanning

## Documentation

### Python
- Use Google-style docstrings
- Include type hints in docstrings
- Document public APIs thoroughly
- Include examples in docstrings

### TypeScript
- Use JSDoc for documentation
- Document all public APIs
- Include type information in documentation
- Use `@example` for code examples

## Continuous Integration

### Required Checks
- Linting
- Type checking
- Unit tests
- Integration tests
- Code coverage (minimum 80%)
- Build verification

### Deployment
- Use semantic versioning
- Follow the release process
- Update changelog
- Tag releases
- Document breaking changes
