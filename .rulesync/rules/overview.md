---
root: true
targets: ["*"]
description: "Project overview"
globs: ["**/*"]
cursorRuleType: "always"
windsurfActivationMode: "always"
tags: ["overview"]
---

## SACRA2 Project Overview

- Project: SACRA2 — Scalable AI Capability & Risk Assessment for evaluating AI agents/LLMs at enterprise scale
- Architecture: React + Vite frontend, FastAPI backend, Redis/Celery workers; DDD layers (api, application, domain, infrastructure); Dockerized
- Tech stack: FE React 18 + TypeScript + Vite + AntD; BE FastAPI + SQLAlchemy + Pydantic v2; DB Postgres; Jobs/Cache Redis; Obs Sentry/Prometheus/structlog
- Services/Modules: frontend — web UI; backend — REST API; workers — async jobs; alembic — migrations
- Evaluation Framework: PROBE → PROBESET → CAPABILITY → ASSESSMENT (hierarchical scoring)
- Data stores: Postgres (sacra2) — core entities; Redis — queues/cache; Event store — Postgres (sacra2_events)
- APIs & Integrations: REST /api/v1; JWT auth; AI providers via unified interface (configured: OpenAI, Anthropic; see docs for others)
- Environments & URLs: dev backend http://localhost:8000; frontend http://localhost:5173
- Run: frontend pnpm install && pnpm dev; backend uv pip install -e . && uv run python main.py dev
- CI/CD: TBD (.github empty)
- Security & Compliance: RBAC, audit trail, JWT auth, CORS; secrets via environment variables
- Observability: Sentry (optional), Prometheus metrics (:9090), structured logs with structlog
- Key Links: docs/sacra2-comprehensive-architecture.md; docs/sacra2-execution-optimization.md; docs/sacra2-model.md
