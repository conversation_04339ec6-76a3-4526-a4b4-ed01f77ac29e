# Filesystem JSON Generator

Script para generar una estructura JSON a partir de un directorio, compatible con el formato usado en `filesystem.js` del VSCode sample.

## Características

- **Estructura jerárquica**: Genera carpetas y archivos con IDs únicos
- **Codificación Base64**: Los archivos binarios se codifican automáticamente en base64
- **Detección de tipos**: Identifica automáticamente archivos binarios vs texto
- **Extensiones**: Extrae y almacena las extensiones de archivo
- **Contenido completo**: Lee y almacena el contenido de todos los archivos

## Uso

### Uso básico
```bash
python generate_filesystem_json.py <directorio>
```

### Guardar en archivo
```bash
python generate_filesystem_json.py <directorio> -o output.json
```

### Ver en consola
```bash
python generate_filesystem_json.py <directorio> --pretty-print
```

### Opciones completas
```bash
python generate_filesystem_json.py <directorio> \
  -o output.json \
  --pretty-print \
  --no-expand-root
```

## Parámetros

- `directory`: Directorio a procesar (requerido)
- `-o, --output`: Archivo de salida JSON (opcional)
- `--pretty-print`: Mostrar JSON en consola
- `--no-expand-root`: No expandir el directorio raíz

## Estructura de salida

```json
[
  {
    "id": "1",
    "name": "mi-proyecto",
    "type": "folder",
    "isExpanded": true,
    "children": [
      {
        "id": "2",
        "name": "src",
        "type": "folder",
        "children": [
          {
            "id": "3",
            "name": "main.js",
            "type": "file",
            "extension": "js",
            "content": "console.log('Hello World');"
          },
          {
            "id": "4",
            "name": "logo.png",
            "type": "file",
            "extension": "png",
            "content": "iVBORw0KGgoAAAANSUhEUgAA...",
            "isBinary": true,
            "encoding": "base64"
          }
        ]
      }
    ]
  }
]
```

## Archivos binarios

Los siguientes tipos de archivo se codifican automáticamente en base64:

- **Imágenes**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`, `.ico`, `.svg`
- **Audio/Video**: `.mp3`, `.mp4`, `.avi`, `.mov`, `.wav`, `.flac`
- **Documentos**: `.pdf`, `.doc`, `.docx`, `.xls`, `.xlsx`, `.ppt`, `.pptx`
- **Comprimidos**: `.zip`, `.tar`, `.gz`, `.rar`, `.7z`
- **Fuentes**: `.woff`, `.woff2`, `.ttf`, `.otf`, `.eot`
- **Ejecutables**: `.exe`, `.dll`, `.so`, `.dylib`, `.bin`, `.dat`

## Ejemplos

### Generar JSON del directorio actual
```bash
python generate_filesystem_json.py . -o filesystem.json
```

### Procesar directorio específico
```bash
python generate_filesystem_json.py /path/to/project --pretty-print
```

### Uso en Python
```python
from generate_filesystem_json import FilesystemGenerator

generator = FilesystemGenerator()
json_str = generator.generate_json('/path/to/directory')
print(json_str)
```

## Notas

- Los archivos y directorios ocultos (que empiezan con `.`) se omiten
- Los directorios se ordenan antes que los archivos
- Ambos se ordenan alfabéticamente
- Los errores de lectura se capturan y se muestran como contenido de error
- Compatible con Python 3.6+
