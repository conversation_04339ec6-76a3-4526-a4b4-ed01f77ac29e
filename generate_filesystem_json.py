#!/usr/bin/env python3
"""
<PERSON>ript to generate a JSON structure from a directory tree.
Similar to the filesystem.js structure used in the VSCode sample.
Binary files are encoded in base64.
"""

import json
import os
import base64
import mimetypes
from pathlib import Path
from typing import Dict, List, Any, Optional


class FilesystemGenerator:
    """Generates a JSON structure from a directory tree."""
    
    def __init__(self):
        self.id_counter = 1
        
    def _get_next_id(self) -> str:
        """Generate the next unique ID."""
        current_id = str(self.id_counter)
        self.id_counter += 1
        return current_id
    
    def _is_binary_file(self, file_path: Path) -> bool:
        """Check if a file is binary based on MIME type."""
        # Common text file extensions - these should NEVER be treated as binary
        text_extensions = {
            '.txt', '.md', '.json', '.yaml', '.yml', '.xml', '.html', '.htm',
            '.css', '.js', '.ts', '.tsx', '.jsx', '.py', '.java', '.c', '.cpp',
            '.h', '.hpp', '.cs', '.php', '.rb', '.go', '.rs', '.sh', '.bat',
            '.sql', '.ini', '.cfg', '.conf', '.log', '.csv', '.tsv', '.rtf'
        }
        
        # If it's a known text extension, it's definitely not binary
        if file_path.suffix.lower() in text_extensions:
            return False
        
        # Common binary file extensions
        binary_extensions = {
            '.exe', '.dll', '.so', '.dylib', '.bin', '.dat',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico',
            '.mp3', '.mp4', '.avi', '.mov', '.wav', '.flac',
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
            '.zip', '.tar', '.gz', '.rar', '.7z',
            '.woff', '.woff2', '.ttf', '.otf', '.eot'
        }
        
        if file_path.suffix.lower() in binary_extensions:
            return True
        
        # For unknown extensions, try to read and detect
        try:
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                # Check for null bytes which indicate binary content
                return b'\x00' in chunk
        except (IOError, OSError):
            return True
    
    def _read_file_content(self, file_path: Path) -> str:
        """Read file content, encoding binary files in base64."""
        try:
            if self._is_binary_file(file_path):
                with open(file_path, 'rb') as f:
                    content = f.read()
                    return base64.b64encode(content).decode('utf-8')
            else:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    return f.read()
        except (IOError, OSError, UnicodeDecodeError) as e:
            return f"Error reading file: {str(e)}"
    
    def _get_file_extension(self, file_path: Path) -> str:
        """Get file extension without the dot."""
        return file_path.suffix[1:] if file_path.suffix else ''
    
    def _should_ignore(self, entry: Path) -> bool:
        """Check if a file or directory should be ignored."""
        name = entry.name
        
        # Skip hidden files/directories
        if name.startswith('.'):
            return True
            
        # Skip specific directories
        if entry.is_dir() and name in {'__pycache__', 'results'}:
            return True
            
        # Skip log files
        if entry.is_file() and name.endswith('.log'):
            return True
            
        return False
    
    def _process_directory(self, dir_path: Path, is_expanded: bool = False) -> Dict[str, Any]:
        """Process a directory and return its JSON structure."""
        children = []
        
        try:
            # Sort entries: directories first, then files, both alphabetically
            entries = sorted(dir_path.iterdir(), key=lambda x: (x.is_file(), x.name.lower()))
            
            for entry in entries:
                if self._should_ignore(entry):
                    continue
                    
                if entry.is_dir():
                    child = self._process_directory(entry, is_expanded=False)
                    children.append(child)
                elif entry.is_file():
                    child = {
                        'id': self._get_next_id(),
                        'name': entry.name,
                        'type': 'file',
                        'extension': self._get_file_extension(entry),
                        'content': self._read_file_content(entry)
                    }
                    
                    # Add binary flag if it's a binary file
                    if self._is_binary_file(entry):
                        child['isBinary'] = True
                        child['encoding'] = 'base64'
                    
                    children.append(child)
                    
        except (IOError, OSError) as e:
            print(f"Warning: Could not read directory {dir_path}: {e}")
        
        folder_data = {
            'id': self._get_next_id(),
            'name': dir_path.name,
            'type': 'folder',
            'children': children
        }
        
        if is_expanded:
            folder_data['isExpanded'] = True
            
        return folder_data
    
    def generate_json(self, root_path: str, output_file: Optional[str] = None, 
                     expand_root: bool = True) -> str:
        """
        Generate JSON structure from a directory.
        
        Args:
            root_path: Path to the root directory
            output_file: Optional output file path
            expand_root: Whether to expand the root directory
            
        Returns:
            JSON string representation
        """
        root = Path(root_path)
        
        if not root.exists():
            raise FileNotFoundError(f"Directory not found: {root_path}")
        
        if not root.is_dir():
            raise NotADirectoryError(f"Path is not a directory: {root_path}")
        
        # Reset ID counter
        self.id_counter = 1
        
        # Generate the structure
        if root.name == '.':
            # If root is current directory, use parent directory name
            root_name = root.resolve().name
        else:
            root_name = root.name
            
        structure = [self._process_directory(root, is_expanded=expand_root)]
        
        # Convert to JSON
        json_str = json.dumps(structure, indent=2, ensure_ascii=False)
        
        # Save to file if specified
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_str)
            print(f"JSON structure saved to: {output_file}")
        
        return json_str


def main():
    """Main function to run the script from command line."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Generate JSON structure from directory tree"
    )
    parser.add_argument(
        'directory',
        help='Directory path to process'
    )
    parser.add_argument(
        '-o', '--output',
        help='Output JSON file path (optional)'
    )
    parser.add_argument(
        '--no-expand-root',
        action='store_true',
        help='Do not expand the root directory'
    )
    parser.add_argument(
        '--pretty-print',
        action='store_true',
        help='Print the JSON to stdout'
    )
    
    args = parser.parse_args()
    
    try:
        generator = FilesystemGenerator()
        json_str = generator.generate_json(
            args.directory,
            args.output,
            expand_root=not args.no_expand_root
        )
        
        if args.pretty_print or not args.output:
            print(json_str)
            
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
